import { AuthSite } from '@common/types/setting';
import * as linkify from 'linkifyjs';
import normalizeUrl, { Options } from 'normalize-url';

import { REDIRECT_HOSTS } from '@/constants/setting';

// 辅助函数，提取有效URL
export const extractFirstValidUrl = (text: string) => {
  // 使用linkify提取url，不提取邮箱
  const links = linkify.find(text);
  // 过滤掉邮箱
  const filteredLinks = links.filter((link) => link.type !== 'email');
  console.log(filteredLinks);
  return filteredLinks;
};

// 添加一个处理 host 输入的函数
export const processHostInput = (input: string) => {
  try {
    // 如果输入以协议开头，尝试解析 URL
    if (input.match(/^https?:\/\//i)) {
      const url = new URL(input);
      return url.hostname;
    }
    return input;
  } catch {
    return input;
  }
};

const DEFAULT_NORMALIZE_OPTIONS: Options = {
  defaultProtocol: 'https', // 默认添加 https
  stripWWW: true, // 移除 www 前缀
  removeTrailingSlash: true, // 移除末尾斜杠
};

// 添加自定义网站
export const validateCustomSite = async (
  customSiteUrl: string,
  authSites: AuthSite[],
) => {
  let processedUrl = customSiteUrl.trim();

  const hasTld = /\.[a-z]{2,}$/i.test(processedUrl);
  if (!hasTld && processedUrl !== '') {
    throw new Error('messages.validUrlPrompt');
  }

  try {
    // 使用 normalize-url 规范化 URL
    processedUrl = normalizeUrl(processedUrl, DEFAULT_NORMALIZE_OPTIONS);
  } catch {
    throw new Error('messages.validUrlPrompt');
  }

  const url = new URL(processedUrl);

  // 处理特殊域名
  for (const willBeCorrect of REDIRECT_HOSTS) {
    if (willBeCorrect.hostname === url.hostname) {
      url.hostname = willBeCorrect.redirect;
      processedUrl = url.toString();
      break;
    }
  }

  // 检查是否重复
  const normalizedSites = new Set(
    authSites.map(({ url }) => normalizeUrl(url, DEFAULT_NORMALIZE_OPTIONS)),
  );

  if (normalizedSites.has(processedUrl)) {
    throw new Error('messages.websiteAlreadyInList');
  }

  const newSite: AuthSite = {
    name: url.hostname,
    url: processedUrl,
    isAuthorized: false,
    authUrl: processedUrl,
    enableDelete: true,
  };

  return [...authSites, newSite];
};

// 文件名规则，只保留字母、数字、语言（不仅中文）、空格、点、下划线
// export const getValidFileName = (title: string) =>
//   title
//     .replace(/[<>:"/\\|?*]/g, '') // 除Windows不允许的字符
//     .replace(/[^a-zA-Z0-9\u0080-\uFFFF\s.-]/g, '') // 将其他特殊字符替换为下划线
//     .replace(/\s+/g, ' ') // 将多个空格替换为单个空格
//     .trim() // 移除首尾空格
//     .substring(0, 200); // 限制文件长度

// export const createStartTaskFactory = (
//   taskId: string,
//   tempFilename: string,
//   finalFilename: string,
//   info: VideoInfo,
//   format: FormatBO,
//   quality: QualityBo,
// ): StoredDownloadTask => ({
//   id: taskId, // 使用相同的 taskId
//   tempFilename,
//   finalFilename,
//   title: info.title,
//   url: info.webpage_url || taskId,
//   format,
//   quality,
//   status: DOWNLOAD_STATUS_ENUM.Pending,
//   progress: 0,
//   command: [],
//   videoInfo: info,
//   speed: '',
//   eta: '',
//   downloadSize: '0',
//   thumbnail: info.thumbnail,
//   thumbnailHeaders: info.thumbnailHeaders,
//   finalFilePath: '',
//   createdAt: Date.now(),
//   updatedAt: Date.now(),
// });
const units = ['B', 'KB', 'MB', 'GB', 'TB'];

export const calcDownloadByteVo = (byte: number, suffix: string = '') => {
  let size = byte;
  let unitIndex = 0;

  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }

  return `${size.toFixed(2)} ${units[unitIndex]}${suffix}`;
};
