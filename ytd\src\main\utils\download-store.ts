import { DOWNLOAD_STATUS_ENUM } from '@common/constants/download';
import { StoredDownloadTask, VideoInfo } from '@common/types/download';
import { Downloader } from '@main/utils/downloader';
import * as Sentry from '@sentry/electron/main';
import { app } from 'electron';
import * as fs from 'fs';
import * as path from 'path';

export class DownloadStore {
  private static instance: DownloadStore;
  private storePath: string;
  private tasks: Map<string, StoredDownloadTask>;

  private constructor() {
    this.storePath = path.join(app.getPath('userData'), 'downloads.json');
    this.tasks = new Map();
    this.loadTasks();
  }

  public static getInstance(): DownloadStore {
    if (!DownloadStore.instance) {
      DownloadStore.instance = new DownloadStore();
    }
    return DownloadStore.instance;
  }

  private async loadTasks(): Promise<void> {
    try {
      try {
        await fs.promises.access(this.storePath);
        const data = await fs.promises.readFile(this.storePath, 'utf8');
        const tasks = JSON.parse(data);
        this.tasks = new Map(Object.entries(tasks));
      } catch {
        // 文件不存在，使用空的任务列表
        console.log('下载任务文件不存在，使用空任务列表');
      }
    } catch (error) {
      console.error('加载下载任务失败:', error);
      Sentry.captureException('加载下载任务失败:' + error);
    }
  }

  private async saveTasks(): Promise<void> {
    try {
      const tasksObject = Object.fromEntries(this.tasks);
      await fs.promises.writeFile(
        this.storePath,
        JSON.stringify(tasksObject, null, 2),
      );
    } catch (error) {
      console.error('保存下载任务失败:', error);
      Sentry.captureException('保存下载任务失败:' + error);
    }
  }

  public async addTask(
    taskId: string,
    url: string,
    tempFilename: string,
    finalFilename: string,
    title: string,
    format: string,
    quality: string,
    status: string,
    videoInfo: VideoInfo & {
      thumbnail?: string;
      thumbnailHeaders?: HeadersInit;
    },
  ): Promise<void> {
    // 写入本地
    console.log('videoInfo写入');
    try {
      const settings = await Downloader.loadSettings();
      const task: StoredDownloadTask = {
        id: taskId,
        url,
        tempFilename,
        finalFilename,
        finalFilePath: path.join(settings.defaultDownloadPath, finalFilename),
        format,
        quality,
        status: status as DOWNLOAD_STATUS_ENUM,
        progress: 0,
        videoInfo: {
          ...videoInfo,
          duration: videoInfo.duration || 0,
          formats: videoInfo.formats || [],
          description: videoInfo.description || '',
          uploader: videoInfo.uploader || '',
          title: title,
          id: videoInfo.id || taskId,
          thumbnail: videoInfo.thumbnail || '',
          thumbnailHeaders: videoInfo.thumbnailHeaders,
        },
        createdAt: Date.now(),
        updatedAt: Date.now(),
        thumbnail: videoInfo.thumbnail || '',
        thumbnailHeaders: videoInfo.thumbnailHeaders,
      };

      this.tasks.set(taskId, task);
      await this.saveTasks();
    } catch (err) {
      console.error('添加任务失败:', err);
      Sentry.captureException('添加任务失败:' + err);
      throw err;
    }
  }

  public async updateTask(
    taskId: string,
    updates: Partial<StoredDownloadTask>,
  ): Promise<void> {
    const task = this.tasks.get(taskId);
    if (task) {
      this.tasks.set(taskId, {
        ...task,
        ...updates,
        updatedAt: Date.now(),
      });
      await this.saveTasks();
    }
  }

  public getTask(taskId: string): StoredDownloadTask | undefined {
    return this.tasks.get(taskId);
  }

  public getAllTasks(): StoredDownloadTask[] {
    return Array.from(this.tasks.values()).sort(
      (a, b) => b.createdAt - a.createdAt,
    );
  }

  public async removeTask(taskId: string): Promise<void> {
    const task = this.tasks.get(taskId);
    if (task?.thumbnailCache) {
      try {
        await fs.promises.unlink(task.thumbnailCache);
      } catch (err) {
        console.error('删除缓存封面图失败:', err);
        Sentry.captureException('删除缓存封面图失败:' + err);
      }
    }
    this.tasks.delete(taskId);
    await this.saveTasks();
  }

  public getTaskById(taskId: string): StoredDownloadTask | undefined {
    return this.tasks.get(taskId);
  }

  public async clearJsonTasks(taskId: string): Promise<void> {
    try {
      console.log('正在删除任务:', taskId);

      // 1. 从内存中删除任务
      this.tasks.delete(taskId);

      // 2. 从本地文件中删除任务
      try {
        await fs.promises.access(this.storePath);
        const data = await fs.promises.readFile(this.storePath, 'utf8');
        const jsonTasks = JSON.parse(data);
        if (jsonTasks[taskId]) {
          delete jsonTasks[taskId];
          await fs.promises.writeFile(
            this.storePath,
            JSON.stringify(jsonTasks, null, 2),
          );
          console.log('成功删除本地任务记录:', taskId);
        }
      } catch {
        // 文件不存在，无需处理
      }

      // 3. 删除临时文件夹
      const settings = await Downloader.loadSettings();
      const appName = app.getName();
      const tempDir = path.join(
        settings.defaultDownloadPath,
        '.' + appName,
        taskId,
      );

      if (
        await fs.promises
          .access(tempDir)
          .then(() => true)
          .catch(() => false)
      ) {
        try {
          // 使用 rm 替代 rmdir，并添加 force 选项
          await fs.promises.rm(tempDir, { recursive: true, force: true });
          console.log('成功删除临时目录:', tempDir);
        } catch (error) {
          console.error('删除临时目录失败:', error);
          Sentry.captureException('删除临时目录失败:' + error);
          // 即使删除失败也继续执行，不影响任务删除流程
        }
      }
    } catch (error) {
      console.error('删除任务失败:', error);
      Sentry.captureException('删除任务失败:' + error);
      throw error;
    }
  }
}
