import { ReactElement } from 'react';
import { useMatch, useNavigate, useResolvedPath } from 'react-router-dom';

import { ApplicationSideItem } from '@/client/application-compose';

interface LayoutItemProps {
  children: string;
  to: string;
  icon: ReactElement;
}
function LayoutItem({ children, icon, to }: LayoutItemProps) {
  const navigate = useNavigate();

  const resolved = useResolvedPath(to);
  const match = useMatch({ path: resolved.pathname, end: true });

  return (
    <ApplicationSideItem
      text={children}
      icon={icon}
      active={!!match}
      onClick={() => navigate(to)}
    />
  );
}

export default LayoutItem;
