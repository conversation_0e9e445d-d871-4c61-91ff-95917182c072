{"download": {"pasteLink": "Coller le lien", "pasteLinkFromClipboard": "Coller le lien depuis le presse-papiers", "playlistChannel": "Playlist/Cha<PERSON>ne", "pastePlaylistChannelLink": "Coller le lien de la playlist/chaîne", "download": "Télécharger", "resourceType": {"video": "Vidéo", "audio": "Audio"}, "quality": "Qualité", "videoQuality": {"best": "<PERSON><PERSON><PERSON>"}, "audioQuality": {"highest": "Maximale"}, "format": "Format", "for": "Pour", "thumbnail": "Miniature", "subtitles": "Sous-titres", "audioTracks": "Pistes audio", "allTracks": "Toutes les pistes", "default": "<PERSON><PERSON> <PERSON><PERSON>", "none": "Aucune", "modal": {"selectAll": "<PERSON><PERSON>", "cancelAll": "<PERSON><PERSON> annuler", "cancel": "Annuler", "download": "Télécharger", "needDownloadToSelect": "Veuillez sélectionner au moins un élément à télécharger"}, "emptyState": {"step1": "Étape 1 : Copiez l'URL de la vidéo", "step2": "Étape 2 : Cliquez pour coller le lien et télécharger"}, "popconfirm": {"downloadingDeleteTitle": "Êtes-vous sûr de vouloir supprimer cette tâche ?", "deleteText": "<PERSON><PERSON><PERSON><PERSON>", "openFileFailed": "Échec de l'ouverture du fichier, voulez-vous le supprimer ?"}}, "taskStatus": {"retrievingInformation": "Récupération des informations", "downloading": "Téléchargement en cours", "audioDownloading": "Téléchargement de l'audio en cours", "videoDownloading": "Téléchargement de la vidéo en cours", "subtitlesDownloading": "Téléchargement des sous-titres en cours", "converting": "Conversion en cours", "merging": "Fusion en cours", "downloadFailed": "Échec du téléchargement", "parseFailed": "Échec de l'analyse", "cancelled": "<PERSON><PERSON><PERSON>", "preparingToDownload": "Préparation du téléchargement"}, "taskActions": {"retry": "<PERSON><PERSON><PERSON><PERSON>", "delete": "<PERSON><PERSON><PERSON><PERSON>", "showInFinder": "Aff<PERSON>r dans le Finder", "showInFolder": "Afficher dans le dossier", "more": "Plus", "logIn": "Se connecter", "timeLeft": "Temps restant", "speed": "Vitesse", "fileSize": "<PERSON><PERSON>"}, "auth": {"logIn": "Se connecter", "logOut": "Se déconnecter", "logInToX": "Se connecter à https://x.com", "done": "<PERSON><PERSON><PERSON><PERSON>", "cancel": "Annuler", "cancelLogin": "Annuler la connexion", "loginTo": "Se connecter à"}, "contextMenu": {"copyCaption": "Copier la légende", "copyLinkAddress": "Copier l'adresse du lien", "openInBrowser": "<PERSON><PERSON><PERSON><PERSON>r le lien dans le navigateur", "remove": "<PERSON><PERSON><PERSON><PERSON>", "removeAll": "<PERSON>ut supprimer"}, "errors": {"connectionTimeout": "Délai de connexion dépassé, veuillez vérifier votre connexion réseau", "unsupportedUrl": "URL non encore prise en charge. Bientôt disponible", "needLoginToDownload": "Connexion requise pour télécharger", "fileNotFound": "Fichier non trouvé", "folderNotFound": "Dossier non trouvé", "openFileLocationFailed": "Échec de l'ouverture de l'emplacement du fichier", "clipboardNotContainsValidUrl": "Le presse-papiers ne contient pas d'URL valide", "retryFailed": "Échec de la nouvelle tentative", "checkVersionFailed": "Échec de la vérification de la version", "notImplemented": "Pas encore implémenté", "parseError": "<PERSON><PERSON><PERSON> d'analyse", "downloadError": "Erreur de téléchargement, veuillez vérifier la connexion réseau", "mergeError": "Erreur de conversion"}, "messages": {"saveSuccess": "Enregistrement réussi", "saveFailed": "Échec de l'enregistrement", "authSuccess": "Autorisation réussie", "authFailed": "Échec de l'autorisation", "removeAuthSuccess": "Suppression de l'autorisation réussie", "removeAuthFailed": "Échec de la suppression de l'autorisation", "validUrlPrompt": "Veuillez entrer une URL valide", "websiteAlreadyInList": "Ce site web est déjà dans la liste", "defaultError": "Opération échouée"}, "dialogs": {"removeAll": {"removeAllItemsFromTheList": "Supprimer tous les éléments de la liste ?", "deleteDownloadedFiles": "Supprimer les fichiers téléchargés", "remove": "<PERSON><PERSON><PERSON><PERSON>", "cancel": "Annuler"}, "fileDeleted": {"fileHasBeenDeletedOrMoved": "Le fichier a été supprimé ou déplacé. Supprimer cet élément ?", "remove": "<PERSON><PERSON><PERSON><PERSON>", "cancel": "Annuler"}, "deleteDownloading": {"fileIsDownloading": "Le fichier est en cours de téléchargement. Le supprimer ?", "delete": "<PERSON><PERSON><PERSON><PERSON>", "cancel": "Annuler"}}, "menu": {"website": "Site web", "settings": "Paramètres"}, "settings": {"general": "Général", "saveTo": "Enregistrer dans", "changeFolderBrowser": "Changer de dossier", "language": "<PERSON><PERSON>", "system": "Système", "createSubdirectoriesForDownloadedPlaylistsAndChannels": "<PERSON><PERSON>er des sous-dossiers pour les playlists et chaînes téléchargées", "numerateFilesInPlaylistsAndChannels": "Numéroter les fichiers dans les playlists et chaînes", "embedSubtitlesInVideoFile": "Intégrer les sous-titres dans le fichier vidéo", "authorization": "Autorisation", "logOut": "Se déconnecter", "logIn": "Se connecter", "delete": "<PERSON><PERSON><PERSON><PERSON>", "addUrl": "Ajouter", "enterTheWebsiteUrl": "Entrer l'URL du site web", "authorizationPanelTips": "La connexion au site web permet de télécharger du contenu restreint par âge, du contenu d'abonnement que vous avez acheté et d'autres contenus privés.", "proxy": "Proxy", "proxyType": "Type de proxy", "httpProxy": "Proxy HTTP", "socks5Proxy": "Proxy SOCKS5", "usingSystemProxy": "Utilisation du proxy système", "notUsingProxy": "Sans utiliser de proxy", "host": "<PERSON><PERSON><PERSON>", "port": "Port", "proxyInfoMessage": {"pleaseEnterProxyHost": "Veuillez entrer l'adresse de l'hôte proxy", "pleaseEnterValidProxyHost": "<PERSON><PERSON><PERSON><PERSON> entrer une adresse d'hôte valide", "pleaseEnterProxyPort": "Veuillez entrer le port du proxy", "pleaseEnterValidProxyPort": "Veuillez entrer un numéro de port valide (1-65535)", "optional": "Optionnel"}, "login": "Identifiant", "password": "Mot de passe", "save": "Enregistrer", "about": "À propos", "version": "Version", "latestVersion": "Dernière version", "upgrade": "Mettre à jour", "message": {"loadSettingsFailed": "Échec du chargement des paramètres"}, "checkVersion": "Vérifier la version", "latestVersionAvailable": "Nouvelle version trouvée", "latestVersionNotAvailable": "Vous avez déjà la dernière version"}, "update": {"newVersionAvailable": "Nouvelle version disponible", "whatsNew": "<PERSON><PERSON><PERSON> de <PERSON>uf", "upgradeNow": "Mettre à jour maintenant", "downloading": "Téléchargement en cours...", "remindAfterDownload": "Me rappeler après le téléchargement", "newVersionReady": "La nouvelle version est prête", "installNow": "Installer maintenant", "remindLater": "Me rappeler plus tard"}, "mainMenu": {"download": "Télécharger", "online": "En ligne", "convert": "Convertir", "audioVideoMerger": "Fusion Audio Vidéo", "joinTelegramGroup": "Rejoindre le groupe Telegram", "joinDiscordCommunity": "Rejoindre la communauté Discord"}, "application": {"menu": {"download": "Téléchargement de liens", "network": "<PERSON><PERSON><PERSON>", "format": "Conversion de format", "merge": "Fusion audio et vidéo"}, "loading": "Chargement..."}, "common": {"cancel": "Annuler", "ok": "OK"}}