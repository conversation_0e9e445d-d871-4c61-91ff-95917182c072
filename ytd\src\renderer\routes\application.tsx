import { ReactElement } from 'react';
import { FaDownload } from 'react-icons/fa6';
import { Navigate, RouteObject } from 'react-router-dom';

import ChangeSvgIcon from '@/assets/change.svg?react';
import FileCopyOutlineSvgIcon from '@/assets/file-copy-outline.svg?react';
import WebSvgIcon from '@/assets/web.svg?react';
import DownloadPage from '@/pages/Download';
import FormatPage from '@/pages/Format';
import MergePage from '@/pages/Merge';
import NetworkPage from '@/pages/Network';

type IRoute = RouteObject & { key: string; icon: ReactElement };

export const applicationMenuRouter: IRoute[] = [
  {
    key: 'application.menu.download',
    icon: <FaDownload />,
    path: '/download',
    element: <DownloadPage />,
  },
  //   {
  //     key: 'application.menu.network',
  //     icon: <WebSvgIcon />,
  //     path: '/network',
  //     element: <NetworkPage />,
  //   },
  //   {
  //     key: 'application.menu.format',
  //     icon: <ChangeSvgIcon />,
  //     path: '/format',
  //     element: <FormatPage />,
  //   },
  //   {
  //     key: 'application.menu.merge',
  //     icon: <FileCopyOutlineSvgIcon />,
  //     path: '/merge',
  //     element: <MergePage />,
  //   },
];

const routers: RouteObject[] = applicationMenuRouter
  .map(({ path, element }) => ({ path, element }))
  .concat([
    {
      path: '/',
      element: <Navigate to="/download" />,
    },
  ]);

export default routers;
