name: Build Windows App

on:
  workflow_dispatch:

jobs:
  build:
    runs-on: windows-latest

    env:
      GITHUB_TOKEN: ${{ secrets.GH_TOKEN }}

    steps:
      - name: 检出代码
        uses: actions/checkout@v3

      - name: 设置 Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'

      - name: 获取版本号
        id: package_version
        run: echo "VERSION=$(node -p "require('./package.json').version")" >> $env:GITHUB_OUTPUT

      - name: 获取当前日期
        id: date
        run: echo "DATE=$(Get-Date -Format 'yyyy-MM-dd')" >> $env:GITHUB_OUTPUT

      - name: 安装依赖
        run: |
          npm i pnpm -g
          pnpm i

      - name: 下载二进制依赖
        run: |
          node scripts/install-ytdlp.mjs
          node scripts/install-ffmpeg.mjs

      - name: 准备构建目录
        run: |
          mkdir dist -Force
          mkdir dist-electron -Force
          mkdir public/bin -Force

      - name: 构建应用
        env:
          GITHUB_TOKEN: ${{ secrets.GH_TOKEN }}
          RELEASE_NAME: 'Release ${{ steps.date.outputs.DATE }}'
        run: |
          echo "开始构建 Windows x64 版本"
          pnpm run make:win

      - name: 上传构建产物
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: windows-artifacts
          path: |
            release/*.exe
