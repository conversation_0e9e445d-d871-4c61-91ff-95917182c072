<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      http-equiv="Content-Security-Policy"
      content="default-src 'self';
             img-src * 'self' data: https: http: https://*.ytimg.com https://*.youtube.com https://*.googlevideo.com https://*.ggpht.com https://*.hdslb.com;
             media-src * blob: 'self';
             script-src 'self' 'unsafe-inline' 'unsafe-eval';
             style-src 'self' 'unsafe-inline';
             connect-src 'self' https: wss: blob: http:;
             font-src 'self' data:;
             frame-src 'self' https:;"
    />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>SnapAny</title>
  </head>

  <body>
    <div id="root"></div>
    <script type="module" src="/src/renderer/main.tsx"></script>
  </body>
</html>
