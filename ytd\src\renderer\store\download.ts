import { Setting } from '@common/types/setting';
import { create } from 'zustand';

import {
  AUDIO_FORMATS,
  AUDIO_TRACK_LANGUAGES,
  DEFAULT_BITRATE,
  DEFAULT_QUALITIES,
  FORMAT_PLATFORM_MAP,
  SUBTITLE_LANGUAGES,
  VIDEO_FORMATS,
} from '@/constants/media';
import { addOrRemoveUniqueItemOfArray } from '@/utils/array';
import { Platform } from '@/utils/environment';
import { mapSettingsToDownloadConfig } from '@/utils/setting';

export type MediaType = 'video' | 'audio';

export type Subtitle = (typeof SUBTITLE_LANGUAGES)[number]['value'];

export type AudioTrack = (typeof AUDIO_TRACK_LANGUAGES)[number]['value'];

export type Quality = (typeof DEFAULT_QUALITIES)[number]['value'] | 'best';

export type Bitrate = (typeof DEFAULT_BITRATE)[number]['value'] | 'best';

export type VideoFormat = (typeof VIDEO_FORMATS)[number]['value'];

export type AudioFormat = (typeof AUDIO_FORMATS)[number]['value'];

export interface DownloadConfigStore {
  mediaType: MediaType;
  hasCover: boolean;
  subtitles: Subtitle[] | 'none';
  audioTracks: AudioTrack[] | 'all';
  quality: Quality;
  bitrate: Bitrate;
  videoFormat: VideoFormat;
  audioFormat: AudioFormat;
  platform: Platform | 'none';
  subtitleAddOrRemove: (value: Subtitle) => void;
  audioTracksAddOrRemove: (value: AudioTrack) => void;
  changeMediaType: (type: MediaType) => void;
  updateHasCover: () => void;
  resetSubtitles: () => void;
  resetAudioTracks: () => void;
  setDefaultAudioTracks: () => void;
  changeQuality: (value: Quality) => void;
  changeBitrate: (value: Bitrate) => void;
  changePlatform: (value: Platform) => void;
  changeVideoFormat: (value: VideoFormat) => void;
  changeAudioFormat: (value: AudioFormat) => void;
  loadSetting: (setting: Setting) => void;
}

export const useDownloadStore = create<DownloadConfigStore>((set) => ({
  mediaType: 'video',
  hasCover: false,
  subtitles: 'none',
  audioTracks: 'all',
  quality: 'best',
  bitrate: 'best',
  platform: 'none',
  videoFormat: 'mp4',
  audioFormat: 'm4a',
  subtitleAddOrRemove: (value) =>
    set((state) => {
      const uniques = addOrRemoveUniqueItemOfArray(
        value,
        state.subtitles === 'none' ? [] : state.subtitles,
      );
      return { subtitles: uniques };
    }),
  audioTracksAddOrRemove: (value) =>
    set((state) => {
      const uniques = addOrRemoveUniqueItemOfArray(
        value,
        state.audioTracks === 'all' ? [] : state.audioTracks,
      );
      return { audioTracks: uniques };
    }),

  changeMediaType: (value) => set(() => ({ mediaType: value })),
  updateHasCover: () => set((state) => ({ hasCover: !state.hasCover })),
  resetAudioTracks: () => set(() => ({ subtitles: 'none' })),
  resetSubtitles: () => set(() => ({ audioTracks: 'all' })),
  setDefaultAudioTracks: () =>
    set((state) => ({
      audioTracks: Array.isArray(state.audioTracks) ? state.audioTracks : [],
    })),
  changeQuality: (value) => set(() => ({ quality: value })),
  changeBitrate: (value) => set(() => ({ bitrate: value })),
  changeVideoFormat: (value) =>
    set(() => ({ videoFormat: value, platform: 'none' })),
  changeAudioFormat: (value) =>
    set(() => ({ audioFormat: value, platform: 'none' })),
  changePlatform: (value) => {
    const videoFormat = FORMAT_PLATFORM_MAP['video'][value];
    const audioFormat = FORMAT_PLATFORM_MAP['audio'][value];

    set(() => ({ platform: value, audioFormat, videoFormat }));
  },
  loadSetting(setting) {
    const config = mapSettingsToDownloadConfig(setting);
    set(config);
  },
}));
