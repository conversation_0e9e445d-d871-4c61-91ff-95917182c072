import crypto from 'node:crypto';
import fs from 'node:fs';
import path from 'node:path';

import { net, session } from 'electron';
import mime from 'mime-types';

import TaskPool from './task-pool';

interface FileInfo {
  totalBytes: number; // 文件大小, 单位: Bytes
  extension: string; // 文件扩展名
  isSupportSharding: boolean; // 是否支持分片
}

interface Sharding {
  totalParts: number; // 分片总数
  parts: Array<number[]>; // 分片范围
}

interface DownloadProgress {
  totalBytes: number; // 文件总大小
  downloadedBytes: number; // 已下载总字节数
  speedBytes: number; // 当前下载速度(字节/秒)
}

interface MultiFileDownloadProgress {
  url: string;
  totalBytes: number;
  downloadedBytes: number;
  speedBytes: number;
}

interface MultiFileDownloadComplete {
  url: string;
  filePath: string;
}

interface MultiFileDownloadError {
  url: string;
  error: Error;
}

class FileDownloader {
  private readonly concurrentTaskCount = 8;
  private readonly maxRetryCount = 1;
  private readonly timeout = 10000;
  private readonly socketTimeout = 10000;

  constructor(
    private readonly proxyUrl = '',
    private readonly tempDir = './temp',
    private readonly headers?: Record<string, string>,
    private readonly taskId?: string,
  ) {
    // 代理配置由electron的session处理
    // 如果提供了taskId，则将tempDir设置为包含taskId的路径
    if (this.taskId) {
      this.tempDir = path.join(this.tempDir, this.taskId);
    }
  }

  private async getFileInfo(
    url: string,
    headers?: Record<string, string>,
  ): Promise<FileInfo> {
    const fileInfo: FileInfo = {
      totalBytes: 0,
      extension: '',
      isSupportSharding: false,
    };
    try {
      console.log('[getFileInfo]', url);
      const response = await new Promise<Electron.IncomingMessage>(
        (resolve, reject) => {
          const request = net.request({
            method: 'GET',
            url: url,
            headers: {
              range: 'bytes=0-0',
              ...headers,
              ...this.headers,
            },
            session: session.defaultSession,
            referrerPolicy: 'unsafe-url',
          });

          request.on('response', resolve);
          request.on('error', reject);
          request.end();
        },
      );

      const acceptRanges = response.headers['accept-ranges'];
      if (
        acceptRanges === 'bytes' ||
        (response.statusCode === 206 && response.headers['content-range'])
      ) {
        const contentRange = response.headers['content-range'];
        if (contentRange && !Array.isArray(contentRange)) {
          fileInfo.totalBytes = parseInt(contentRange.split('/')[1] ?? '0');
          if (fileInfo.totalBytes > 0) {
            fileInfo.isSupportSharding = true;
          }
        }
      }

      if (!fileInfo.isSupportSharding) {
        // 获取Content-Length
        const contentLength = response.headers['content-length'];
        if (contentLength && !Array.isArray(contentLength)) {
          fileInfo.totalBytes = parseInt(contentLength);
        }
      }

      // 尝试获取文件后缀
      const contentType = response.headers['content-type'];
      if (contentType && !Array.isArray(contentType)) {
        fileInfo.extension = mime.extension(contentType) || '';
      }

      if (fileInfo.extension === '') {
        const contentDisposition = response.headers['content-disposition'];
        if (contentDisposition && !Array.isArray(contentDisposition)) {
          fileInfo.extension =
            contentDisposition.replace(/"/g, '').split('.').pop() || '';
        }
      }

      if (fileInfo.extension === '' || fileInfo.extension === 'bin') {
        // 尝试解析url后缀
        const urlObj = new URL(url);
        fileInfo.extension =
          path.extname(urlObj.pathname).split('.').pop() || '';
      }

      // 确保响应流被正确处理
      response.on('data', () => {});
      response.on('end', () => {});
    } catch (error) {
      console.log('[fetch error]', error);
      throw new Error('获取文件信息失败' + error);
    }
    return fileInfo;
  }

  private async isFileExists(filePath: string): Promise<boolean> {
    return fs.promises
      .access(filePath)
      .then(() => true)
      .catch(() => false);
  }

  private async downloadFilePart(
    url: string,
    start: number,
    end: number,
    tempFilePath: string,
    retryCount: number = 0,
    controller?: AbortController,
    onProgress?: (bytes: number) => void,
    customHeaders?: Record<string, string>,
  ): Promise<void> {
    const writer = fs.createWriteStream(tempFilePath, { flags: 'a' }); // 使用追加模式
    let abortListener: (() => void) | undefined;

    try {
      // 判断文件是否存在
      const fileExists = await this.isFileExists(tempFilePath);
      if (!fileExists) {
        await fs.promises.writeFile(tempFilePath, '');
      }
      // 判断文件大小
      const shardingSize = end - start + 1;
      const stats = await fs.promises.stat(tempFilePath);
      const fileSize = stats.size;

      // 如果分片已完整下载,直接返回
      if (fileSize === shardingSize) {
        return;
      }
      // 文件大小小于分片大小，则下载剩余的分片
      const newStart = start + fileSize;
      // 合并默认 headers 和自定义 headers
      let headers = {
        ...this.headers,
        ...customHeaders,
      };

      if (start >= 0 && end >= 0) {
        headers = {
          ...headers,
          range: `bytes=${newStart}-${end}`,
        };
      }

      await new Promise<void>((resolve, reject) => {
        const request = net.request({
          method: 'GET',
          url: url,
          headers: headers,
          session: session.defaultSession,
          referrerPolicy: 'unsafe-url',
        });

        request.on('response', (response) => {
          if (response.statusCode !== 200 && response.statusCode !== 206) {
            reject(new Error(`下载失败，状态码: ${response.statusCode}`));
            return;
          }

          response.on('data', (chunk) => {
            try {
              writer.write(chunk);
              onProgress?.(chunk.length);
            } catch (error) {
              console.error('写入数据时发生错误:', error);
              reject(error);
            }
          });

          response.on('end', () => {
            try {
              writer.end();
              resolve();
            } catch (error) {
              console.error('结束写入流时发生错误:', error);
              reject(error);
            }
          });

          response.on('error', (error) => {
            try {
              writer.end();
              reject(error);
            } catch (endError) {
              console.error('处理响应错误时发生错误:', endError);
              reject(error);
            }
          });
        });

        request.on('error', (error) => {
          try {
            writer.end();
            reject(error);
          } catch (endError) {
            console.error('处理请求错误时发生错误:', endError);
            reject(error);
          }
        });

        if (controller) {
          abortListener = () => {
            try {
              request.abort();
              writer.end();
              reject(new Error('请求被取消'));
            } catch (error) {
              console.error('取消请求时发生错误:', error);
              reject(new Error('请求被取消'));
            }
          };
          controller.signal.addEventListener('abort', abortListener);
        }

        request.end();
      });
    } catch (error) {
      // 如果是取消操作，直接抛出错误，不进行重试
      if (error instanceof Error && error.message === '请求被取消') {
        throw error;
      }

      console.log(`[分片下载错误] 范围: ${start}-${end}`, error);
      if (retryCount < this.maxRetryCount) {
        console.log(`[重试下载] 范围: ${start}-${end}`);
        return this.downloadFilePart(
          url,
          start,
          end,
          tempFilePath,
          retryCount + 1,
          controller,
          onProgress,
          customHeaders,
        );
      }
      // 重试失败，抛出错误以终止所有任务
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      throw new Error(`分片下载失败 [${start}-${end}]: ${errorMessage}`);
    } finally {
      // 移除事件监听器
      if (controller && abortListener) {
        try {
          controller.signal.removeEventListener('abort', abortListener);
        } catch (error) {
          console.error('移除事件监听器时发生错误:', error);
        }
      }

      // 确保资源被清理
      if (writer && !writer.destroyed) {
        try {
          await new Promise<void>((resolve) => {
            writer.end(() => {
              writer.destroy();
              resolve();
            });
          });
        } catch (err) {
          console.error('关闭流时发生错误:', err);
        }
      }
    }
  }

  private getShardingCount(totalBytes: number): Sharding {
    const MB_SIZE = 1024 * 1024;
    let partSize = 0;

    if (totalBytes < 100 * MB_SIZE) {
      partSize = 5 * MB_SIZE;
    } else if (totalBytes < 200 * MB_SIZE) {
      partSize = 10 * MB_SIZE;
    } else if (totalBytes < 2000 * MB_SIZE) {
      partSize = 20 * MB_SIZE;
    } else {
      partSize = 50 * MB_SIZE;
    }

    const totalParts = Math.ceil(totalBytes / partSize);
    const parts = Array.from({ length: totalParts }, (_, i) => [
      i * partSize,
      Math.min((i + 1) * partSize - 1, totalBytes - 1),
    ]);

    return {
      totalParts,
      parts,
    };
  }

  private md5(str: string): string {
    return crypto.createHash('md5').update(str).digest('hex');
  }

  private async mergeSharding(
    partFolderName: string,
    extension: string,
    parts: Array<number[]>,
  ): Promise<void> {
    const filePath = path.join(this.tempDir, `${partFolderName}.${extension}`);
    // 创建写入流
    const writeStream = fs.createWriteStream(filePath);

    // 按顺序合并分片
    for (const part of parts) {
      const tempFilePath = path.join(
        this.tempDir,
        partFolderName,
        `${part[0]}-${part[1]}`,
      );

      try {
        // 检查分片是否存在
        const tempFileExists = await this.isFileExists(tempFilePath);
        if (tempFileExists) {
          // 创建读取流并通过管道传输到写入流
          const readStream = fs.createReadStream(tempFilePath);
          await new Promise<void>((resolve, reject) => {
            readStream.pipe(writeStream, { end: false });
            readStream.on('end', resolve);
            readStream.on('error', (err) => {
              readStream.destroy();
              reject(err);
            });
          });
        }
      } catch (error) {
        console.error(`处理分片失败: ${part[0]}-${part[1]}`, error);
        // throw new Error(`处理分片失败: ${part[0]}-${part[1]}` + error);
        // 不要立即抛出错误，继续处理其他分片
      }
    }

    // 关闭写入流
    await new Promise<void>((resolve, reject) => {
      writeStream.end();
      writeStream.on('finish', resolve);
      writeStream.on('error', reject);
    });
  }

  private async cleanupShardingFolder(folderPath: string): Promise<void> {
    try {
      // 递归删除文件夹及其内容
      await fs.promises.rm(folderPath, { recursive: true, force: true });
      console.log(`清理分片文件夹成功: ${folderPath}`);
    } catch (error) {
      console.error(`清理分片文件夹失败: ${folderPath}`, error);
      throw new Error(`清理分片文件夹失败: ${folderPath} + ${error}`);
    }
  }

  public async download(
    url: string,
    headers?: Record<string, string>,
    controller?: AbortController,
    callbacks?: {
      onProgress?: (progress: DownloadProgress) => Promise<void>;
      onComplete?: (filePath: string) => Promise<void>;
      onError?: (error: Error) => Promise<void>;
    },
  ): Promise<void> {
    let taskPool: TaskPool<string> | undefined;
    let progressTimer: NodeJS.Timeout | undefined;
    let downloadedBytes = 0;
    let currentSecondBytes = 0;
    let lastProgressTime = 0;
    let totalBytes = 0;
    let abortListener: () => void = () => {};

    if (!controller) {
      controller = new AbortController();
    }

    try {
      // 添加 abort 信号监听，并保存引用以便后续移除
      abortListener = () => {
        if (progressTimer) {
          clearInterval(progressTimer);
          progressTimer = undefined;
        }
        if (taskPool) {
          taskPool.close();
          taskPool = undefined;
        }
        // 清理所有资源
        if (callbacks?.onError) {
          callbacks.onError(new Error('下载已取消'));
        }
      };
      controller.signal.addEventListener('abort', abortListener);

      const res = await this.getFileInfo(url, headers);
      totalBytes = res.totalBytes;

      progressTimer = setInterval(() => {
        callbacks?.onProgress?.({
          totalBytes,
          downloadedBytes,
          speedBytes: currentSecondBytes,
        });
        currentSecondBytes = 0;
        lastProgressTime = Date.now();
      }, 1000);

      // 创建临时目录
      const tempDirExists = await this.isFileExists(this.tempDir);
      if (!tempDirExists) {
        await fs.promises.mkdir(this.tempDir, { recursive: true });
      }
      const partFolderName = this.md5(url);

      // 支持分片下载，使用异步池
      const tasks = [];
      if (res.isSupportSharding) {
        // 判断分片文件夹是否存在
        const partFolderPath = path.join(this.tempDir, partFolderName);
        const partFolderExists = await this.isFileExists(partFolderPath);
        if (!partFolderExists) {
          await fs.promises.mkdir(partFolderPath, { recursive: true });
        }
        const sharding = this.getShardingCount(res.totalBytes);

        // 遍历分片范围
        for (const part of sharding.parts) {
          const tempFilePath = path.join(
            partFolderPath,
            `${part[0]}-${part[1]}`,
          );
          const tempFileExists = await this.isFileExists(tempFilePath);
          if (tempFileExists) {
            const stats = await fs.promises.stat(tempFilePath);
            downloadedBytes += stats.size;
          }

          tasks.push(() =>
            this.downloadFilePart(
              url,
              part[0],
              part[1],
              tempFilePath,
              0,
              controller,
              (bytes) => {
                currentSecondBytes += bytes;
                downloadedBytes = downloadedBytes + bytes;
              },
              headers,
            ),
          );
        }

        console.log('任务开始', this.concurrentTaskCount);
        const startTime = Date.now();
        taskPool = new TaskPool<string>(this.concurrentTaskCount);

        try {
          await taskPool.runAll(tasks);

          // 清理定时器和 TaskPool
          if (progressTimer) {
            clearInterval(progressTimer);
            progressTimer = undefined;
          }
          await taskPool.close();
          taskPool = undefined;

          console.log('所有任务完成，开始合并文件');

          await this.mergeSharding(
            partFolderName,
            res.extension,
            sharding.parts,
          );
          const endTime = Date.now();
          console.log(`下载完成，耗时: ${endTime - startTime}ms`);

          // 最后一次进度更新
          callbacks?.onProgress?.({
            totalBytes,
            downloadedBytes,
            speedBytes: currentSecondBytes / (Date.now() - lastProgressTime),
          });

          await this.cleanupShardingFolder(partFolderPath);
          callbacks?.onComplete?.(
            path.join(this.tempDir, `${partFolderName}.${res.extension}`),
          );
        } catch (error) {
          // 如果任何分片下载失败，立即终止整个下载过程
          console.error('分片下载失败，终止整个下载过程');

          // 确保清理资源
          if (taskPool) {
            await taskPool.close();
            taskPool = undefined;
          }

          // 重新抛出错误，让外层 catch 处理
          throw error;
        }
      } else {
        // 不支持分片下载的情况
        const filePath = path.join(
          this.tempDir,
          `${partFolderName}.${res.extension}`,
        );

        // 删除文件
        if (await this.isFileExists(filePath)) {
          await fs.promises.unlink(filePath);
        }

        await this.downloadFilePart(
          url,
          -1,
          -1,
          filePath,
          0,
          controller,
          (bytes) => {
            currentSecondBytes += bytes;
            downloadedBytes = downloadedBytes + bytes;
          },
          headers,
        );

        // 清理定时器
        if (progressTimer) {
          clearInterval(progressTimer);
          progressTimer = undefined;
        }

        // 确保非分片下载也显示100%进度
        callbacks?.onProgress?.({
          totalBytes,
          downloadedBytes,
          speedBytes: currentSecondBytes / (Date.now() - lastProgressTime),
        });
        callbacks?.onComplete?.(filePath);
      }
    } catch (error) {
      if (progressTimer) {
        clearInterval(progressTimer);
        progressTimer = undefined;
      }
      if (taskPool) {
        await taskPool.close();
        taskPool = undefined;
      }

      const finalError =
        error instanceof Error ? error : new Error('下载失败' + error);

      if (controller.signal.aborted) {
        const abortError = new Error('下载已取消');
        callbacks?.onError?.(abortError);
        throw abortError;
      }

      callbacks?.onError?.(finalError);
      throw finalError;
    } finally {
      // 清理资源
      if (progressTimer) {
        clearInterval(progressTimer);
        progressTimer = undefined;
      }
      if (taskPool) {
        await taskPool.close();
        taskPool = undefined;
      }
      // 移除事件监听器
      controller.signal.removeEventListener('abort', abortListener);
    }
  }

  public async downloadMultiple(
    urls: {
      url: string;
      headers?: Record<string, string>;
    }[],
    downloadPath: string,
    controller?: AbortController,
    callbacks?: {
      onProgress?: (progress: MultiFileDownloadProgress[]) => Promise<void>;
      onComplete?: (results: MultiFileDownloadComplete[]) => Promise<void>;
      onError?: (errors: MultiFileDownloadError[]) => Promise<void>;
    },
  ): Promise<void> {
    const multiFileProgress = new Map<string, DownloadProgress>();
    const multiFileResults = new Map<string, string>();
    const multiFileErrors = new Map<string, Error>();
    let hasCompletedCallback = false;
    let activeDownloads = urls.length;

    // 创建下载临时目录路径
    const downloadTempDir = path.join(downloadPath, 'temp');

    if (!controller) {
      controller = new AbortController();
    }

    // 为每个下载任务创建独立的 AbortController
    const downloadControllers = new Map<string, AbortController>();

    // 监听主 controller 的 abort 事件
    const mainAbortListener = () => {
      try {
        // 当主 controller 触发 abort 时，取消所有子任务
        downloadControllers.forEach((ctrl) => {
          try {
            ctrl.abort();
          } catch (error) {
            console.error('取消下载任务时发生错误:', error);
            // 记录错误但不中断流程
          }
        });
      } catch (error) {
        console.error('主取消监听器发生错误:', error);
      }
    };
    controller.signal.addEventListener('abort', mainAbortListener);

    try {
      // 首先获取所有文件的大小信息
      await Promise.all(
        urls.map(async (urlObj) => {
          try {
            const fileInfo = await this.getFileSize(urlObj.url, urlObj.headers);
            multiFileProgress.set(urlObj.url, {
              totalBytes: fileInfo,
              downloadedBytes: 0,
              speedBytes: 0,
            });
          } catch (error) {
            const finalError =
              error instanceof Error ? error : new Error(String(error));
            multiFileErrors.set(urlObj.url, finalError);
            activeDownloads--;
          }
        }),
      );

      // 如果有初始的文件信息，触发第一次进度回调
      if (multiFileProgress.size > 0) {
        try {
          await callbacks?.onProgress?.(
            Array.from(multiFileProgress.entries()).map(([url, progress]) => ({
              url,
              ...progress,
            })),
          );
        } catch (error) {
          console.error('更新进度时发生错误:', error);
        }
      }

      // 如果在获取文件大小阶段就有错误，立即报告并终止
      if (multiFileErrors.size > 0) {
        try {
          await callbacks?.onError?.(
            Array.from(multiFileErrors.entries()).map(([url, error]) => ({
              url,
              error,
            })),
          );
        } catch (error) {
          console.error('报告初始错误时发生错误:', error);
        }
        throw new Error('获取文件大小失败');
      }

      // 为每个下载任务创建 AbortController
      urls.forEach((urlObj) => {
        const url = urlObj.url;
        if (!multiFileErrors.has(url)) {
          const taskController = new AbortController();
          downloadControllers.set(url, taskController);
        }
      });

      // 开始下载文件
      await Promise.all(
        urls.map(async (urlObj) => {
          if (multiFileErrors.has(urlObj.url)) {
            return;
          }

          try {
            const taskController = downloadControllers.get(urlObj.url)!;

            if (controller.signal.aborted) {
              taskController.abort();
              return;
            }
            console.log('下载任务开始', urlObj);

            // 创建新的 FileDownloader 实例，使用 downloadPath/temp 作为缓存目录
            const downloader = new FileDownloader(
              this.proxyUrl,
              downloadTempDir,
              this.headers,
              this.taskId,
            );

            await downloader.download(
              urlObj.url,
              urlObj?.headers,
              taskController,
              {
                onProgress: async (progress: DownloadProgress) => {
                  try {
                    const safeProgress = {
                      ...progress,
                      downloadedBytes: Math.min(
                        progress.downloadedBytes,
                        progress.totalBytes,
                      ),
                    };
                    multiFileProgress.set(urlObj.url, safeProgress);
                    await callbacks?.onProgress?.(
                      Array.from(multiFileProgress.entries()).map(
                        ([url, progress]) => ({
                          url,
                          ...progress,
                        }),
                      ),
                    );
                  } catch (error) {
                    console.error('更新进度时发生错误:', error);
                  }
                },
                onComplete: async (filePath: string) => {
                  try {
                    multiFileResults.set(urlObj.url, filePath);
                    downloadControllers.delete(urlObj.url); // 清理完成的任务的 controller
                    activeDownloads--;

                    if (activeDownloads === 0 && !hasCompletedCallback) {
                      hasCompletedCallback = true;
                      await callbacks?.onComplete?.(
                        Array.from(multiFileResults.entries()).map(
                          ([url, filePath]) => ({
                            url,
                            filePath,
                          }),
                        ),
                      );
                    }
                  } catch (error) {
                    console.error('处理下载完成时发生错误:', error);
                  }
                },
                onError: async (error: Error) => {
                  try {
                    multiFileErrors.set(urlObj.url, error);
                    downloadControllers.delete(urlObj.url); // 清理出错的任务的 controller
                    activeDownloads--;

                    // 当有错误发生时，取消所有其他下载
                    downloadControllers.forEach((ctrl) => {
                      try {
                        ctrl.abort();
                      } catch (abortError) {
                        console.error('取消下载任务时发生错误:', abortError);
                      }
                    });

                    await callbacks?.onError?.(
                      Array.from(multiFileErrors.entries()).map(
                        ([url, error]) => ({
                          url,
                          error,
                        }),
                      ),
                    );
                  } catch (callbackError) {
                    console.error('处理下载错误时发生错误:', callbackError);
                  }
                },
              },
            );
          } catch (error) {
            try {
              activeDownloads--;
              downloadControllers.delete(urlObj.url); // 清理出错的任务的 controller
              const finalError =
                error instanceof Error ? error : new Error(String(error));
              multiFileErrors.set(urlObj.url, finalError);

              // 当有错误发生时，取消所有其他下载
              downloadControllers.forEach((ctrl) => {
                try {
                  ctrl.abort();
                } catch (abortError) {
                  console.error('取消下载任务时发生错误:', abortError);
                }
              });

              await callbacks?.onError?.(
                Array.from(multiFileErrors.entries()).map(([url, error]) => ({
                  url,
                  error,
                })),
              );
            } catch (callbackError) {
              console.error('处理下载错误时发生错误:', callbackError);
            }
          }
        }),
      );
    } catch (error) {
      try {
        if (multiFileErrors.size > 0) {
          await callbacks?.onError?.(
            Array.from(multiFileErrors.entries()).map(([url, error]) => ({
              url,
              error,
            })),
          );
        }

        // 如果是取消操作，直接返回
        if (controller.signal.aborted) {
          return;
        }

        throw error;
      } catch (callbackError) {
        console.error('处理最终错误时发生错误:', callbackError);
      }
    } finally {
      // 移除事件监听器
      try {
        controller.signal.removeEventListener('abort', mainAbortListener);
      } catch (error) {
        console.error('移除事件监听器时发生错误:', error);
      }
    }
  }

  /**
   * 获取下载链接的文件大小
   * @param url 下载链接
   * @param headers 可选的请求头
   * @returns 文件大小（字节）
   */
  public async getFileSize(
    url: string,
    customHeaders?: Record<string, string>,
  ): Promise<number> {
    try {
      console.log('[getFileSize]', customHeaders);
      // 移除Referer
      // const headers = {
      //   ...customHeaders,
      // };
      // delete headers.Referer;
      const response = await new Promise<Electron.IncomingMessage>(
        (resolve, reject) => {
          const request = net.request({
            method: 'GET',
            url: url,
            headers: {
              range: 'bytes=0-0',
              ...this.headers,
              ...customHeaders,
            },
            session: session.defaultSession,
            referrerPolicy: 'unsafe-url',
          });

          request.on('response', resolve);
          request.on('error', reject);
          request.end();
        },
      );

      let fileSize = 0;

      // 首先尝试从 content-range 获取文件大小
      const contentRange = response.headers['content-range'];
      if (contentRange && !Array.isArray(contentRange)) {
        fileSize = parseInt(contentRange.split('/')[1] ?? '0');
      }

      // 如果 content-range 不存在或解析失败，尝试从 content-length 获取
      if (!fileSize) {
        const contentLength = response.headers['content-length'];
        if (contentLength && !Array.isArray(contentLength)) {
          fileSize = parseInt(contentLength);
        }
      }

      // 确保响应流被正确处理
      response.on('data', () => {});
      response.on('end', () => {});

      return fileSize;
    } catch (error) {
      throw new Error('获取文件大小失败：' + error);
    }
  }
}

export default FileDownloader;
