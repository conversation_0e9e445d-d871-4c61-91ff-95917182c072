import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import process from 'process';

import { __dirname, checkBinaryHelper, downloadFileHelper } from './helper.mjs';

const FFMPEG_URL =
  'https://github.com/descriptinc/ffmpeg-ffprobe-static/releases/download/b7.1.0-rc.1';

// ffmpeg 下载配置
const FFMPEG_VERSIONS = {
  win32: {
    x64: {
      url: `${FFMPEG_URL}/ffmpeg-win32-x64`,
      filename: 'ffmpeg.exe',
    },
  },
  darwin: {
    x64: {
      url: `${FFMPEG_URL}/ffmpeg-darwin-x64`,
      filename: 'ffmpeg-x64',
    },
    arm64: {
      url: `${FFMPEG_URL}/ffmpeg-darwin-arm64`,
      filename: 'ffmpeg-arm64',
    },
  },
};

async function main() {
  let destPath;
  try {
    console.log('📦 开始安装 ffmpeg');
    const platform = process.platform;
    const arch = process.arch;
    let fileSizes = [];

    const binDir = path.join(__dirname, '..', 'public', 'bin');
    if (!fs.existsSync(binDir)) {
      fs.mkdirSync(binDir, { recursive: true });
    }

    if (platform === 'darwin') {
      for (const [targetArch, config] of Object.entries(
        FFMPEG_VERSIONS.darwin,
      )) {
        // 如果存在且可用则不下载
        destPath = path.join(binDir, config.filename);
        if (checkBinaryHelper(destPath)) {
          console.log(`✅ ffmpeg ${targetArch} 已安装且可用`);
          // 记录每个版本的大小
          fileSizes.push({
            arch: targetArch,
            size: (fs.statSync(destPath).size / 1024 / 1024).toFixed(2),
          });
          // 如果存在则跳过
          continue;
        }
        console.log(`⏬ 正在下载 macOS ${targetArch} 版本...`);

        await downloadFileHelper(config.url, destPath);
        execSync(`chmod +x "${destPath}"`);

        // 记录每个版本的大小
        fileSizes.push({
          arch: targetArch,
          size: (fs.statSync(destPath).size / 1024 / 1024).toFixed(2),
        });

        if (targetArch === arch) {
          const linkPath = path.join(binDir, 'ffmpeg');
          if (fs.existsSync(linkPath)) {
            fs.unlinkSync(linkPath);
          }
          fs.symlinkSync(config.filename, linkPath);

          console.log(`✅ 已下载 ${targetArch} 版本的 ffmpeg`);
        }
      }
      // macOS: 显示两个版本的大小
      const sizeInfo = fileSizes
        .map((f) => `${f.arch}: ${f.size} MB`)
        .join(', ');
      console.log('✅ ffmpeg 安装成功 (' + sizeInfo + ')');
    } else {
      const config = FFMPEG_VERSIONS.win32.x64;
      destPath = path.join(binDir, config.filename);

      if (checkBinaryHelper(destPath)) {
        console.log('✅ ffmpeg 已安装且可用');
        return;
      }
      console.log('⏬ 正在下载 Windows 版本...');
      await downloadFileHelper(config.url, destPath);

      if (!checkBinaryHelper(destPath)) {
        throw new Error('下载的文件无效或不完整');
      }

      // Windows: 显示单个版本的大小
      console.log(
        '✅ ffmpeg 安装成功',
        (fs.statSync(destPath).size / 1024 / 1024).toFixed(2) + ' MB',
      );
    }

    process.exit(0);
  } catch (error) {
    console.error('❌ 安装失败:', error.message);
    // 清理下载的文件
    if (destPath && fs.existsSync(destPath)) {
      fs.unlinkSync(destPath);
    }
    process.exit(1);
  }
}

main();
