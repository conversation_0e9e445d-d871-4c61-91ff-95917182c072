const { notarize } = require('@electron/notarize');
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

async function notarizing(context) {
  const { electronPlatformName, appOutDir } = context;
  if (electronPlatformName !== 'darwin') {
    return;
  }

  const appName = context.packager.appInfo.productFilename;
  const appPath = `${appOutDir}/${appName}.app`;
  const appBundleId = context.packager.config.appId;
  const targetArch =
    context.packager.platformSpecificBuildOptions.target?.[0]?.arch?.[0] ||
    'x64';

  console.log(`📦 开始签名和公证流程 (${targetArch})`);

  // 检查二进制文件
  const ffmpegPath = path.join(
    appPath,
    'Contents/Resources/app.asar.unpacked/public/bin/ffmpeg',
  );
  const ffprobePath = path.join(
    appPath,
    'Contents/Resources/app.asar.unpacked/public/bin/ffprobe',
  );
  const ytdlpPath = path.join(
    appPath,
    'Contents/Resources/app.asar.unpacked/public/bin/yt-dlp',
  );

  const missingFiles = [];
  if (!fs.existsSync(ffmpegPath)) missingFiles.push('ffmpeg');
  if (!fs.existsSync(ffprobePath)) missingFiles.push('ffprobe');
  if (!fs.existsSync(ytdlpPath)) missingFiles.push('yt-dlp');

  if (missingFiles.length > 0) {
    throw new Error(`❌ 缺少必要的二进制文件: ${missingFiles.join(', ')}`);
  }

  // 定义签名配置
  const SIGN_KEY =
    process.env.APPLE_SIGNING_IDENTITY ||
    'A0A4EFE8A6A06D52FE631E901F7F4B3C0A1FFA03';
  const ENTITLEMENTS = path.resolve(
    __dirname,
    '../extra/entitlements.mac.plist',
  );

  // 签名二进制文件
  const signBinary = async (binaryPath, name) => {
    try {
      if (!fs.existsSync(binaryPath)) {
        throw new Error(`找不到文件: ${binaryPath}`);
      }

      console.log(`🔏 正在签名 ${name}...`);

      // 设置执行权限
      execSync(`chmod +x "${binaryPath}"`);

      // 移除现有签名
      execSync(`codesign --remove-signature "${binaryPath}"`);

      // 签名
      const signCommand = [
        'codesign',
        '--force',
        '--deep',
        '--sign',
        `"${SIGN_KEY}"`,
        '--timestamp',
        '--options',
        'runtime',
        '--entitlements',
        `"${ENTITLEMENTS}"`,
        '--verbose',
        `"${binaryPath}"`,
      ].join(' ');

      execSync(signCommand, { stdio: 'inherit' });

      // 验证签名
      execSync(`codesign --verify --deep --strict "${binaryPath}"`, {
        stdio: 'inherit',
      });
      console.log(`✅ ${name} 签名完成`);
    } catch (error) {
      console.error(`❌ 签名 ${name} 失败:`, error.message);
      throw error;
    }
  };

  try {
    // 签名二进制文件
    await signBinary(ffmpegPath, 'ffmpeg');
    await signBinary(ffprobePath, 'ffprobe');
    await signBinary(ytdlpPath, 'yt-dlp');

    // 签名整个应用
    console.log('🔏 正在签名应用程序包...');
    execSync(
      `codesign --force --deep --sign "${SIGN_KEY}" --timestamp --options runtime --entitlements "${ENTITLEMENTS}" "${appPath}"`,
      { stdio: 'inherit' },
    );
    execSync(`codesign --verify --deep --strict "${appPath}"`, {
      stdio: 'inherit',
    });
    console.log('✅ 应用程序包签名完成');

    // 开始公证
    console.log('📝 开始公证流程...');
    await notarize({
      appBundleId,
      appPath,
      appleId: '<EMAIL>',
      appleIdPassword: 'dlym-lrow-zdgj-emzk',
      teamId: '84QK9535NH',
    });
    console.log('✅ 公证成功完成！');
  } catch (error) {
    console.error('❌ 签名或公证失败:', error.message);
    throw error;
  }
}

module.exports = notarizing;
