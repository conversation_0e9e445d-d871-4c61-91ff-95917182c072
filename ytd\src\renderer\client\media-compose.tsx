import { List, Progress } from 'flowbite-react';
import { ReactNode } from 'react';

export function MediaInfoItem(props: { icon: ReactNode; content: ReactNode }) {
  return (
    <section className="flex items-center gap-1">
      {props.icon}
      {props.content}
    </section>
  );
}

export function MediaListItem(props: {
  children: ReactNode;
  progress: number;
}) {
  return (
    <div>
      <List.Item className="py-5 px-4 mt-4 ml-4 mr-4 grid grid-cols-[1fr_auto] gap-x-2 bg-white">
        {props.children}
      </List.Item>
      {props.progress > 0 && props.progress < 100 && (
        <Progress
          size="sm"
          progress={props.progress}
          color="blue"
          theme={{
            base: 'rounded-none',
            bar: 'rounded-none',
          }}
        />
      )}
    </div>
  );
}

export function MediaShowResolution(props: { width: number; height: number }) {
  return (
    <p className="inline-flex gap-1">
      <span>{props.width}</span>
      <span>∗</span>
      <span>{props.height}</span>
    </p>
  );
}
