import { TextInput } from 'flowbite-react';
import {
  FC,
  FocusEventHandler,
  ForwardedRef,
  forwardRef,
  KeyboardEventHandler,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';

export interface PathComboBoxProps {
  id?: string;
  path?: string;
  handleValidPath: (path: string) => void;
  handleOpenFolder?: () => void;
  handleChangeDownloadPath?: () => void;
  rightIcon?: FC;
  actionText: string;
  placeholder?: string;
  className?: string;
  disableBlurSubmit?: boolean;
}

export interface PathComboBoxRef {
  setShowPath: (showPath: string) => void;
  clearShowPath: () => void;
}

function PathComboBox(
  {
    id,
    handleOpenFolder,
    path = '',
    handleValidPath,
    handleChangeDownloadPath,
    actionText,
    rightIcon,
    placeholder,
    className,
    disableBlurSubmit,
  }: PathComboBoxProps,
  ref: ForwardedRef<PathComboBoxRef>,
) {
  const [showPath, setShowPath] = useState(path);

  const inputRef = useRef<HTMLInputElement>(null);
  /**
   * ! HACK
   * await https://github.com/themesberg/flowbite-react/pull/1492
   */
  const containerRef = useRef<HTMLDivElement>(null);
  useEffect(() => {
    const onRightIconClick = handleOpenFolder;
    if (!rightIcon || !onRightIconClick || !containerRef.current) {
      return;
    }

    const rightElem = containerRef.current.querySelector(
      '[data-testid="right-icon"]',
    )!;

    rightElem.classList.remove('pointer-events-none');
    rightElem.classList.add('cursor-pointer');

    const onRightClick = (e: Event) => {
      e.stopPropagation();
      onRightIconClick();
    };
    rightElem.addEventListener('click', onRightClick);

    return () => {
      rightElem.removeEventListener('click', onRightClick);
    };
  }, [handleOpenFolder, rightIcon]);

  useImperativeHandle(ref, () => ({
    setShowPath(showPath) {
      setShowPath(showPath);
    },
    clearShowPath() {
      setShowPath('');
    },
  }));

  useEffect(() => {
    setShowPath(path);
  }, [path]);

  handleChangeDownloadPath ??= () => {
    handleValidPath(showPath);
    setShowPath('');
  };

  const onBlur: FocusEventHandler<HTMLInputElement> = (e) => {
    if (path === showPath) {
      return;
    }

    handleValidPath(e.target.value);
  };

  const onKeyDown: KeyboardEventHandler<HTMLInputElement> = (e) => {
    if (e.key === 'Escape') {
      setShowPath(path);
      // 任务队列靠后，以确保只有默认行为
      setTimeout(() => {
        inputRef.current!.blur();
      }, 0);
    }

    if (e.key !== 'Enter') {
      return;
    }

    if (disableBlurSubmit) {
      handleValidPath(showPath);
      setShowPath('');
    }
    inputRef.current!.blur();
  };

  return (
    <div className={`flex ${className}`} ref={containerRef}>
      <TextInput
        ref={inputRef}
        id={id}
        className="w-[50px] grow shrink"
        theme={{
          field: {
            input: {
              withAddon: { off: 'rounded-l-lg' },
            },
          },
        }}
        rightIcon={rightIcon}
        value={showPath}
        onChange={(e) => setShowPath(e.target.value)}
        onKeyDown={onKeyDown}
        onBlur={!disableBlurSubmit ? onBlur : undefined}
        placeholder={placeholder}
      />
      <button
        className="bg-[#1F2A37] text-white px-5 py-2.5 rounded-r-xl cursor-pointer text-sm"
        onClick={handleChangeDownloadPath}
      >
        {actionText}
      </button>
    </div>
  );
}

export default forwardRef(PathComboBox);
