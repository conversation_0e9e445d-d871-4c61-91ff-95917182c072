import { compareVersions } from '@common/utils/version';
import { Button } from 'flowbite-react';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { Notice } from '@/components/Notice';
import { useSnapany } from '@/hooks/snapany';

function SettingAboutPanel() {
  const { t } = useTranslation();

  const { version, updateInfo, mutateUpdateInfo, isLoading } = useSnapany();

  const updateVersion = updateInfo?.version;

  const validLatestFn = useCallback(
    (update?: string, v?: string) =>
      !!update && !!v && compareVersions(v, update) < 0,
    [],
  );

  const latestAvailable = useMemo(
    () => validLatestFn(updateVersion, version),
    [updateVersion, validLatestFn, version],
  );

  const onCheckVersion = async () => {
    const latestUpdateInfo = await mutateUpdateInfo();

    if (!latestUpdateInfo) {
      return Notice.error(t('errors.checkVersionFailed'));
    }

    if (validLatestFn(latestUpdateInfo.version, version)) {
      Notice.success(t('settings.latestVersionAvailable'));
    } else {
      Notice.success(t('settings.latestVersionNotAvailable'));
    }
  };

  const onUpdate = () => {
    Notice.error(t('errors.notImplemented'));
  };

  return (
    <div className="space-y-[22px]">
      <p>
        {t('settings.version')} : v{version}
      </p>
      <section className="flex gap-x-7 items-center">
        <span>
          {t('settings.latestVersion')} :{' '}
          {updateVersion ? `v${updateVersion}` : t('application.loading')}
        </span>

        <Button
          disabled={isLoading}
          size="sm"
          color="blue"
          onClick={() => (latestAvailable ? onUpdate() : onCheckVersion())}
        >
          {latestAvailable ? t('settings.upgrade') : t('settings.checkVersion')}
        </Button>
      </section>

      <p>{t('menu.website')} : https://snapany.com</p>
    </div>
  );
}

export default SettingAboutPanel;
