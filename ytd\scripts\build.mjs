import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { build as viteBuild } from 'vite';

// 获取当前文件的目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.resolve(__dirname, '..');

// 复制public目录到dist，但排除bin目录中的二进制文件
function copyPublicToDist() {
  const publicDir = path.join(rootDir, 'public');
  const distDir = path.join(rootDir, 'dist');
  
  // 确保dist目录存在
  if (!fs.existsSync(distDir)) {
    fs.mkdirSync(distDir, { recursive: true });
  }
  
  // 读取public目录
  const files = fs.readdirSync(publicDir);
  
  for (const file of files) {
    const srcPath = path.join(publicDir, file);
    const destPath = path.join(distDir, file);
    
    // 不复制bin目录
    if (file === 'bin') {
      continue;
    }
    
    // 复制其他文件和目录
    if (fs.statSync(srcPath).isDirectory()) {
      // 递归复制目录
      copyDir(srcPath, destPath);
    } else {
      // 复制文件
      fs.copyFileSync(srcPath, destPath);
    }
    
    console.log(`✅ 已复制: ${file}`);
  }
}

// 递归复制目录
function copyDir(src, dest) {
  // 创建目标目录
  if (!fs.existsSync(dest)) {
    fs.mkdirSync(dest, { recursive: true });
  }
  
  // 读取源目录
  const entries = fs.readdirSync(src);
  
  for (const entry of entries) {
    const srcPath = path.join(src, entry);
    const destPath = path.join(dest, entry);
    
    // 检查是否是二进制文件
    const isBinary = entry.includes('ffmpeg') || entry.includes('ffprobe') || entry.includes('yt-dlp');
    
    if (fs.statSync(srcPath).isDirectory()) {
      // 递归复制子目录
      copyDir(srcPath, destPath);
    } else if (!isBinary) {
      // 只复制非二进制文件
      fs.copyFileSync(srcPath, destPath);
    }
  }
}

async function build() {
  try {
    // 执行Vite构建
    await viteBuild();
    
    // 手动复制public目录（排除二进制文件）
    copyPublicToDist();
    
    console.log('✅ 构建完成');
  } catch (error) {
    console.error('❌ 构建失败:', error);
    process.exit(1);
  }
}

build();
