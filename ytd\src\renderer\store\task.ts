import { DOWNLOAD_STATUS_ENUM } from '@common/constants/download';
import { StoredDownloadTask } from '@common/types/download';
import { RendererFunction, SimpleResult } from '@common/types/electron-bridge';
import * as Sentry from '@sentry/electron/renderer';
import { t } from 'i18next';
import { create } from 'zustand';

import { Notice } from '@/components/Notice';
import {
  cancelDownload,
  clearJsonTasks,
  getDownloadTasks,
  getDownloadVideoInfo,
  onRenderer,
  openFileLocation,
  removeRenderer,
} from '@/service/render';
import { createSyncHandleTaskFactory, taskIdGenerator } from '@/utils/task';

export interface TaskStore {
  tasks: StoredDownloadTask[];
  syncHandle?: RendererFunction;
  registerSync: () => void;
  clearSync: () => void;
  fetchTask: () => void;
  deleteTask: (task: StoredDownloadTask) => void;
  openTaskFolder: (task: StoredDownloadTask) => Promise<SimpleResult>;
  retryTask: (task: StoredDownloadTask) => void;
  addTask: (url: string) => void;
}

const TASKING_STATUS = [
  DOWNLOAD_STATUS_ENUM.Completed,
  DOWNLOAD_STATUS_ENUM.DownloadError,
  DOWNLOAD_STATUS_ENUM.Cancelled,
];

export const useTaskStore = create<TaskStore>((set, get) => ({
  tasks: [],
  syncHandle: undefined,

  async fetchTask() {
    const tasks = await getDownloadTasks();
    const sortedTasks = tasks.sort((a, b) => b.createdAt - a.createdAt);
    set(() => ({ tasks: sortedTasks }));
  },
  async deleteTask(task) {
    if (!TASKING_STATUS.includes(task.status)) {
      await cancelDownload(task.id);
    }
    await clearJsonTasks(task.id || task.videoInfo.id);
    await get().fetchTask();
  },
  async openTaskFolder(task) {
    try {
      return await openFileLocation(task.id);
    } catch (err) {
      Sentry.captureException('打开文件夹失败:' + err);
      if (err instanceof Error && err.message.includes('ENOENT')) {
        Notice.error(t('errors.fileNotFound'));
      } else {
        Notice.error(t('errors.openFileLocationFailed'));
      }
      return { success: false };
    }
  },
  async retryTask(task) {
    await getDownloadVideoInfo(task.url, task.id);
    await get().fetchTask();
  },

  async addTask(url) {
    const taskId = await taskIdGenerator.generateId();

    await getDownloadVideoInfo(url, taskId);
    // ! web 这边的数据不可靠
    await get().fetchTask();
  },

  registerSync() {
    const { syncHandle } = get();

    if (syncHandle) {
      return;
    }

    const handleStatusChange = createSyncHandleTaskFactory(set, get);

    onRenderer('download-status-change', handleStatusChange);
    set(() => ({ syncHandle: handleStatusChange }));
  },
  clearSync() {
    const { syncHandle } = get();

    if (syncHandle) {
      removeRenderer('download-status-change', syncHandle);
      set(() => ({ syncHandle: undefined }));
    }
  },
}));
