import { cloneElement, ReactElement, useEffect, useRef, useState } from 'react';

import Backdrop from '@/components/modal/Backdrop';
import Portal from '@/components/Portal';

export interface SimpleModalProps {
  container?: Element | (() => Element | null) | null;
  open?: boolean;
  children: ReactElement;
  zIndex?: number;
  onClose?: () => void;
}

function SimpleModal({
  container,
  children,
  open,
  zIndex,
  onClose,
}: SimpleModalProps) {
  const portalRef = useRef(null);
  const [isAnimating, setIsAnimating] = useState(false);

  useEffect(() => {
    if (open) {
      setIsAnimating(true);
    } else {
      const timer = setTimeout(() => {
        setIsAnimating(false);
      }, 300);
      return () => clearTimeout(timer);
    }
  }, [open]);

  if (!open && !isAnimating) {
    return null;
  }

  return (
    <Portal ref={portalRef} container={container}>
      <div
        className={`fixed right-0 left-0 top-0 bottom-0 transition-opacity duration-300 ${
          open ? 'opacity-100' : 'opacity-0'
        } ${zIndex ? `z-${zIndex}` : 'z-1000'}`}
      >
        <Backdrop invisible={!open} onClick={onClose}>
          <div
            onClick={(e) => e.stopPropagation()}
            className={`
              rounded-lg 
              overflow-hidden 
              absolute 
              left-1/2 
              top-1/2 
              bg-white 
              transform 
              -translate-x-1/2 
              -translate-y-1/2
              transition-all
              duration-300
              ${open ? 'scale-100 opacity-100' : 'scale-95 opacity-0'}
            `}
          >
            {cloneElement(children, {
              onClose,
            })}
          </div>
        </Backdrop>

        {/* 后续可能需要键盘聚焦 */}
      </div>
    </Portal>
  );
}

export default SimpleModal;
