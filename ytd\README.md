# snapany-desktop

## 本地开发

```bash
pnpm i # 下载 npm 依赖，使用 pnpm 是因为需要配合 lock 文件锁版本
pnpm start # 从脚本层启动
```

## Web 结构

- assets，Web 资源文件。
- business，业务组件。
- client，C 端组件。
- components，基础组件和通用组件。
- constant，常量存放。
- hooks，抽离业务层。
- layouts，页面 UI 模板。
- routes，前端路由。
- store，全局状态。
- service，前端服务。
- types，可复用类型
- utils，工具函数
- index.css，侵入式全局样式。
- main.tsx，工程入口。
- vite-env.d.ts，对 Vite Web 环境的类型声明。

### 组件划分

- 组件库，与特定项目、业务能力无关。
- 业务组件。**全局状态侵入组件为这，不依赖页面组件**，与项目耦合的业务层。
- 页面私有组件。路由层内定义 `components` 目录，简化复杂逻辑，**其他页面不应复用**。
- 通用组件。**不允许这一层使用全局状态，也不允许使用自定义 `hooks`**，提供内建组件和拓展组件能力。
- 基础组件。解决基础设施，**不允许这一层使用全局状态**，提供业务的最基础复用能力。
- 客户端(C 端)组件。**不允许这一层使用全局状态**，纯 UI 相关，满足客户端特定风格。
- 布局组件。配合 UI 模板，不允许有视图布局外的内容。

## 通用代码结构

> 包含渲染进程和主进程复用的代码区域

- constants，通用常量层。
- types，通用类型与 API 约束
- utils，非运行时工具类。
- locales，国际化
