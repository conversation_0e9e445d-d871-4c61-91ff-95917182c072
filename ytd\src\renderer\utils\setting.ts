import { Setting } from '@common/types/setting';

import { saveSettings } from '@/service/render';
import {
  AudioFormat,
  AudioTrack,
  Bitrate,
  DownloadConfigStore,
  Quality,
  Subtitle,
  VideoFormat,
} from '@/store/download';
import { Platform } from '@/utils/environment';

export const validatePort = (port: string) =>
  Number(port) > 0 && Number(port) < 65535;

export const validateHost = (host: string) => {
  const hostPattern =
    /^(?:(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}|(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}|xn--[a-zA-Z0-9]+)$/;
  return hostPattern.test(host);
};

export const saveDownloadConfigToSettings = async (
  downloadConfig: DownloadConfigStore,
  settings: Setting,
) => {
  const updatedSettings = mapDownloadConfigToSettings(downloadConfig, settings);
  await saveSettings(updatedSettings);
};

export const mapDownloadConfigToSettings = (
  downloadConfig: DownloadConfigStore,
  settings: Setting,
): Setting => {
  const {
    mediaType,
    audioFormat,
    audioTracks,
    bitrate,
    hasCover,
    quality,
    subtitles,
    videoFormat,
    platform,
  } = downloadConfig;

  const settingDto: Partial<Setting> = {
    downloadType: mediaType,
    thumbnail: hasCover,
    downloadPlatform: platform,
  };

  if (mediaType === 'video') {
    settingDto.downloadTypeVideo = {
      quality,
      format: videoFormat,
      subtitle: subtitles === 'none' ? [] : subtitles,
      audioChange: audioTracks === 'all' ? ['all'] : audioTracks,
    };
  } else {
    settingDto.downloadTypeAudio = {
      quality: bitrate,
      format: audioFormat,
    };
  }

  return { ...settings, ...settingDto };
};

export const mapSettingsToDownloadConfig = (settings: Setting) => {
  const {
    downloadType,
    downloadTypeVideo,
    downloadTypeAudio,
    thumbnail,
    downloadPlatform,
  } = settings as Partial<Setting>;

  const downloadConfig: Partial<DownloadConfigStore> = {};

  if (downloadType) {
    downloadConfig.mediaType = downloadType;
  }

  if (thumbnail !== undefined) {
    downloadConfig.hasCover = thumbnail;
  }

  if (downloadPlatform) {
    downloadConfig.platform = downloadPlatform as Platform;
  }

  if (downloadType === 'audio') {
    if (downloadTypeAudio?.format) {
      downloadConfig.audioFormat = downloadTypeAudio.format as AudioFormat;
    }

    if (downloadTypeAudio?.quality) {
      downloadConfig.bitrate = downloadTypeAudio.quality as Bitrate;
    }
  } else {
    if (downloadTypeVideo?.audioChange) {
      downloadConfig.audioTracks =
        downloadTypeVideo.audioChange as AudioTrack[];
    }

    if (downloadTypeVideo?.format) {
      downloadConfig.videoFormat = downloadTypeVideo.format as VideoFormat;
    }

    if (downloadTypeVideo?.quality) {
      downloadConfig.quality = downloadTypeVideo.quality as Quality;
    }

    if (downloadTypeVideo?.subtitle) {
      downloadConfig.subtitles = downloadTypeVideo.subtitle as Subtitle[];
    }
  }

  return downloadConfig;
};
