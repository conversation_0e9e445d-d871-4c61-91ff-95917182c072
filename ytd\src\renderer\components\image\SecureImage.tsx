import * as Sentry from '@sentry/electron/renderer';
import React, { useEffect, useState } from 'react';

import SkeletonImageSvgIcon from '@/assets/skeleton-image.svg?react';
import { fetchImage } from '@/service/render';

interface SecureImageProps {
  src: string;
  alt?: string;
  className?: string;
  headers?: HeadersInit;
  onError?: (e: React.SyntheticEvent<HTMLImageElement, Event>) => void;
  onLoad?: (e: React.SyntheticEvent<HTMLImageElement, Event>) => void;
  showPlaceholder?: boolean;
}

const SecureImage: React.FC<SecureImageProps> = ({
  src,
  alt,
  className,
  headers,
  onError,
  onLoad,
  showPlaceholder = false,
}) => {
  const [imageSrc, setImageSrc] = useState<string>('');
  const [isError, setIsError] = useState(false);

  useEffect(() => {
    const loadImage = async () => {
      if (!src) return;

      try {
        const result = await fetchImage(src, headers);
        if (result.success && result.dataBase64) {
          setImageSrc(`data:image/jpeg;base64,${result.dataBase64}`);
          setIsError(false);
        } else {
          console.error('获取图片失败:', result.error);
          setIsError(true);
        }
      } catch (error) {
        Sentry.captureException('获取图片失败:' + error);
        setIsError(true);
        throw error;
      }
    };

    loadImage();
  }, [src, headers]);

  const handleImageError = (
    e: React.SyntheticEvent<HTMLImageElement, Event>,
  ) => {
    setIsError(true);
    if (onError) {
      onError(e);
    }
  };

  const handleImageLoad = (
    e: React.SyntheticEvent<HTMLImageElement, Event>,
  ) => {
    if (onLoad) {
      onLoad(e);
    }
  };

  if (!imageSrc && !isError) {
    return showPlaceholder ? (
      // 透明度为0.5
      <div className={className}>
        <div className=" bg-white/6 w-[96px] h-[96px] flex items-center justify-center">
          <SkeletonImageSvgIcon className="w-12 h-12 fill-[#bfbfbf]" />
        </div>
      </div>
    ) : null;
  }

  if (isError) {
    return showPlaceholder ? (
      <div
        className={className}
        style={{ backgroundColor: 'rgba(255, 0, 0, 0.2)' }}
      >
        <div className=" bg-white/6 w-[96px] h-[96px] flex items-center justify-center">
          <SkeletonImageSvgIcon className="w-12 h-12 fill-[#bfbfbf]" />
        </div>
      </div>
    ) : null;
  }

  return (
    <img
      src={imageSrc}
      alt={alt}
      className={className}
      onError={handleImageError}
      onLoad={handleImageLoad}
    />
  );
};

export default SecureImage;
