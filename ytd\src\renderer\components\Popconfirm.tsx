import { Button, ButtonProps, Popover } from 'flowbite-react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ReactNode, useState } from 'react';
import { useTranslation } from 'react-i18next';

interface PopconfirmProps {
  okText?: string;
  okButtonProps?: Omit<ButtonProps, 'onClick'>;
  onConfirm?: MouseEventHandler<HTMLButtonElement>;
  cancelText?: string;
  cancelButtonProps?: Omit<ButtonProps, 'onClick'>;
  onCancel?: MouseEventHandler<HTMLButtonElement>;
  children: ReactNode;
  disabled?: boolean;
  title: ReactNode;
}

function Popconfirm({
  okText,
  cancelText,
  children,
  title,
  okButtonProps,
  cancelButtonProps,
  onCancel,
  onConfirm,
  disabled,
}: PopconfirmProps) {
  const { t } = useTranslation();

  const [open, setOpen] = useState(false);

  return (
    <Popover
      open={disabled ? false : open}
      onOpenChange={(v) => {
        if (disabled) {
          return;
        }
        setO<PERSON>(v);
      }}
      content={
        <div className="px-7.5 py-4 flex flex-col gap-6">
          <main className=" flex items-center justify-center text-sm">
            {title}
          </main>
          <footer className="flex justify-around gap-6">
            <Button
              className="text-sm"
              color="light"
              {...cancelButtonProps}
              onClick={(e) => {
                setOpen(false);
                onCancel?.(e);
              }}
            >
              {cancelText ?? t('common.cancel')}
            </Button>
            <Button
              className="text-sm"
              color="blue"
              {...okButtonProps}
              onClick={onConfirm}
            >
              {okText ?? t('common.ok')}
            </Button>
          </footer>
        </div>
      }
    >
      {/* 套一层 div 的原因是 flowbite-react 源码存在 ref 引用 target。 */}
      <div>{children}</div>
    </Popover>
  );
}

export default Popconfirm;
