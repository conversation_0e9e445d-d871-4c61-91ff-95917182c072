{"name": "snapany-desktop", "productName": "snapany", "homepage": "https://snapany.com", "author": "gongyi<PERSON>hi", "version": "0.4.6", "description": "SnapAny - 视频下载工具", "main": "dist-electron/main.js", "scripts": {"start": "node ./scripts/start.mjs", "clean": "rimraf dist dist-electron release", "build": "node ./scripts/build.mjs && electron-builder", "make:win": "node ./scripts/build.mjs  && electron-builder --win --x64", "make:mac": "npm run make:mac-x64 && npm run make:mac-arm64", "make:mac-x64": "npm run install-deps && node scripts/fftools-x64Tofftools.mjs && node ./scripts/build.mjs && electron-builder --mac --x64", "make:mac-arm64": "npm run install-deps && node scripts/fftools-armTofftools.mjs && node ./scripts/build.mjs && electron-builder --mac --arm64", "make:win32": "node ./scripts/build.mjs && electron-builder --win --ia32", "make:win64": "npm run install-deps && node ./scripts/build.mjs && electron-builder --win --x64", "make:win-all": "npm run install-deps && node ./scripts/build.mjs && electron-builder --win --ia32 --x64", "install-deps": "node scripts/install-ytdlp.mjs && node scripts/install-ffmpeg.mjs && node scripts/install-ffprobe.mjs", "prepare": "husky"}, "keywords": [], "license": "ISC", "dependencies": {"@aptabase/electron": "^0.3.1", "@sentry/electron": "^5.9.0", "ahooks": "^3.8.4", "classnames": "^2.5.1", "flowbite-react": "^0.10.2", "fluent-ffmpeg": "^2.1.3", "i18next": "^24.2.1", "i18next-browser-languagedetector": "^8.0.2", "linkifyjs": "^4.2.0", "m3u8-parser": "^7.2.0", "mime-types": "^2.1.35", "normalize-url": "^8.0.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-i18next": "^15.4.0", "react-icons": "^5.4.0", "react-router-dom": "^7.2.0", "react-virtuoso": "^4.12.5", "sqlite3": "^5.1.7", "swr": "^2.3.2", "uuid": "^11.0.3", "zustand": "^5.0.3"}, "devDependencies": {"@commitlint/cli": "^19.7.1", "@commitlint/config-conventional": "^19.7.1", "@electron/notarize": "^2.5.0", "@eslint/js": "^9.21.0", "@tailwindcss/vite": "^4.0.8", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/uuid": "^10.0.0", "@vitejs/plugin-react": "^4.3.4", "cross-env": "^7.0.3", "electron": "32.3.3", "electron-builder": "^25.1.8", "eslint": "^9.21.0", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.5", "eslint-plugin-simple-import-sort": "^12.1.1", "fs-extra": "^11.3.0", "globals": "^16.0.0", "husky": "^9.1.7", "lint-staged": "^15.4.3", "prettier": "^3.5.2", "rimraf": "^6.0.1", "sass": "^1.85.0", "tailwindcss": "^4.0.8", "typescript": "^5.7.2", "typescript-eslint": "^8.25.0", "vite": "^6.1.1", "vite-plugin-electron": "^0.29.0", "vite-plugin-svgr": "^4.3.0"}, "engines": {"node": ">=18.0.0"}, "pnpm": {"onlyBuiltDependencies": ["@parcel/watcher", "electron", "esbuild", "sqlite3"]}}