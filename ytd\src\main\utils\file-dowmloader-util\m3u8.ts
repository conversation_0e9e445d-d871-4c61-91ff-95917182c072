import { spawn } from 'child_process';
import crypto from 'crypto';
import { net, session } from 'electron';
import { app } from 'electron';
import fs from 'fs-extra';
import { Parser } from 'm3u8-parser';
import path from 'path';

import TaskPool from './task-pool';

interface ProgressInfo {
  totalBytes: number;
  downloadedBytes: number;
  speedBytes: number;
}

interface DownloadResult {
  tasks: (() => Promise<string>)[];
}

interface PlaylistItem {
  uri: string;
  attributes: Record<string, string>;
}

interface SegmentItem {
  uri: string;
}

interface MediaGroup {
  uri?: string;
  [key: string]: unknown;
}

interface M3u8Parser {
  mapUri: string;
  playlists: PlaylistItem[];
  segments: SegmentItem[];
  mediaGroups: Record<string, Record<string, MediaGroup>>;
}

interface ParseSegmentResult {
  segmentUrls: string[];
  videoM3u8Url: string; // 实际的视频m3u8 url
  audioM3u8Url: string; // 实际的音频m3u8 url
}

export class M3u8Downloader {
  private tempDir: string;
  private headers: Record<string, string> = {
    'User-Agent':
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
  };
  private downloadedBytes: number = 0;
  private currentSecondBytes: number = 0;
  private totalBytes: number = 0;
  public proxyUrl: string = '';
  private timeout: number = 10000;
  private socketTimeout: number = 10000;
  private retryCount: number = 1;
  private ffmpegPath: string = '';

  constructor() {
    // 初始化临时目录
    this.tempDir = path.join(app.getPath('temp'), app.getName(), 'downloads');

    // 初始化ffmpeg路径（异步）
    this.initFFmpegPath();
  }

  // 异步初始化ffmpeg路径
  private async initFFmpegPath(): Promise<void> {
    try {
      this.ffmpegPath = await this.getFFmpegPath();
    } catch (error) {
      console.error('初始化FFmpeg路径失败:', error);
      throw error;
    }
  }

  private async getFFmpegPath(): Promise<string> {
    const isDev = process.env.NODE_ENV === 'development';
    const platform = process.platform;

    try {
      if (isDev) {
        // 开发环境路径
        return path.join(
          __dirname,
          '..',
          'public',
          'bin',
          platform === 'win32' ? 'ffmpeg.exe' : `ffmpeg-${process.arch}`,
        );
      }

      // 生产环境路径
      const basePath = app.isPackaged
        ? path.join(process.resourcesPath, 'app.asar.unpacked')
        : path.join(__dirname, '..', '..');

      const ffmpegPath = path.join(
        basePath,
        'public',
        'bin',
        platform === 'win32' ? 'ffmpeg.exe' : 'ffmpeg',
      );

      // 确保文件存在
      try {
        await fs.promises.access(ffmpegPath);
        return ffmpegPath;
      } catch {
        throw new Error(`FFmpeg 可执行文件不存在: ${ffmpegPath}`);
      }
    } catch (error) {
      console.error('获取 FFmpeg 路径失败:', error);
      throw error;
    }
  }

  async parseM3u8(
    tempFolderName: string,
    m3u8Url: string,
    type: 'video' | 'audio',
    headers?: Record<string, string>,
    controller?: AbortController,
  ): Promise<M3u8Parser> {
    const m3u8Content = await new Promise<string>((resolve, reject) => {
      const request = net.request({
        method: 'GET',
        url: m3u8Url,
        headers: { ...headers, ...this.headers },
        session: session.defaultSession,
        referrerPolicy: 'unsafe-url',
      });

      let data = '';
      request.on('response', (response) => {
        if (response.statusCode !== 200) {
          reject(new Error(`请求失败，状态码: ${response.statusCode}`));
          return;
        }

        response.on('data', (chunk) => {
          data += chunk.toString();
        });
        response.on('end', () => resolve(data));
        response.on('error', reject);
      });

      request.on('error', reject);

      if (controller) {
        controller.signal.addEventListener('abort', () => {
          request.abort();
          reject(new Error('请求被取消'));
        });
      }

      request.end();
    });

    const parser = new Parser();
    parser.push(m3u8Content);
    parser.end();

    // 使用绝对路径创建目录
    const absoluteTempPath = path.join(this.tempDir, tempFolderName, type);
    await fs.ensureDir(absoluteTempPath);

    // 修改：处理m3u8内容中的所有URL参数
    let processedContent = m3u8Content;
    // 处理EXT-X-MAP中的URI
    processedContent = processedContent.replace(
      /(#EXT-X-MAP:URI=")([^"]+)(")/g,
      (match: string, p1: string, p2: string, p3: string): string =>
        p1 + p2.replace(/\?.*$/, '') + p3,
    );
    // 处理.ts文件的URL
    processedContent = processedContent.replace(
      /^(.+\.(ts|m4s))(\?.*$)/gm,
      '$1',
    );

    // 使用绝对路径写入文件
    const m3u8FilePath = path.join(absoluteTempPath, `${type}.m3u8`);
    await fs.writeFile(m3u8FilePath, processedContent);

    return {
      mapUri: processedContent.match(/#EXT-X-MAP:URI="([^"]+)"/)?.[1] || '',
      playlists: parser.manifest.playlists || [],
      segments: parser.manifest.segments || [],
      mediaGroups: parser.manifest.mediaGroups || {},
    };
  }

  async parseSegments(
    tempFolderName: string,
    m3u8Url: string,
    m3u8Parser: M3u8Parser,
    headers?: Record<string, string>,
    controller?: AbortController,
  ): Promise<ParseSegmentResult> {
    // 获取所有分片URL
    const playlists = m3u8Parser.playlists;
    const mediaGroups = m3u8Parser.mediaGroups;
    let segments = m3u8Parser.segments;

    let mapUri = m3u8Parser.mapUri;
    let videoM3u8Url = m3u8Url;
    let audioM3u8Url = '';
    // 处理 playlist 情况
    if (segments.length === 0 && playlists.length > 0) {
      // console.log("检测到主播放列表，寻找最佳质量的子播放列表...");

      // 选择带宽最高的播放列表
      const bestPlaylist = playlists.reduce(
        (best: PlaylistItem, current: PlaylistItem) => {
          const currentBandwidth = current.attributes?.BANDWIDTH || 0;
          const bestBandwidth = best.attributes?.BANDWIDTH || 0;
          return currentBandwidth > bestBandwidth ? current : best;
        },
        playlists[0],
      );

      if (!bestPlaylist) {
        throw new Error('未找到可用的播放列表');
      }
      // console.log("bestPlaylist:\n", bestPlaylist);

      // 获取子播放列表的 URL
      const subPlaylistUrl = new URL(bestPlaylist.uri, m3u8Url).href;
      videoM3u8Url = subPlaylistUrl;
      // console.log("选择子播放列表:", subPlaylistUrl);
      // console.log("带宽:", bestPlaylist.attributes?.BANDWIDTH, "bps");
      // console.log(
      //   "分辨率:",
      //   bestPlaylist.attributes?.RESOLUTION?.width,
      //   "x",
      //   bestPlaylist.attributes?.RESOLUTION?.height
      // );
      if (bestPlaylist.attributes?.AUDIO) {
        const audioObject = mediaGroups.AUDIO[bestPlaylist.attributes?.AUDIO];
        if (audioObject) {
          const audioKeys = Object.keys(audioObject);
          if (audioKeys.length > 0) {
            const audioGroup = audioObject[audioKeys[0]] as MediaGroup;
            audioM3u8Url = new URL(audioGroup.uri || '', m3u8Url).href;
          }
        }
      }

      // console.log("audioM3u8Url:\n", audioM3u8Url);

      // 获取并解析子播放列表
      const subM3u8Parser = await this.parseM3u8(
        tempFolderName,
        subPlaylistUrl,
        'video',
        headers,
        controller,
      );
      mapUri = subM3u8Parser.mapUri;
      segments = subM3u8Parser.segments || [];

      if (segments.length === 0) {
        throw new Error('子播放列表中未找到媒体分片');
      }
    }

    // 获取所有分片URL
    let segmentUrls = segments.map((segment: SegmentItem) => {
      return new URL(segment.uri, videoM3u8Url).href;
    });

    if (mapUri != '') {
      segmentUrls = [new URL(mapUri, videoM3u8Url).href, ...segmentUrls];
    }
    // console.log(segmentUrls);
    return {
      segmentUrls: segmentUrls,
      videoM3u8Url: videoM3u8Url,
      audioM3u8Url: audioM3u8Url,
    };
  }

  async downloadSegments(
    segmentUrls: string[],
    tempDir: string,
    type: 'video' | 'audio',
    headers?: Record<string, string>,
    controller?: AbortController,
  ): Promise<DownloadResult> {
    console.log('开始获取分片大小...');
    const sizeTasks = segmentUrls.map(async (url: string) => {
      try {
        const size = await new Promise<number>((resolve, reject) => {
          const request = net.request({
            method: 'GET',
            url: url,
            headers: {
              range: 'bytes=0-0',
              ...this.headers,
              ...headers,
            },
            session: session.defaultSession,
            referrerPolicy: 'unsafe-url',
          });

          request.on('response', (response) => {
            if (response.statusCode !== 206) {
              reject(
                new Error(`获取分片大小失败，状态码: ${response.statusCode}`),
              );
              return;
            }

            const contentRange = response.headers['content-range'];
            if (!contentRange || Array.isArray(contentRange)) {
              reject(new Error('服务器未返回有效的Content-Range头'));
              return;
            }

            const size = parseInt(contentRange.split('/').pop() || '0');
            resolve(size);
          });

          request.on('error', reject);

          if (controller) {
            controller.signal.addEventListener('abort', () => {
              request.abort();
              reject(new Error('请求被取消'));
            });
          }

          request.end();
        });

        return size;
      } catch (error) {
        console.error(`获取分片大小失败 ${url}:`, error);
        throw error;
      }
    });

    const sizes = await Promise.all(sizeTasks);
    this.totalBytes += sizes.reduce((acc, size) => acc + size, 0);
    console.log('总大小:', this.totalBytes, 'bytes');

    // 下载所有分片
    const downloadTasks = segmentUrls.map((url: string, index: number) => {
      return async () => {
        try {
          const newUrl = url.replace(/\?.*$/, '');
          const filename = path.basename(newUrl);
          // 使用绝对路径
          const typeDir = path.join(tempDir, type);
          await fs.ensureDir(typeDir);
          const segmentPath = path.join(typeDir, filename);

          // 检查文件是否已存在且完整
          if (await this.isFileComplete(segmentPath, sizes[index])) {
            console.log(`分片 ${filename} 已存在且完整，跳过下载`);
            this.downloadedBytes += sizes[index];
            return segmentPath;
          }

          // 使用 retryCount 控制重试次数
          for (let attempt = 0; attempt <= this.retryCount; attempt++) {
            try {
              await new Promise<void>((resolve, reject) => {
                const writer = fs.createWriteStream(segmentPath);
                const request = net.request({
                  method: 'GET',
                  url: url,
                  headers: { ...headers, ...this.headers },
                  session: session.defaultSession,
                  referrerPolicy: 'unsafe-url',
                });

                request.on('response', (response) => {
                  if (response.statusCode !== 200) {
                    reject(
                      new Error(`下载分片失败，状态码: ${response.statusCode}`),
                    );
                    return;
                  }

                  let downloadedChunkSize = 0;
                  response.on('data', (chunk) => {
                    writer.write(chunk);
                    downloadedChunkSize = chunk.length;
                    this.downloadedBytes += downloadedChunkSize;
                    this.currentSecondBytes += downloadedChunkSize;
                  });

                  response.on('end', () => {
                    writer.end();
                    resolve();
                  });

                  response.on('error', (error) => {
                    writer.end();
                    reject(error);
                  });
                });

                request.on('error', (error) => {
                  writer.end();
                  reject(error);
                });

                if (controller) {
                  controller.signal.addEventListener('abort', () => {
                    request.abort();
                    writer.end();
                    reject(new Error('请求被取消'));
                  });
                }

                writer.on('error', reject);
                request.end();
              });

              // 验证下载的文件大小
              if (await this.isFileComplete(segmentPath, sizes[index])) {
                return segmentPath;
              }
              throw new Error('文件大小验证失败');
            } catch (error) {
              if (attempt === this.retryCount) {
                // 已达到最大重试次数
                console.error(
                  `分片 ${filename} 达到最大重试次数 ${this.retryCount}，下载失败`,
                );
                throw error;
              }
              console.warn(
                `分片 ${filename} 下载失败，正在进行第 ${attempt + 1}/${
                  this.retryCount
                } 次重试...`,
              );
              // 删除可能损坏的文件
              await fs.remove(segmentPath).catch(() => {});
            }
          }
          throw new Error(`分片 ${filename} 下载失败`);
        } catch (error) {
          console.error(`下载分片 ${index} 失败:`, error);
          throw error;
        }
      };
    });

    return {
      tasks: downloadTasks,
    };
  }

  // 添加新的辅助方法来检查文件完整性
  private async isFileComplete(
    filePath: string,
    expectedSize: number,
  ): Promise<boolean> {
    try {
      if (!(await fs.pathExists(filePath))) {
        return false;
      }
      const stats = await fs.stat(filePath);
      return stats.size === expectedSize;
    } catch (error) {
      console.error(`检查文件完整性失败 ${filePath}:`, error);
      return false;
    }
  }

  async mergeSegments(
    outputPath: string,
    tempFolderName: string,
  ): Promise<void> {
    // 使用绝对路径
    const videoPath = path.join(outputPath, 'video');
    const audioPath = path.join(outputPath, 'audio');
    const [isHaveVideo, isHaveAudio] = await Promise.all([
      fs.pathExists(videoPath),
      fs.pathExists(audioPath),
    ]);

    // 修改为返回Promise的FFmpeg执行函数
    const runFFmpeg = (args: string[]): Promise<void> => {
      return new Promise((resolve, reject) => {
        console.log(`执行FFmpeg命令: ${this.ffmpegPath} ${args.join(' ')}`);
        const ffmpeg = spawn(this.ffmpegPath, args);

        let errorOutput = '';

        ffmpeg.stderr.on('data', (data) => {
          errorOutput += data.toString();
        });

        ffmpeg.on('close', (code) => {
          console.log('FFmpeg 进程结束，退出码:', code);
          if (code === 0) {
            resolve();
          } else {
            reject(
              new Error(
                `FFmpeg exited with code ${code}\nError: ${errorOutput}`,
              ),
            );
          }
        });

        ffmpeg.on('error', (err) => {
          console.error('FFmpeg 进程错误:', err);
          reject(err);
        });
      });
    };

    try {
      if (isHaveVideo) {
        const videoM3u8Path = path.join(videoPath, 'video.m3u8');
        const exists = await fs.pathExists(videoM3u8Path);
        if (!exists) {
          throw new Error(`视频 m3u8 文件不存在: ${videoM3u8Path}`);
        }

        // 转换视频，添加 -y 参数
        await runFFmpeg([
          '-y', // 添加这个参数来自动覆盖文件
          '-i',
          videoM3u8Path,
          '-c',
          'copy',
          path.join(videoPath, 'video.mp4'),
        ]);
      }

      if (isHaveAudio) {
        const audioM3u8Path = path.join(audioPath, 'audio.m3u8');
        const exists = await fs.pathExists(audioM3u8Path);
        if (!exists) {
          throw new Error(`音频 m3u8 文件不存在: ${audioM3u8Path}`);
        }

        // 转换音频，添加 -y 参数
        await runFFmpeg([
          '-y', // 添加这个参数来自动覆盖文件
          '-i',
          audioM3u8Path,
          '-c',
          'copy',
          path.join(audioPath, 'audio.mp4'),
        ]);
      }

      // 合并视频和音频
      if (isHaveVideo && isHaveAudio) {
        console.log('开始合并视频和音频...');
        // 使用绝对路径
        const outputFilePath = path.join(
          outputPath,
          '..',
          `${tempFolderName}.mp4`,
        );
        await runFFmpeg([
          '-y', // 添加这个参数来自动覆盖文件
          '-i',
          path.join(videoPath, 'video.mp4'),
          '-i',
          path.join(audioPath, 'audio.mp4'),
          '-c:v',
          'copy',
          '-c:a',
          'copy',
          '-map',
          '0:v:0',
          '-map',
          '1:a:0',
          outputFilePath,
        ]);
      } else if (isHaveVideo) {
        // 使用绝对路径
        const outputFilePath = path.join(
          outputPath,
          '..',
          `${tempFolderName}.mp4`,
        );
        await runFFmpeg([
          '-y', // 添加这个参数来自动覆盖文件
          '-i',
          path.join(videoPath, 'video.m3u8'),
          '-c',
          'copy',
          outputFilePath,
        ]);
      }
    } catch (error) {
      console.error('FFmpeg处理失败:', error);
      throw error;
    }
  }

  async downloadM3u8(
    m3u8Url: string,
    tempDir: string,
    options: {
      headers?: Record<string, string>;
      onProgress?: (progress: ProgressInfo) => void;
      onComplete?: (filePath: string) => void;
      onError?: (error: Error) => void;
    } = {},
  ): Promise<string> {
    const taskPool = new TaskPool(2);
    const tempFolderName = md5(m3u8Url);
    let progressTimer: NodeJS.Timeout | undefined;

    try {
      // 获取并解析m3u8文件
      const m3u8Parser = await this.parseM3u8(
        tempFolderName,
        m3u8Url,
        'video',
        options.headers,
      );
      const parseResult = await this.parseSegments(
        tempFolderName,
        m3u8Url,
        m3u8Parser,
      );
      let tasks: (() => Promise<string>)[] = [];

      const outputPath = path.join(tempDir, tempFolderName);
      const { tasks: videoTasks } = await this.downloadSegments(
        parseResult.segmentUrls,
        outputPath,
        'video',
      );
      tasks = [...tasks, ...videoTasks];
      if (parseResult.audioM3u8Url != '') {
        // 获取并解析m3u8文件
        const m3u8Parser = await this.parseM3u8(
          tempFolderName,
          parseResult.audioM3u8Url,
          'audio',
          options.headers,
        );
        const parseVideoResult = await this.parseSegments(
          tempFolderName,
          parseResult.audioM3u8Url,
          m3u8Parser,
          options.headers,
        );
        const { tasks: audioTasks } = await this.downloadSegments(
          parseVideoResult.segmentUrls,
          outputPath,
          'audio',
          options.headers,
        );
        tasks = [...tasks, ...audioTasks];
      }
      // 修改进度回调定时器的逻辑
      progressTimer = setInterval(() => {
        options.onProgress?.({
          totalBytes: this.totalBytes,
          downloadedBytes: this.downloadedBytes,
          speedBytes: this.currentSecondBytes,
        });
        this.currentSecondBytes = 0;
      }, 1000);

      // 使用 TaskPool 运行所有任务
      try {
        await taskPool.runAll(tasks);
      } catch (error) {
        console.error('下载任务失败:', error);
        throw error;
      }

      // 合并视频和音频
      await this.mergeSegments(outputPath, tempFolderName);
      // 删除临时文件
      await fs.remove(outputPath);

      const filePath = path.join(tempDir, `${tempFolderName}.mp4`);
      options.onComplete?.(filePath);
      return filePath;
    } catch (error) {
      options.onError?.(error as Error);
      throw error;
    } finally {
      clearInterval(progressTimer);
      await taskPool.close();
    }
  }

  async downloadMultipleM3u8(
    urls: {
      url: string;
      headers?: Record<string, string>;
    }[],
    downloadPath: string,
    options: {
      headers?: Record<string, string>;
      onProgress?: (
        progresses: { url: string; progress: ProgressInfo }[],
      ) => void;
      onComplete?: (results: { url: string; filePath: string }[]) => void;
      onError?: (url: string, error: Error) => void;
    } = {},
  ): Promise<{ url: string; filePath: string }[]> {
    const results: { url: string; filePath: string }[] = [];
    const progressMap = new Map<string, ProgressInfo>();

    // 创建下载临时目录路径
    const downloadTempDir = path.join(downloadPath, 'temp');
    // 确保临时目录存在
    await fs.ensureDir(downloadTempDir);

    // 创建进度更新定时器
    const progressTimer = setInterval(() => {
      if (options.onProgress) {
        const progresses = Array.from(progressMap.entries()).map(
          ([url, progress]) => ({
            url,
            progress,
          }),
        );
        options.onProgress(progresses);
      }
    }, 1000);

    try {
      await Promise.all(
        urls.map(async (urlObj) => {
          try {
            const downloader = new M3u8Downloader();
            // 设置下载临时目录
            await downloader.setTempDir(downloadTempDir);

            await downloader.downloadM3u8(urlObj.url, downloadTempDir, {
              headers: { ...options.headers, ...urlObj.headers },
              onProgress: (progress) => {
                progressMap.set(urlObj.url, progress);
              },
              onComplete: (filePath) => {
                results.push({ url: urlObj.url, filePath });
              },
              onError: (error) => {
                options.onError?.(urlObj.url, error);
              },
            });
          } catch (error) {
            options.onError?.(urlObj.url, error as Error);
          }
        }),
      );

      options.onComplete?.(results);
      return results;
    } finally {
      clearInterval(progressTimer);
    }
  }

  // 获取m3u8文件总大小
  public async getTotalM3u8Size(
    m3u8Url: string,
    customHeaders?: Record<string, string>,
    controller?: AbortController,
  ): Promise<number> {
    const headers = {
      ...this.headers,
      ...customHeaders,
    };

    const m3u8Parser = await this.parseM3u8(
      'temp',
      m3u8Url,
      'video',
      headers,
      controller,
    );
    const parseResult = await this.parseSegments(
      'temp',
      m3u8Url,
      m3u8Parser,
      headers,
      controller,
    );
    const segmentUrls = parseResult.segmentUrls;

    const sizeTasks = segmentUrls.map(async (url: string) => {
      try {
        const size = await new Promise<number>((resolve, reject) => {
          const request = net.request({
            method: 'GET',
            url: url,
            headers: {
              range: 'bytes=0-0',
              ...headers,
            },
            session: session.defaultSession,
            referrerPolicy: 'unsafe-url',
          });

          request.on('response', (response) => {
            if (response.statusCode !== 206) {
              reject(
                new Error(`获取分片大小失败，状态码: ${response.statusCode}`),
              );
              return;
            }

            const contentRange = response.headers['content-range'];
            if (!contentRange || Array.isArray(contentRange)) {
              reject(new Error('服务器未返回有效的Content-Range头'));
              return;
            }

            const size = parseInt(contentRange.split('/').pop() || '0');
            resolve(size);
          });

          request.on('error', reject);

          if (controller) {
            controller.signal.addEventListener('abort', () => {
              request.abort();
              reject(new Error('请求被取消'));
            });
          }

          request.end();
        });

        return size;
      } catch (error) {
        console.error(`获取分片大小失败 ${url}:`, error);
        throw error;
      }
    });

    const sizes = await Promise.all(sizeTasks);
    return sizes.reduce((acc, size) => acc + size, 0);
  }

  // 设置临时目录
  public async setTempDir(dir: string): Promise<void> {
    try {
      // 规范化路径
      const normalizedPath = path.normalize(dir);

      // 确保目录存在
      await fs.ensureDir(normalizedPath);

      // 检查写入权限
      try {
        await fs.access(normalizedPath, fs.constants.W_OK);
      } catch (error: unknown) {
        const errorMessage =
          error instanceof Error ? error.message : String(error);
        throw new Error(`无法写入临时目录 ${normalizedPath}: ${errorMessage}`);
      }

      this.tempDir = normalizedPath;
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      console.error('设置临时目录失败:', errorMessage);
      throw error;
    }
  }
}

function md5(str: string) {
  return crypto.createHash('md5').update(str).digest('hex');
}

export default M3u8Downloader;
