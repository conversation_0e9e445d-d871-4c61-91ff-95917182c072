{"appId": "com.snapany.desktop", "productName": "SnapAny", "directories": {"output": "release", "buildResources": "assets"}, "files": ["dist-electron/**/*", "dist/**/*", "dist-electron/preload.js", "package.json"], "afterSign": "scripts/notarize.js", "win": {"target": [{"target": "nsis", "arch": ["x64"]}], "icon": "public/icon256.ico", "publisherName": "GYS", "signingHashAlgorithms": ["sha256"], "signAndEditExecutable": true, "rfc3161TimeStampServer": "http://timestamp.sectigo.com", "certificateFile": "./extra/certificates/certificate.pfx", "certificatePassword": "SECTIGO1029476773", "files": [{"from": "public/bin", "to": "public/bin", "filter": ["ffmpeg.exe", "ffprobe.exe", "yt-dlp.exe"]}], "asarUnpack": ["public/bin/ffmpeg.exe", "public/bin/ffprobe.exe", "public/bin/yt-dlp.exe"]}, "mac": {"target": [{"target": "dmg"}], "icon": "public/icon.icns", "hardenedRuntime": true, "gatekeeperAssess": false, "entitlements": "extra/entitlements.mac.plist", "entitlementsInherit": "extra/entitlements.mac.plist", "cscLink": "./extra/certificates/证书.p12", "cscKeyPassword": "gys-tech", "category": "public.app-category.utilities", "extendInfo": {"CFBundleIdentifier": "com.snapany.desktop"}, "identity": "A0A4EFE8A6A06D52FE631E901F7F4B3C0A1FFA03", "files": [{"from": "public/bin", "to": "public/bin", "filter": ["ffmpeg", "ffprobe", "yt-dlp"]}], "asarUnpack": ["public/bin/ffmpeg", "public/bin/ffprobe", "public/bin/yt-dlp"], "binaries": ["Contents/Resources/app.asar.unpacked/public/bin/ffmpeg", "Contents/Resources/app.asar.unpacked/public/bin/ffprobe", "Contents/Resources/app.asar.unpacked/public/bin/yt-dlp"], "darkModeSupport": true, "artifactName": "SnapAny_${version}_${arch}.dmg"}, "nsis": {"oneClick": false, "perMachine": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "SnapAny", "installerIcon": "public/icon256.ico", "uninstallerIcon": "public/icon256.ico", "artifactName": "SnapAny_${version}_${arch}.${ext}", "deleteAppDataOnUninstall": true}, "compression": "maximum", "asar": true, "dmg": {"sign": true}, "publish": null, "extraResources": [{"from": "src/common/locales", "to": "locales"}]}