import { app } from 'electron';
import * as fs from 'fs';
import * as path from 'path';

// 存储当前语言设置
let currentLanguage = 'en';

// 存储已加载的语言翻译
const loadedTranslations: { [lang: string]: Record<string, unknown> } = {};

// 支持的语言列表
const SUPPORTED_LANGUAGES = [
  'en', // 英文
  'zh-Hans', // 简体中文
  'zh-Hant', // 繁体中文
  'hi', // 印地语
  'es', // 西班牙语
  'fr', // 法语
  'ru', // 俄语
  'id', // 印尼语
  'bn', // 孟加拉语
  'pt', // 葡萄牙语
  'de', // 德语
  'ja', // 日语
  'ko', // 韩语
  'vi', // 越南语
  'tr', // 土耳其语
  'it', // 意大利语
];

// 语言代码映射
const languageMap: { [key: string]: string } = {
  en: 'en',
  'zh-Hans': 'zh-<PERSON>',
  'zh-Hant': 'zh-Hant',
  zh: 'zh-<PERSON>',
  hi: 'hi',
  es: 'es',
  fr: 'fr',
  ru: 'ru',
  id: 'id',
  bn: 'bn',
  pt: 'pt',
  de: 'de',
  ja: 'ja',
  ko: 'ko',
  vi: 'vi',
  tr: 'tr',
  it: 'it',
};

// 系统语言到应用语言的映射
function mapSystemLanguage(systemLang: string): string {
  // 特殊处理中文
  if (systemLang.toLowerCase().startsWith('zh')) {
    // 检查是否包含 Hans 或 Hant
    if (systemLang.includes('Hans') || systemLang.includes('CN')) {
      return 'zh-Hans';
    }
    if (
      systemLang.includes('Hant') ||
      systemLang.includes('TW') ||
      systemLang.includes('HK')
    ) {
      return 'zh-Hant';
    }
    // 默认使用简体中文
    return 'zh-Hans';
  }

  // 获取基础语言代码
  const baseLang = systemLang.split('-')[0].toLowerCase();

  // 语言的映射
  const langMap: { [key: string]: string } = {
    'zh-hans': 'zh-Hans',
    'zh-hant': 'zh-Hant',
    zh: 'zh-Hans',
    en: 'en',
    ja: 'ja',
    ko: 'ko',
    pt: 'pt',
    bn: 'bn',
    de: 'de',
    it: 'it',
    vi: 'vi',
    tr: 'tr',
    hi: 'hi',
    es: 'es',
    fr: 'fr',
    ru: 'ru',
    id: 'id',
  };

  return langMap[baseLang] || 'en';
}

// 获取语言文件路径
function getLanguageFilePath(lang: string) {
  const isDev = process.env.NODE_ENV === 'development';
  if (isDev) {
    // 开发环境下从项目根目录读取
    return path.join(process.cwd(), 'locales', `${lang}.json`);
  }
  // 生产环境下从 resources 目录读取
  return path.join(process.resourcesPath, 'locales', `${lang}.json`);
}

// 获取语言文件名
function getLanguageFileName(locale: string): string {
  // 转换为小写并查找映射
  const normalizedLocale = locale.toLowerCase();
  const mappedLocale = languageMap[normalizedLocale] || normalizedLocale;

  // 返回对应的文件名
  return `${mappedLocale}.json`;
}

// 修改加载语言文件的函数
async function loadLanguageFile(
  locale: string,
): Promise<Record<string, unknown>> {
  // 如果已经加载过该语言，直接返回缓存
  if (loadedTranslations[locale]) {
    return loadedTranslations[locale];
  }

  try {
    const fileName = getLanguageFileName(locale);
    const filePath = path.join(getLocalesPath(), fileName);

    try {
      await fs.promises.access(filePath);
      const content = await fs.promises.readFile(filePath, 'utf8');
      const translations = JSON.parse(content) as Record<string, unknown>;

      // 缓存翻译
      loadedTranslations[locale] = translations;
      return translations;
    } catch (error) {
      console.error(`加载语言文件失败: ${locale}`, error);
      // 如果加载失败，尝试加载英文
      if (locale !== 'en') {
        return await loadLanguageFile('en');
      }
      throw error;
    }
  } catch (error) {
    console.error(`加载语言文件失败: ${locale}`, error);
    // 如果英文也加载失败，返回空对象
    return {};
  }
}

// 获取设置文件路径
function getSettingsPath() {
  return path.join(app.getPath('userData'), 'settings.json');
}

// 从设置文件读取语言
async function getLanguageFromSettings(): Promise<string> {
  try {
    const systemLang = app.getPreferredSystemLanguages()[0];
    const settingsPath = getSettingsPath();
    console.log('读取设置:', settingsPath);

    try {
      await fs.promises.access(settingsPath);
      const content = await fs.promises.readFile(settingsPath, 'utf8');
      const settings = JSON.parse(content);

      // 如果设置为 system 或未设置，则使用系统语言
      if (settings.language === 'system' || !settings.language) {
        return mapSystemLanguage(systemLang);
      }
      // 检查设置的语言是否在支持列表中
      return SUPPORTED_LANGUAGES.includes(settings.language)
        ? settings.language
        : 'en';
    } catch {
      // 文件不存在，使用系统语言
      return mapSystemLanguage(systemLang);
    }
  } catch (error) {
    console.error('读取设置失败:', error);
  }
  return 'en';
}

// 获取翻译文本 - 让它变成异步的
export async function getTranslation(
  key: string,
  lang?: string,
): Promise<string> {
  const language = lang || currentLanguage;
  const translations = await loadLanguageFile(language);

  if (!translations) {
    return key;
  }

  // 支持嵌套的 key，如 'settings.general'
  const keys = key.split('.');
  let result: unknown = translations;
  for (const k of keys) {
    if (result && typeof result === 'object' && k in result) {
      result = (result as Record<string, unknown>)[k];
    } else {
      return key;
    }
  }

  return typeof result === 'string' ? result : key;
}

// 为了向后兼容，保留同步版本，但内部使用缓存数据
export function t(key: string, lang?: string): string {
  const language = lang || currentLanguage;
  const translations =
    loadedTranslations[language] || loadedTranslations['en'] || {};

  if (!translations) {
    return key;
  }

  // 支持嵌套的 key，如 'settings.general'
  const keys = key.split('.');
  let result: unknown = translations;
  for (const k of keys) {
    if (result && typeof result === 'object' && k in result) {
      result = (result as Record<string, unknown>)[k];
    } else {
      return key;
    }
  }

  return typeof result === 'string' ? result : key;
}

// 切换语言
export async function changeLanguage(locale: string): Promise<boolean> {
  try {
    // 如果是 system，获取系统语言
    const actualLang =
      locale === 'system'
        ? mapSystemLanguage(app.getPreferredSystemLanguages()[0])
        : locale;

    const translations = await loadLanguageFile(actualLang);
    if (translations) {
      currentLanguage = actualLang;
      return true;
    }
    return false;
  } catch (error) {
    console.error(`更改语言失败: ${locale}`, error);
    // 如果更改失败，使用英文
    currentLanguage = 'en';
    return false;
  }
}

// 初始化语言设置
export async function initLanguage(): Promise<void> {
  try {
    const settingsLang = await getLanguageFromSettings();
    // 如果设置为 system，使用系统语言
    const actualLang =
      settingsLang === 'system'
        ? mapSystemLanguage(app.getPreferredSystemLanguages()[0])
        : settingsLang;

    // 确保语言文件存在
    const langFile = getLanguageFilePath(actualLang);
    try {
      await fs.promises.access(langFile);
      currentLanguage = actualLang;
      // 预加载当前语言
      await loadLanguageFile(actualLang);
    } catch {
      currentLanguage = 'en';
      // 预加载英文
      await loadLanguageFile('en');
    }
  } catch (error) {
    console.error('初始化语言设置失败:', error);
    currentLanguage = 'en';
    // 预加载英文
    await loadLanguageFile('en');
  }
}

// 获取当前语言
export function getCurrentLanguage(): string {
  return currentLanguage;
}

// 获取支持的语言列表
export function getSupportedLanguages(): string[] {
  return SUPPORTED_LANGUAGES;
}

// 获取语言文件路径
function getLocalesPath(): string {
  const isDev = process.env.NODE_ENV === 'development';

  return isDev
    ? path.join(__dirname, '..', 'src/common', 'locales')
    : path.join(process.resourcesPath, 'locales');
}

const i18n = {
  t,
  getTranslation,
  changeLanguage,
  initLanguage,
  getCurrentLanguage,
  getSupportedLanguages,
};

export default i18n;
