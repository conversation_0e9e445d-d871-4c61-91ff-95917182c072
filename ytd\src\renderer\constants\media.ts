import { FormatPlatformMap } from '@/types/setting';

// 字幕及音轨语言
export const SUBTITLE_LANGUAGES = [
  { value: 'en', label: 'English' },
  { value: 'zh', label: '中文' },
  { value: 'es', label: '<PERSON>spa<PERSON><PERSON>' },
  { value: 'fr', label: 'Français' },
  { value: 'de', label: 'Deutsch' },
  { value: 'ja', label: '日本語' },
  { value: 'ko', label: '한국어' },
  { value: 'ru', label: 'Русский' },
  { value: 'pt', label: 'Português' },
  { value: 'it', label: 'Italiano' },
  { value: 'nl', label: 'Nederlands' },
  { value: 'tr', label: 'Türkçe' },
  { value: 'pl', label: 'Polski' },
  { value: 'sv', label: 'Svenska' },
  { value: 'fi', label: 'Suomi' },
  { value: 'da', label: 'Dansk' },
  { value: 'no', label: 'Norsk' },
  { value: 'el', label: 'Ελληνικά' },
  { value: 'cs', label: '<PERSON><PERSON><PERSON><PERSON>' },
  { value: 'hu', label: '<PERSON><PERSON><PERSON>' },
  { value: 'uk', label: 'Українská' },
  { value: 'ro', label: 'Română' },
  { value: 'ar', label: 'العربية' },
  { value: 'hi', label: 'हिन्दी' },
  { value: 'th', label: 'ไทย' },
  { value: 'vi', label: 'Tiếng Việt' },
  { value: 'id', label: 'Bahasa Indonesia' },
  { value: 'ms', label: 'Bahasa Melayu' },
  { value: 'fil', label: 'Filipino' },
  { value: 'he', label: 'עברית' },
  { value: 'fa', label: 'فارسی' },
  { value: 'bn', label: 'বাংলা' },
  { value: 'ta', label: 'தமிழ்' },
  { value: 'te', label: 'తెలుగు' },
  { value: 'ur', label: 'اردو' },
] as const;

// 在 SUBTITLE_LANGUAGES 定义后添加音轨语言数组
export const AUDIO_TRACK_LANGUAGES = SUBTITLE_LANGUAGES.map(
  (lang) =>
    ({
      ...lang,
      value: `${lang.value}`, // 为音轨选项添加前缀以区分
    }) as const,
);

// 默认分辨率
export const DEFAULT_QUALITIES = [
  { label: '8K(4320p)', value: '4320' },
  { label: '4K(2160p)', value: '2160' },
  { label: '2K(1440p)', value: '1440' },
  { label: '1080p', value: '1080' },
  { label: '720p', value: '720' },
  { label: '480p', value: '480' },
  { label: '360p', value: '360' },
  { label: '240p', value: '240' },
] as const;

// 默认比特率
export const DEFAULT_BITRATE = [
  { label: '320k', value: '320' },
  { label: '256k', value: '256' },
  { label: '128k', value: '128' },
  { label: '64k', value: '64' },
] as const;

export const VIDEO_FORMATS = [
  { label: 'MP4', value: 'mp4' },
  { label: 'MKV', value: 'mkv' },
] as const;

export const AUDIO_FORMATS = [
  { label: 'MP3', value: 'mp3' },
  { label: 'M4A', value: 'm4a' },
  { label: 'OGG', value: 'ogg' },
] as const;

export const PLATFORMS = [
  { label: 'Windows', value: 'windows' },
  { label: 'macOS', value: 'macos' },
  { label: 'Linux', value: 'linux' },
  { label: 'iOS', value: 'ios' },
  { label: 'Android', value: 'android' },
] as const;

// 添加格式映射关系
export const FORMAT_PLATFORM_MAP: FormatPlatformMap = {
  video: {
    windows: 'mp4',
    macos: 'mp4',
    linux: 'mkv',
    ios: 'mp4',
    android: 'mp4',
  },
  audio: {
    windows: 'mp3',
    macos: 'm4a',
    linux: 'ogg',
    ios: 'm4a',
    android: 'mp3',
  },
};
