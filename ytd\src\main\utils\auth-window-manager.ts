import { CookieManager } from '@main/utils/cookie-manager';
import i18n from '@main/utils/i18n';
import * as Sentry from '@sentry/electron/main';
import { BrowserView, BrowserWindow, ipcMain, session } from 'electron';

export class AuthWindowManager {
  private static instance: AuthWindowManager | null = null;
  private authWindows: Map<string, BrowserWindow> = new Map();
  private authViews: Map<string, BrowserView> = new Map();
  private titleBarHeight = 32;
  private authHandlers: Map<
    string,
    {
      completedHandler: ((event: Electron.IpcMainEvent) => void) | null;
      closeHandler: ((event: Electron.IpcMainEvent) => void) | null;
    }
  > = new Map();
  private mainWindow: BrowserWindow | null = null;

  private constructor() {
    // 在构造函数中初始化 i18n
    i18n.initLanguage();
  }

  public static getInstance(): AuthWindowManager {
    if (!AuthWindowManager.instance) {
      AuthWindowManager.instance = new AuthWindowManager();
    }
    return AuthWindowManager.instance;
  }

  private getTitleBarHTML(): string {
    // 使用当前语言获取翻译
    const cancelText = i18n.t('auth.cancel');
    const doneText = i18n.t('auth.done');
    const loginToText = i18n.t('auth.loginTo');

    const titleBarHtml = `
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <style>
                    body {
                        margin: 0;
                        padding: 0;
                        height: 32px;
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        -webkit-app-region: drag;
                        background: transparent;
                        padding: 0 8px;
                    }
                    .left-section {
                        display: flex;
                        align-items: center;
                        gap: 8px;
                    }
                    .site-title {
                        color: #333333;
                        font-size: 13px;
                        margin-right: 8px;
                    }
                    .right-section {
                        display: flex;
                        align-items: center;
                    }
                    .titlebar-button {
                        height: 24px;
                        min-width: 48px;
                        background: transparent;
                        border: none;
                        cursor: pointer;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        -webkit-app-region: no-drag;
                        color: #666666;
                        margin-left: 8px;
                        border-radius: 4px;
                        font-size: 12px;
                        padding: 0 12px;
                    }
                    .refresh-button {
                        width: 24px;
                        min-width: unset;
                        padding: 0;
                        border: 1px solid #d9d9d9;
                    }
                    .refresh-button:hover {
                        background: rgba(0, 0, 0, 0.04);
                    }
                    .refresh-icon {
                        width: 14px;
                        height: 14px;
                        fill: #666666;
                    }
                    #complete-btn {
                        color: #1890ff;
                        border: 1px solid #1890ff;
                    }
                    #complete-btn:hover {
                        background: rgba(24, 144, 255, 0.1);
                    }
                    #close-btn {
                        border: 1px solid #666666;
                    }
                    #close-btn:hover {
                        background: #ff4d4f;
                        color: #ffffff;
                        border-color: #ff4d4f;
                    }
                </style>
            </head>
            <body>
                <div class="left-section">
                    <span class="site-title">${loginToText} <span id="site-domain"></span></span>
                    <button id="refresh-btn" class="titlebar-button refresh-button" title="刷新">
                        <svg class="refresh-icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg">
                            <path d="M512 922.666667C276.352 922.666667 85.333333 731.648 85.333333 496S276.352 69.333333 512 69.333333c235.648 0 426.666667 191.018667 426.666667 426.666667 0 23.552-19.114667 42.666667-42.666667 42.666667s-42.666667-19.114667-42.666667-42.666667c0-188.522667-152.810667-341.333333-341.333333-341.333333S170.666667 307.477333 170.666667 496s152.810667 341.333333 341.333333 341.333333c94.261333 0 179.52-38.186667 241.344-99.84L637.866667 622.037333A42.624 42.624 0 0 1 640 597.333333c0-23.552 19.114667-42.666667 42.666667-42.666666h256c23.552 0 42.666667 19.114667 42.666666 42.666666v256c0 23.552-19.114667 42.666667-42.666666 42.666667s-42.666667-19.114667-42.666667-42.666667V720.853333C827.392 846.016 678.997333 922.666667 512 922.666667z"/>
                        </svg>
                    </button>
                </div>
                <div class="right-section">
                    <button id="close-btn" class="titlebar-button">${cancelText}</button>
                    <button id="complete-btn" class="titlebar-button">${doneText}</button>
                </div>
                <script>
                    const { ipcRenderer } = require('electron');
                    
                    // 获取当前网站域名并显示
                    ipcRenderer.on('update-site-domain', (event, domain) => {
                        document.getElementById('site-domain').textContent = domain;
                    });

                    document.getElementById('complete-btn').onclick = () => {
                        ipcRenderer.send('auth-completed');
                    };
                    document.getElementById('close-btn').onclick = () => {
                        ipcRenderer.send('auth-close');
                    };
                    document.getElementById('refresh-btn').onclick = () => {
                        ipcRenderer.send('auth-refresh');
                    };
                </script>
            </body>
            </html>
        `;
    return titleBarHtml;
  }

  private async verifyLogin(
    url: string,
    cookies: Electron.Cookie[],
  ): Promise<boolean> {
    // 检查是否包含关键的登录cookie
    const domain = new URL(url).hostname.replace(/^www\./, '');

    if (domain.includes('youtube.com')) {
      return cookies.some(
        (cookie) =>
          cookie.name === 'LOGIN_INFO' ||
          cookie.name === 'APISID' ||
          cookie.name === 'SID',
      );
    }

    if (domain.includes('instagram.com')) {
      return cookies.some(
        (cookie) => cookie.name === 'sessionid' || cookie.name === 'ds_user_id',
      );
    }

    if (domain.includes('x.com') || domain.includes('twitter.com')) {
      return cookies.some(
        (cookie) => cookie.name === 'auth_token' || cookie.name === 'twid',
      );
    }

    return cookies.length > 0;
  }

  public async openAuthWindow(url: string, siteKey: string): Promise<void> {
    try {
      // 如果已经有该站点的窗口，先关闭
      if (this.authWindows.has(siteKey)) {
        this.closeAuthWindow(siteKey);
      }

      const authPromise = new Promise((resolve, reject) => {
        (async () => {
          const handlers = {
            completedHandler: async () => {
              try {
                const cookies = await session.defaultSession.cookies.get({});
                // console.log(cookies);

                // 验证是否真正登录
                const isLoggedIn = await this.verifyLogin(url, cookies);

                if (isLoggedIn) {
                  await CookieManager.getInstance().saveSiteCookies(
                    url,
                    cookies,
                  );
                  // 确保在关闭窗口前移除事件监听器
                  this.cleanupListeners();
                  // 先关闭窗口
                  const window = this.authWindows.get(siteKey);
                  if (window && !window.isDestroyed()) {
                    window.close();
                  }
                  this.authWindows.delete(siteKey);
                  this.authViews.delete(siteKey);
                  // 最后解析 Promise
                  resolve(cookies);
                } else {
                  // 关闭窗口
                  this.closeAuthWindow(siteKey);
                  reject('你好像还没有登录哦~');
                }
              } catch (error) {
                Sentry.captureException('未获取到登录信息:' + error);
                reject(error);
              }
            },
            closeHandler: () => {
              this.cleanupListeners();
              const window = this.authWindows.get(siteKey);
              if (window && !window.isDestroyed()) {
                window.close();
              }
              this.authWindows.delete(siteKey);
              this.authViews.delete(siteKey);
              reject(new Error(i18n.t('auth.cancelLogin')));
            },
          };

          const isDev = process.env.NODE_ENV === 'development';
          // const isDev = true;

          // 创建主窗口
          const authWindow = new BrowserWindow({
            width: 800,
            height: 640,
            frame: false,
            parent: this.mainWindow!,
            modal: true,
            titleBarStyle: 'hidden',
            titleBarOverlay: false,
            minimizable: false,
            minHeight: 550,
            minWidth: 450,
            webPreferences: {
              nodeIntegration: true,
              contextIsolation: false,
              devTools: isDev,
              webviewTag: isDev,
            },
          });
          // 监听取消事件, 取消后关闭窗口, 并移除事件监听器,返回取消
          ipcMain.once('auth-close', () => {
            handlers.closeHandler();
            authWindow.close();
          });
          // 创建标题栏视图
          const titleBarView = new BrowserView({
            webPreferences: {
              nodeIntegration: true,
              contextIsolation: false,
              devTools: isDev,
              webviewTag: isDev,
            },
          });
          authWindow.addBrowserView(titleBarView);
          titleBarView.setBounds({
            x: 0,
            y: 0,
            width: 800,
            height: this.titleBarHeight,
          });
          titleBarView.setAutoResize({ width: true, height: false });

          // 加载标题栏HTML
          await titleBarView.webContents.loadURL(
            `data:text/html;charset=utf-8,${encodeURIComponent(this.getTitleBarHTML())}`,
          );

          // 确保URL格式正确
          if (!url.startsWith('http://') && !url.startsWith('https://')) {
            url = 'https://' + url;
          }

          // 立即发送域名信息
          const domain = new URL(url).hostname;
          titleBarView.webContents.send('update-site-domain', domain);

          // 创建加载视图
          const loadingView = new BrowserView({
            webPreferences: {
              nodeIntegration: true,
              contextIsolation: false,
            },
          });
          authWindow.addBrowserView(loadingView);
          loadingView.setBounds({
            x: 0,
            y: this.titleBarHeight,
            width: 800,
            height: 640 - this.titleBarHeight,
          });
          loadingView.setAutoResize({ width: true, height: true });

          // 加载loading效果
          const loadingHTML = `
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <style>
                            body {
                                margin: 0;
                                display: flex;
                                justify-content: center;
                                align-items: center;
                                height: 100vh;
                                background: #ffffff;
                            }
                            .loading {
                                width: 50px;
                                height: 50px;
                                border: 5px solid #f3f3f3;
                                border-top: 5px solid #1890ff;
                                border-radius: 50%;
                                animation: spin 1s linear infinite;
                            }
                            @keyframes spin {
                                0% { transform: rotate(0deg); }
                                100% { transform: rotate(360deg); }
                            }
                        </style>
                    </head>
                    <body>
                        <div class="loading"></div>
                    </body>
                    </html>
                `;
          await loadingView.webContents.loadURL(
            `data:text/html;charset=utf-8,${encodeURIComponent(loadingHTML)}`,
          );

          // 修改错误视图的 HTML 模板
          const getErrorHTML = (url: string, errorDescription: string) => `
                    <!DOCTYPE html>
                    <html dir="ltr" lang="zh">
                    <head>
                        <meta charset="utf-8">
                        <meta name="color-scheme" content="light dark">
                        <meta name="theme-color" content="#fff">
                        <meta name="viewport" content="width=device-width, initial-scale=1.0">
                        <title>${url}</title>
                        <style>
                            body {
                                --background-color: #fff;
                                --error-code-color: var(--google-gray-700);
                                --heading-color: var(--google-gray-900);
                                --link-color: rgb(88, 88, 88);
                                --text-color: var(--google-gray-700);
                                background: var(--background-color);
                                color: var(--text-color);
                                word-wrap: break-word;
                                margin: 0;
                                padding: 0 20px;
                                font-family: 'Segoe UI', Arial, 'Microsoft Yahei', sans-serif;
                                font-size: 75%;
                            }

                            .interstitial-wrapper {
                                box-sizing: border-box;
                                font-size: 1em;
                                line-height: 1.6em;
                                margin: 14vh auto 0;
                                max-width: 600px;
                                width: 100%;
                            }

                            .icon {
                                height: 72px;
                                margin: 0 0 40px;
                                width: 72px;
                                -webkit-user-select: none;
                                content: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEgAAABIAQMAAABvIyEEAAAABlBMVEUAAABTU1OoaSf/AAAAAXRSTlMAQObYZgAAAENJREFUeF7tzbEJACEQRNGBLeAasBCza2lLEGx0CxFGG9hBMDDxRy/72O9FMnIFapGylsu1fgoBdkXfUHLrQgdfrlJN1BdYBjQQm3UAAAAASUVORK5CYII=);
                            }

                            h1 {
                                color: var(--heading-color);
                                font-size: 1.6em;
                                font-weight: normal;
                                line-height: 1.25em;
                                margin-bottom: 16px;
                            }

                            .error-code {
                                color: var(--error-code-color);
                                margin-top: 12px;
                                font-size: 0.8em;
                            }

                            .error-details {
                                margin: 16px 0;
                                color: var(--text-color);
                            }

                            .suggestions {
                                margin-top: 16px;
                                color: var(--text-color);
                            }

                            .suggestion-list {
                                padding-left: 20px;
                                margin: 8px 0 0;
                                line-height: 1.8;
                            }

                            @media (prefers-color-scheme: dark) {
                                body {
                                    --background-color: var(--google-gray-900);
                                    --error-code-color: var(--google-gray-500);
                                    --heading-color: var(--google-gray-500);
                                    --link-color: var(--google-blue-300);
                                    --text-color: var(--google-gray-500);
                                }
                            }
                        </style>
                    </head>
                    <body>
                        <div class="interstitial-wrapper">
                            <div class="icon"></div>
                            <h1>无法访问此网站</h1>
                            <div class="error-details">${url}</div>
                            <div class="error-details"></div>
                            <div class="suggestions">
                                请尝试：
                                <ul class="suggestion-list">
                                    <li>检查网络连接</li>
                                    <li>检查是否有拼写错误</li>
                                    <li>检查防火墙或代理设置</li>
                                </ul>
                            </div>
                            <div class="error-code">${errorDescription ?? 'ERR_FAILED'}</div>
                        </div>
                    </body>
                    </html>


                `;

          // 502错误视图
          /*
                该网页无法正常运作
                1.qweqwe.com 目前无法处理此请求。
                HTTP ERROR 502
                */
          const get502HTML = (url: string) => `
                    <!DOCTYPE html>
                    <html dir="ltr" lang="zh">
                    <head>
                        <meta charset="utf-8">
                        <meta name="color-scheme" content="light dark">
                        <meta name="theme-color" content="#fff">
                        <meta name="viewport" content="width=device-width, initial-scale=1.0">
                        <title>${url}</title>
                        <style>
                            body {
                                --background-color: #fff;
                                --error-code-color: var(--google-gray-700);
                                --heading-color: var(--google-gray-900);
                                --link-color: rgb(88, 88, 88);
                                --text-color: var(--google-gray-700);
                                background: var(--background-color);
                                color: var(--text-color);
                                word-wrap: break-word;
                                margin: 0;
                                padding: 0 20px;
                                font-family: 'Segoe UI', Arial, 'Microsoft Yahei', sans-serif;
                                font-size: 75%;
                            }

                            .interstitial-wrapper {
                                box-sizing: border-box;
                                font-size: 1em;
                                line-height: 1.6em;
                                margin: 14vh auto 0;
                                max-width: 600px;
                                width: 100%;
                            }

                            .icon {
                                height: 72px;
                                margin: 0 0 40px;
                                width: 72px;
                                -webkit-user-select: none;
                                content: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEgAAABIAQMAAABvIyEEAAAABlBMVEUAAABTU1OoaSf/AAAAAXRSTlMAQObYZgAAAENJREFUeF7tzbEJACEQRNGBLeAasBCza2lLEGx0CxFGG9hBMDDxRy/72O9FMnIFapGylsu1fgoBdkXfUHLrQgdfrlJN1BdYBjQQm3UAAAAASUVORK5CYII=);
                            }

                            h1 {
                                color: var(--heading-color);
                                font-size: 1.6em;
                                font-weight: normal;
                                line-height: 1.25em;
                                margin-bottom: 16px;
                            }

                            .error-code {
                                color: var(--error-code-color);
                                margin-top: 12px;
                                font-size: 0.8em;
                            }

                            .error-details {
                                margin: 16px 0;
                                color: var(--text-color);
                            }

                            .suggestions {
                                margin-top: 16px;
                                color: var(--text-color);
                            }

                            .suggestion-list {
                                padding-left: 20px;
                                margin: 8px 0 0;
                                line-height: 1.8;
                            }

                            @media (prefers-color-scheme: dark) {
                                body {
                                    --background-color: var(--google-gray-900);
                                    --error-code-color: var(--google-gray-500);
                                    --heading-color: var(--google-gray-500);
                                    --link-color: var(--google-blue-300);
                                    --text-color: var(--google-gray-500);
                                }
                            }
                        </style>
                    </head>
                    <body>
                        <div class="interstitial-wrapper">
                            <div class="icon"></div>
                            <h1>该网页无法正常运作</h1>
                            <div class="error-details"><b>${url}</b> 目前无法处理此请求。</div>
                            <div class="error-details">HTTP ERROR 502</div>

                        </div>
                    </body>
                    </html>

                `;

          // 创建错误视图但不立即添加到窗口

          const errorView = new BrowserView({
            webPreferences: {
              nodeIntegration: true,
              contextIsolation: false,
            },
          });
          // 设置错误视图的边界
          errorView.setBounds({
            x: 0,
            y: this.titleBarHeight,
            width: 800,
            height: 640 - this.titleBarHeight,
          });
          errorView.setAutoResize({ width: true, height: true });
          // 预加载错误视图的内容，但不添加到窗口
          await errorView.webContents.loadURL(
            `data:text/html;charset=utf-8,${encodeURIComponent(getErrorHTML('', ''))}`,
          );

          // 创建内容视图
          const authView = new BrowserView({
            webPreferences: {
              nodeIntegration: false,
              contextIsolation: true,
              webSecurity: false,
              allowRunningInsecureContent: true,
              devTools: isDev,
              webviewTag: isDev,
            },
          });

          // 先隐藏内容视图
          authWindow.addBrowserView(authView);
          authView.setBounds({
            x: 0,
            y: this.titleBarHeight,
            width: 800,
            height: 640 - this.titleBarHeight,
          });
          authView.setAutoResize({ width: true, height: true });

          // 修改 did-finish-load 事件处理
          authView.webContents.once('did-finish-load', () => {
            // 页面加载成功时，移除 loading 视图
            if (loadingView) {
              authWindow.removeBrowserView(loadingView);
            }
            if (errorView) {
              authWindow.removeBrowserView(errorView);
            }
          });

          // 在创建内容视图后，添加响应状态码监听
          authView.webContents.session.webRequest.onResponseStarted(
            { urls: ['<all_urls>'] },
            (details) => {
              if (details.statusCode === 502) {
                // 显示 502 错误页面
                const errorHTML = get502HTML(url);
                errorView.webContents
                  .loadURL(
                    `data:text/html;charset=utf-8,${encodeURIComponent(errorHTML)}`,
                  )
                  .then(() => {
                    if (loadingView) {
                      authWindow.removeBrowserView(loadingView);
                    }
                    if (authView) {
                      authWindow.removeBrowserView(authView);
                    }
                    authWindow.addBrowserView(errorView);
                  });

                Sentry.captureException(
                  `页面加载失败: HTTP ${details.statusCode} Bad Gateway`,
                );
                console.log('页面加载失败: HTTP 502 Bad Gateway');
              }
            },
          );

          // 修改错误处理函数
          const handleLoadError = async (
            errorView: BrowserView,
            loadingView: BrowserView,
            authView: BrowserView,
            authWindow: BrowserWindow,
            url: string,
            errorDescription: string,
          ) => {
            // 根据错误代码判断显示哪种错误视图
            let errorHTML;
            // 只有特定的连接错误才显示 502 错误
            if (
              // errorDescription.includes('ERR_PROXY_CONNECTION_FAILED') ||
              // errorDescription.includes('ERR_CONNECTION_REFUSED') ||
              // errorDescription.includes('ERR_EMPTY_RESPONSE') ||
              errorDescription.includes('502')
            ) {
              // 添加对 502 状态码的检查
              errorHTML = get502HTML(url);
            } else {
              // 其他所有错误(包括超时)都显示普通错误视图
              const errorMessage = errorDescription;
              // 为常见错误提供更友好的中文说明
              // if (errorDescription.includes('ERR_CONNECTION_TIMED_OUT')) {
              //     errorMessage = '连接超时，请检查您的网络连接或代理设置';
              // } else if (errorDescription.includes('ERR_NAME_NOT_RESOLVED')) {
              //     errorMessage = '无法解析域名，请检查您的网络连接或DNS设置';
              // } else if (errorDescription.includes('ERR_INTERNET_DISCONNECTED')) {
              //     errorMessage = '网络连接已断开，请检查您的网络设置';
              // }
              errorHTML = getErrorHTML(url, errorMessage);
            }

            // 更新错误视图内容
            await errorView.webContents.loadURL(
              `data:text/html;charset=utf-8,${encodeURIComponent(errorHTML)}`,
            );

            // 移除 loading 视图
            if (loadingView) {
              authWindow.removeBrowserView(loadingView);
            }

            // 不完全移除 authView，隐藏
            if (authView) {
              authView.setBounds({ x: 0, y: 0, width: 0, height: 0 });
            }

            // 显示错误视图
            authWindow.addBrowserView(errorView);

            // 记录错误日志
            Sentry.captureException(`页面加载失败: ${errorDescription}`);
            console.log('页面加载失败: ', errorDescription);
          };

          // 修改加载失败事件处理
          authView.webContents.on(
            'did-fail-load',
            async (event, errorCode, errorDescription, url) => {
              handleLoadError(
                errorView,
                loadingView,
                authView,
                authWindow,
                url,
                errorDescription,
              );
            },
          );

          // 添加 did-fail-provisional-load 事件处理
          authView.webContents.on(
            'did-fail-provisional-load',
            async (event, errorCode, errorDescription, url) => {
              handleLoadError(
                errorView,
                loadingView,
                authView,
                authWindow,
                url,
                errorDescription,
              );
            },
          );

          // 加载目标URL
          await authView.webContents.loadURL(url);
          // await authView.webContents.loadURL('www.baidu.com');

          if (isDev) {
            authView.webContents.openDevTools({ mode: 'detach' });
            titleBarView.webContents.openDevTools({ mode: 'detach' });
          }

          if (!isDev) {
            authWindow.removeMenu();
          }

          // 监听窗口关闭事件
          let isCompleted = false;
          let isCancelled = false;

          // 修改 completedHandler，标记完成状态
          const originalCompletedHandler = handlers.completedHandler;
          handlers.completedHandler = async () => {
            isCompleted = true;
            await originalCompletedHandler();
          };

          // 修改 closeHandler，标记取消状态
          const originalCloseHandler = handlers.closeHandler;
          handlers.closeHandler = () => {
            isCancelled = true;
            originalCloseHandler();
          };

          // 监听窗口关闭事件
          authWindow.on('closed', () => {
            // 只有在非完成且非取消状态时才执行取消逻辑
            if (!isCompleted && !isCancelled) {
              this.cleanupListeners();
              this.authWindows.delete(siteKey);
              this.authViews.delete(siteKey);
              reject(new Error(i18n.t('auth.cancelLogin')));
            }
          });

          this.authWindows.set(siteKey, authWindow);
          this.authViews.set(siteKey, authView);

          // 在所有窗口和视图设置完成后，再设置事件监听器
          this.authHandlers.set(siteKey, handlers);
          ipcMain.once('auth-completed', handlers.completedHandler);

          // 刷新事件处理
          const refreshHandler = () => {
            const currentView = authView;
            if (currentView && !currentView.webContents.isDestroyed()) {
              // 恢复 authView 的尺寸
              currentView.setBounds({
                x: 0,
                y: this.titleBarHeight,
                width: 800,
                height: 640 - this.titleBarHeight,
              });
              // 重新加载页面
              currentView.webContents.reload();
              // 移除错误视图
              if (errorView) {
                authWindow.removeBrowserView(errorView);
              }
            }
          };

          // 添加刷新事件监听
          ipcMain.on('auth-refresh', refreshHandler);

          // 在窗口关闭时清理刷新事件监听器
          authWindow.on('closed', () => {
            // 移除刷新事件监听器
            ipcMain.removeListener('auth-refresh', refreshHandler);

            // 原有的关闭逻辑
            if (!isCompleted && !isCancelled) {
              this.cleanupListeners();
              this.authWindows.delete(siteKey);
              this.authViews.delete(siteKey);
              reject(new Error(i18n.t('auth.cancelLogin')));
            }
          });
        })();
      });

      return authPromise as Promise<void>;
    } catch (error) {
      Sentry.captureException('授权窗口错误:' + error);
      this.cleanupListeners();
      throw error;
    }
  }

  private cleanupListeners(): void {
    ipcMain.removeAllListeners('auth-completed');
    ipcMain.removeAllListeners('auth-close');
    ipcMain.removeAllListeners('auth-refresh');
  }

  public closeAuthWindow(siteKey: string): void {
    const window = this.authWindows.get(siteKey);
    if (window) {
      this.cleanupListeners();
      window.close();
      this.authWindows.delete(siteKey);
      this.authViews.delete(siteKey);
    }
  }

  public async removeAuth(url: string): Promise<void> {
    await CookieManager.getInstance().removeSiteCookies(url);
  }

  public setMainWindow(window: BrowserWindow) {
    this.mainWindow = window;
  }
}
