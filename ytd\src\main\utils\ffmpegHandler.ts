import { FFmpegProgress } from '@common/types/download';
import * as Sentry from '@sentry/electron/main';
import { app } from 'electron';
import ffmpeg from 'fluent-ffmpeg';
import * as fs from 'fs';
import * as path from 'path';

// 获取设置 loadSettings
import { Downloader } from './downloader';

interface FFprobeStream {
  index: number;
  codec_type?: string;
  codec_name?: string;
  width?: number;
  height?: number;
  r_frame_rate?: string;
  channels?: number;
  sample_rate?: number;
  tags?: {
    language?: string;
  };
}

interface FFprobeData {
  streams: FFprobeStream[];
  format: {
    duration?: string | number;
    [key: string]: unknown;
  };
}

interface FFmpegCommandOptions {
  inputOptions?: string[];
  outputOptions?: string[];
  streamMappings?: string[];
}

// 定义额外的类型用于 processDirectory
interface StreamInfo {
  file: string;
  index: number;
  codec: string;
  language: string;
  duration?: number;
  resolution?: string;
  fps?: string;
  channels?: number;
  sample_rate?: number;
  type?: string;
}

interface ProcessMediaInfo {
  video: StreamInfo[];
  audio: StreamInfo[];
  subtitle: StreamInfo[];
  image: string[];
}

// 语言代码映射表 (ISO 639-2)
const LANGUAGE_CODES: { [key: string]: string } = {
  // 完整语言代码映射
  'zh-hans': 'chi',
  'zh-hant': 'chi',
  'zh-cn': 'chi',
  'zh-tw': 'chi',
  'zh-hk': 'chi',
  'zh-sg': 'chi',
  'zh-mo': 'chi',
  'en-us': 'eng',
  'en-gb': 'eng',
  'en-ca': 'eng',
  'en-au': 'eng',
  'ja-jp': 'jpn',
  'ko-kr': 'kor',
  'fr-fr': 'fra',
  'fr-ca': 'fra',
  'de-de': 'deu',
  'es-es': 'spa',
  'es-mx': 'spa',
  'it-it': 'ita',
  'ru-ru': 'rus',
  'pt-pt': 'por',
  'pt-br': 'por',
  'ar-sa': 'ara',
  'hi-in': 'hin',
  'th-th': 'tha',
  'vi-vn': 'vie',

  // 简单语言代码映射
  zh: 'chi',
  en: 'eng',
  ja: 'jpn',
  ko: 'kor',
  fr: 'fra',
  de: 'deu',
  es: 'spa',
  it: 'ita',
  ru: 'rus',
  pt: 'por',
  ar: 'ara',
  hi: 'hin',
  th: 'tha',
  vi: 'vie',
  nl: 'dut',
  sv: 'swe',
  no: 'nor',
  fi: 'fin',
  da: 'dan',
  pl: 'pol',
  tr: 'tur',
  cs: 'cze',
  hu: 'hun',
  el: 'gre',
  he: 'heb',
  id: 'ind',
  ms: 'may',
  ro: 'rum',
  uk: 'ukr',
  bg: 'bul',
  hr: 'hrv',
  sr: 'srp',
  sk: 'slo',
  sl: 'slv',
  et: 'est',
  lv: 'lav',
  lt: 'lit',
  fa: 'per',
  ur: 'urd',
  bn: 'ben',
  ta: 'tam',
  te: 'tel',
  ml: 'mal',
  kn: 'kan',
  mr: 'mar',
  gu: 'guj',
  pa: 'pan',
  si: 'sin',
  km: 'khm',
  lo: 'lao',
  my: 'bur',
  am: 'amh',
  sw: 'swa',
  af: 'afr',
  fil: 'fil', // 菲律宾语
  und: 'und',
};

// 语言显示名称映射表
const LANGUAGE_NAMES: { [key: string]: string } = {
  // 中文
  'zh-hans': '简体中文',
  'zh-hant': '繁体中文',
  'zh-cn': '简体中文',
  'zh-tw': '繁体中文',
  'zh-hk': '繁体中文(香港)',
  'zh-sg': '简体中文(新加坡)',
  'zh-mo': '繁体中文(澳门)',
  zh: '中文',

  // 英语
  'en-us': 'English_(US)',
  'en-gb': 'English_(UK)',
  'en-ca': 'English_(Canada)',
  'en-au': 'English_(Australia)',
  en: 'English',

  // 日语
  'ja-jp': '日本語',
  ja: '日本語',

  // 韩语
  'ko-kr': '한국어',
  ko: '한국어',

  // 法语
  'fr-fr': 'Français_(France)',
  'fr-ca': 'Français_(Canada)',
  fr: 'Français',

  // 德语
  'de-de': 'Deutsch',
  de: 'Deutsch',

  // 西班牙语
  'es-es': 'Español_(España)',
  'es-mx': 'Español_(México)',
  es: 'Español',

  // 意大利语
  'it-it': 'Italiano',
  it: 'Italiano',

  // 俄语
  'ru-ru': 'Русский',
  ru: 'Русский',

  // 葡萄牙语
  'pt-pt': 'Português_(Portugal)',
  'pt-br': 'Português_(Brasil)',
  pt: 'Português',

  // 阿拉伯语
  'ar-sa': 'العربية',
  ar: 'العربية',

  // 印地语
  'hi-in': 'हिन्दी',
  hi: 'हिन्दी',

  // 泰语
  'th-th': 'ไทย',
  th: 'ไทย',

  // 越南语
  'vi-vn': 'Tiếng Việt',
  vi: 'Tiếng Việt',

  // 其他语言
  nl: 'Nederlands',
  sv: 'Svenska',
  no: 'Norsk',
  fi: 'Suomi',
  da: 'Dansk',
  pl: 'Polski',
  tr: 'Türkçe',
  cs: 'Čeština',
  hu: 'Magyar',
  el: 'Ελληνικά',
  he: 'עברית',
  id: 'Bahasa Indonesia',
  ms: 'Bahasa Melayu',
  ro: 'Română',
  uk: 'Українська',
  bg: 'Български',
  hr: 'Hrvatski',
  sr: 'Српски',
  sk: 'Slovenčina',
  sl: 'Slovenščina',
  et: 'Eesti',
  lv: 'Latviešu',
  lt: 'Lietuvių',
  fa: 'فارسی',
  ur: 'اردو',
  bn: 'বাংলা',
  ta: 'தமிழ்',
  te: 'తెలుగు',
  ml: 'മലയാളം',
  kn: 'ಕನ್ನಡ',
  mr: 'मराठी',
  gu: 'ગુજરાતી',
  pa: 'ਪੰਜਾਬੀ',
  si: 'සිංහල',
  km: 'ខ្មែរ',
  lo: 'ລາວ',
  my: 'မြန်မာ',
  am: 'አማርኛ',
  sw: 'Kiswahili',
  af: 'Afrikaans',
  fil: 'Filipino', // 菲律宾语

  // 默认
  und: 'Unknown',
};

/**
 * 获取ISO 639-2标准的三位字母语言代码
 * @param language 语言标识，可以是完整的语言代码或部分语言代码
 * @returns ISO 639-2标准的三位字母语言代码
 */
function getLanguageCode(language: string): string {
  if (!language) return 'und';

  // 转换为小写以进行匹配
  const lowerLang = language.toLowerCase();

  // 1. 尝试完整匹配
  if (LANGUAGE_CODES[lowerLang]) {
    return LANGUAGE_CODES[lowerLang];
  }

  // 2. 尝试匹配主要语言部分（如果有连字符）
  const parts = lowerLang.split('-');
  if (parts.length > 1 && LANGUAGE_CODES[parts[0]]) {
    return LANGUAGE_CODES[parts[0]];
  }

  // 3. 尝试匹配每个部分
  for (const part of parts) {
    if (LANGUAGE_CODES[part]) {
      return LANGUAGE_CODES[part];
    }
  }

  // 4. 默认返回未定义
  return 'und';
}

export class FFmpegHandler {
  private static instance: FFmpegHandler | null = null;
  private ffmpegPath: string;
  private ffprobePath: string;
  // 添加一个Map来存储taskId与ffmpeg进程的映射关系
  private ffmpegProcesses: Map<string, ffmpeg.FfmpegCommand[]> = new Map();

  private constructor() {
    // 获取 ffmpeg 和 ffprobe 路径
    this.ffmpegPath = this.getResourcePath('ffmpeg');
    // this.ffmpegPath = 'D:\\Download\\demodownload\\ffmpeg测试\\ffmpeg4.exe';
    this.ffprobePath = this.getResourcePath('ffprobe');
    console.log('ffmpegPath:', this.ffmpegPath);
    console.log('ffprobePath:', this.ffprobePath);

    // 设置 ffmpeg 路径
    ffmpeg.setFfmpegPath(this.ffmpegPath);
    ffmpeg.setFfprobePath(this.ffprobePath);
  }

  // 获取ffmpeg、ffprobe资源路径
  private getResourcePath(toolName: string): string {
    const isDev = process.env.NODE_ENV === 'development';
    const platform = process.platform;
    const executable = platform === 'win32' ? `${toolName}.exe` : toolName;

    if (isDev) {
      // 开发环境路径
      return path.join(
        __dirname,
        '..',
        'public',
        'bin',
        platform === 'win32' ? executable : `${toolName}-${process.arch}`,
      );
    }

    // 生产环境路径
    const basePath = app.isPackaged
      ? path.join(process.resourcesPath, 'app.asar.unpacked')
      : path.join(__dirname, '..', '..');

    return path.join(basePath, 'public', 'bin', executable);
  }

  // 获取单例
  public static getInstance(): FFmpegHandler {
    if (!FFmpegHandler.instance) {
      FFmpegHandler.instance = new FFmpegHandler();
    }
    return FFmpegHandler.instance;
  }

  // 转换音频格式
  public async convertAudio(
    inputPath: string,
    outputPath: string,
    format: string,
    quality: string,
    onProgress?: (progress: FFmpegProgress) => void,
    onError?: (error: Error) => void,
    taskId?: string,
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        const command = ffmpeg(inputPath);

        // 如果提供了taskId，则注册该ffmpeg进程
        if (taskId) {
          this.registerProcess(taskId, command);
        }

        // 设置音频编码器和比特率
        switch (format) {
          case 'mp3':
            command.audioCodec('libmp3lame').audioBitrate(quality);
            break;
          case 'm4a':
            command.audioCodec('aac').audioBitrate(quality);
            break;
          case 'ogg':
            command.audioCodec('libvorbis').audioBitrate(quality);
            break;
        }

        command
          .on('progress', (progress: FFmpegProgress) => {
            if (onProgress) {
              onProgress(progress);
            }
          })
          .on('end', () => {
            resolve();
          })
          .on('error', (err: Error) => {
            console.error('音频转换失败:', err);
            Sentry.captureException('音频转换失败:' + err);
            if (onError) {
              onError(err);
            }
            reject(err);
          })
          .save(outputPath);
      } catch (error) {
        console.error('音频转换时发生错误:', error);
        Sentry.captureException('音频转换时发生错误:' + error);
        reject(error);
      }
    });
  }

  // 转换视频格式
  public async convertVideo(
    inputPath: string,
    outputPath: string,
    format: string,
    onProgress?: (progress: FFmpegProgress) => void,
    onError?: (error: Error) => void,
    taskId?: string,
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        const command = ffmpeg(inputPath);

        // 如果提供了taskId，则注册该ffmpeg进程
        if (taskId) {
          this.registerProcess(taskId, command);
        }

        // 设置视频编码器和质量
        switch (format) {
          case 'mp4':
            // mp4 使用 libx264 编码, 音频使用 aac 编码
            command.videoCodec('libx264').outputOptions(['-c:a', 'aac']);
            break;
          case 'mkv':
            // mkv 使用 copy 编码
            command.outputOptions(['-c:v', 'copy', '-c:a', 'copy']);
            break;
          default:
            // 对于其他格式，保持原始编码
            command.outputOptions(['-c:v', 'copy', '-c:a', 'copy']);
            break;
        }

        // 保持字幕流
        command.outputOptions(['-c:s', 'copy']);

        command
          .on('progress', (progress: FFmpegProgress) => {
            if (onProgress) {
              onProgress(progress);
            }
          })
          .on('end', () => {
            resolve();
          })
          .on('error', (err: Error) => {
            console.error('视频转换失败:', err);
            Sentry.captureException('视频转换失败:' + err);
            if (onError) {
              onError(err);
            }
            reject(err);
          })
          .save(outputPath);
      } catch (error) {
        console.error('视频转换时发生错误:', error);
        Sentry.captureException('视频转换时发生错误:' + error);
        reject(error);
      }
    });
  }

  // 合并音视频
  public async mergeAudioVideo(
    videoPath: string,
    audioPath: string,
    outputPath: string,
    onProgress?: (progress: FFmpegProgress) => void,
    onError?: (error: Error) => void,
    taskId?: string,
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        const command = ffmpeg().input(videoPath).input(audioPath);

        // 如果提供了taskId，则注册该ffmpeg进程
        if (taskId) {
          this.registerProcess(taskId, command);
        }

        command
          .outputOptions([
            '-c:v',
            'copy',
            '-c:a',
            'aac',
            '-strict',
            'experimental',
          ])
          .on('progress', (progress: FFmpegProgress) => {
            if (onProgress) {
              onProgress(progress);
            }
          })
          .on('end', () => {
            resolve();
          })
          .on('error', (err: Error) => {
            console.error('音视频合并失败:', err);
            Sentry.captureException('音视频合并失败:' + err);
            if (onError) {
              onError(err);
            }
            reject(err);
          })
          .save(outputPath);
      } catch (error) {
        console.error('音视频合并时发生错误:', error);
        Sentry.captureException('音视频合并时发生错误:' + error);
        reject(error);
      }
    });
  }

  // 检查文件是否为图片
  private async isImageFile(filePath: string): Promise<boolean> {
    try {
      // 获取文件扩展名并转为小写
      const ext = path.extname(filePath).toLowerCase();

      // 常见图片格式的扩展名列表
      const imageExtensions = [
        '.jpg',
        '.jpeg',
        '.png',
        '.gif',
        '.bmp',
        '.webp',
        '.tif',
        '.tiff',
        '.heic',
        '.heif',
        '.avif',
      ];
      return imageExtensions.includes(ext);
    } catch (error) {
      console.error('检查文件类型失败:', error);
      return false;
    }
  }

  // 将图片转换为PNG格式
  private async convertImagesToPng(
    inputDirs: Record<string, string>[],
    taskId?: string,
  ): Promise<Record<string, string>[]> {
    // 创建inputDirs的副本，避免直接修改原始数组
    const updatedInputDirs = [...inputDirs];

    // 获取inputDirs中的所有的图片文件并将其转换为png格式，使用isImageFile过滤
    // 由于isImageFile是异步的，需要使用Promise.all来等待所有判断完成
    const imageCheckPromises = updatedInputDirs.map(async (item) => {
      const isImage = await this.isImageFile(item.filePath);
      return { item, isImage };
    });

    const imageCheckResults = await Promise.all(imageCheckPromises);
    const imagesToConvert = imageCheckResults
      .filter((result) => result.isImage)
      .map((result) => result.item.filePath);

    console.log(`\n找到 ${imagesToConvert.length} 个图片文件需要转换为PNG格式`);

    // 使用ffmpeg将图片文件转换为png格式，并替换旧的inputDirs中的图片路径
    const convertImagePromises: Promise<void>[] = [];

    // 处理每个图片文件
    for (const imgFile of imagesToConvert) {
      // 创建新的PNG文件路径
      const imgDir = path.dirname(imgFile);
      const imgBaseName = path.basename(imgFile, path.extname(imgFile));
      const newImgPath = path.join(imgDir, `${imgBaseName}.png`);

      console.log(`准备转换图片: ${imgFile} -> ${newImgPath}`);

      // 创建转换Promise
      const convertPromise = new Promise<void>((resolve) => {
        try {
          // 检查文件是否存在的操作需要移到Promise外部
          this.checkAndConvertImage(
            imgFile,
            newImgPath,
            updatedInputDirs,
            taskId,
            resolve,
          );
        } catch (error) {
          console.error(`图片转换过程中发生错误: ${imgFile}`, error);
          // 出错时不阻止整个流程
          resolve();
        }
      });

      convertImagePromises.push(convertPromise);
    }

    // 等待所有图片转换完成
    await Promise.all(convertImagePromises).catch((error) => {
      console.error('图片转换过程中发生错误:', error);
      Sentry.captureException('图片转换过程中发生错误:' + error);
      // 继续执行，不中断整个流程
    });

    if (imagesToConvert.length > 0) {
      console.log(
        `\n所有图片转换完成，共处理 ${imagesToConvert.length} 个图片文件`,
      );
    }

    // 返回更新后的inputDirs数组
    return updatedInputDirs;
  }

  // 添加新的异步方法来处理文件检查和转换
  private async checkAndConvertImage(
    imgFile: string,
    newImgPath: string,
    updatedInputDirs: Record<string, string>[],
    taskId?: string,
    resolve?: () => void,
  ): Promise<void> {
    try {
      // 如果目标文件已存在，则跳过转换
      try {
        await fs.promises.access(newImgPath, fs.constants.F_OK);
        console.log(`目标PNG文件已存在，跳过转换: ${newImgPath}`);
        // 更新inputDirs中的图片路径
        const index = updatedInputDirs.findIndex(
          (item) => item.filePath === imgFile,
        );
        if (index !== -1) {
          updatedInputDirs[index].filePath = newImgPath;
        }
        if (resolve) resolve();
        return;
      } catch {
        // 文件不存在，继续处理
      }

      // 使用ffprobe检查是否为动图
      try {
        const isAnimated = await this.isAnimatedImage(imgFile);
        if (isAnimated) {
          console.log(`检测到动图，保留原始格式: ${imgFile}`);
          if (resolve) resolve();
          return;
        }
      } catch (error) {
        console.error(`检查图片是否为动图时发生错误: ${imgFile}`, error);
        // 如果检查失败，继续尝试转换
      }

      // 如果不是动图，则进行转换
      this.convertImageToPng(
        imgFile,
        newImgPath,
        updatedInputDirs,
        taskId,
        resolve,
      );
    } catch (error) {
      console.error(`图片转换过程中发生错误: ${imgFile}`, error);
      if (resolve) resolve();
    }
  }

  // 将单个图片转换为PNG格式
  private convertImageToPng(
    imgFile: string,
    newImgPath: string,
    updatedInputDirs: Record<string, string>[],
    taskId?: string,
    resolve?: () => void,
  ): void {
    const command = ffmpeg(imgFile);

    // 如果提供了taskId，则注册该ffmpeg进程
    if (taskId) {
      this.registerProcess(taskId, command);
    }

    command
      .outputOptions(['-c:v', 'png'])
      .on('end', () => {
        // 更新inputDirs中的图片路径
        const index = updatedInputDirs.findIndex(
          (item) => item.filePath === imgFile,
        );
        if (index !== -1) {
          updatedInputDirs[index].filePath = newImgPath;
          console.log(`图片已转换: ${imgFile} -> ${newImgPath}`);
        }
        if (resolve) resolve();
      })
      .on('error', (err: Error) => {
        console.error(`图片转换失败: ${imgFile}`, err);
        // 转换失败时不更新路径，继续使用原始图片
        if (resolve) resolve();
      })
      .save(newImgPath);
  }

  // 检查图片是否为动图
  private async isAnimatedImage(filePath: string): Promise<boolean> {
    return new Promise<boolean>((resolve, reject) => {
      try {
        // 使用ffprobe检查文件信息
        ffmpeg.ffprobe(filePath, (err, data) => {
          if (err) {
            reject(err);
            return;
          }

          // 检查是否有视频流
          const hasVideoStream = data.streams.some(
            (stream) => stream.codec_type === 'video',
          );

          // 检查是否为GIF、APNG或WebP动图
          const ext = path.extname(filePath).toLowerCase();

          if (ext === '.gif') {
            // 对于GIF，检查帧数
            const frames = data.streams.find(
              (stream) => stream.codec_type === 'video',
            )?.nb_frames;
            // 如果帧数大于1或未定义（某些GIF可能不报告帧数），则认为是动图
            resolve(frames === undefined || parseInt(frames as string) > 1);
          } else if (
            ['.png', '.apng', '.webp'].includes(ext) &&
            hasVideoStream
          ) {
            // 对于PNG和WebP，检查编解码器和其他属性
            const videoStream = data.streams.find(
              (stream) => stream.codec_type === 'video',
            );

            // 检查是否有动画相关的属性
            const isAnimated =
              videoStream?.codec_name === 'apng' ||
              (videoStream?.codec_name === 'webp' &&
                videoStream?.nb_frames !== '1');

            resolve(isAnimated || false);
          } else {
            // 其他格式不视为动图
            resolve(false);
          }
        });
      } catch (error) {
        console.error('检查图片是否为动图时发生错误:', error);
        // 如果出错，默认为动图
        resolve(true);
      }
    });
  }

  // 根据合并目录合并所有媒体流
  public async processDirectory(
    inputDirs: Record<string, string>[], // 要合并的文件列表，inputDirs.filePath 是文件路径
    outputFormat: string,
    outputFilename: string,
    embedSubtitles: boolean,
    onProgress?: (progress: FFmpegProgress) => void,
    onComplete?: (result: { filePath: string; language?: string }[]) => void, // 返回合并成功后产生的文件路径以及未参与合并的文件
    onError?: (error: Error) => void,
    taskId?: string, // 添加taskId参数，用于关联ffmpeg进程
  ): Promise<void> {
    // 先将所有图片转换为PNG格式，并获取更新后的inputDirs
    const updatedInputDirs = await this.convertImagesToPng(inputDirs, taskId);

    try {
      const settings = await Downloader.loadSettings();
      // 验证输出目录
      const outputDir = path.dirname(outputFilename);
      try {
        await fs.promises.access(outputDir, fs.constants.F_OK);
      } catch {
        // 目录不存在，创建它
        await fs.promises.mkdir(outputDir, { recursive: true });
      }

      // 从 updatedInputDirs 中提取文件信息
      const mediaInfo: ProcessMediaInfo = {
        video: [],
        audio: [],
        subtitle: [],
        image: [],
      };

      // 存储图片文件列表
      const imageFiles: string[] = [];

      console.log('\n开始分析媒体流...');

      // 记录所有输入文件的路径，用于后续判断哪些文件未被使用
      const allInputFiles: string[] = [];

      for (const fileInfo of updatedInputDirs) {
        const filePath = fileInfo.filePath;
        allInputFiles.push(filePath);

        try {
          // 检查文件是否存在
          await fs.promises.access(filePath, fs.constants.F_OK);
        } catch {
          console.warn(`文件不存在: ${filePath}`);
          continue;
        }

        // 检查是否为目录
        try {
          const stat = await fs.promises.stat(filePath);
          if (stat.isDirectory()) continue;
        } catch (error) {
          console.warn(`无法获取文件状态: ${filePath}`, error);
          continue;
        }

        try {
          // 检查是否为图片文件
          if (await this.isImageFile(filePath)) {
            console.log(`  检测到图片文件: ${filePath}`);
            imageFiles.push(filePath);
            continue;
          }

          const info = await new Promise<FFprobeData>((resolve, reject) => {
            ffmpeg.ffprobe(filePath, (err, data) => {
              if (err) reject(err);
              else resolve(data);
            });
          });

          if (info.streams) {
            info.streams.forEach((stream: FFprobeStream) => {
              const streamInfo: StreamInfo = {
                file: filePath, // 使用完整路径
                index: stream.index,
                codec: stream.codec_name || '',
                language: fileInfo.language || stream.tags?.language || 'und',
                duration: info.format?.duration
                  ? Number(info.format.duration)
                  : undefined,
              };

              switch (stream.codec_type) {
                case 'video':
                  streamInfo.resolution = `${stream.width}x${stream.height}`;
                  streamInfo.fps = eval(stream.r_frame_rate).toFixed(2);
                  mediaInfo.video.push(streamInfo);
                  console.log(
                    `  视频流: ${streamInfo.codec} (${streamInfo.resolution}, ${streamInfo.fps}fps)`,
                  );
                  break;

                case 'audio':
                  streamInfo.channels = stream.channels;
                  streamInfo.sample_rate = stream.sample_rate;
                  mediaInfo.audio.push(streamInfo);
                  console.log(
                    `  音频流: ${streamInfo.codec} (${streamInfo.language}, ${streamInfo.channels}ch, ${streamInfo.sample_rate}Hz)`,
                  );
                  break;

                case 'subtitle':
                  mediaInfo.subtitle.push(streamInfo);
                  console.log(
                    `  字幕流: ${streamInfo.codec} (${streamInfo.language})`,
                  );
                  break;
              }
            });
          } else if (path.extname(filePath).match(/\.(srt|ass|ssa|vtt)$/i)) {
            mediaInfo.subtitle.push({
              file: filePath, // 使用完整路径
              type: 'external',
              language:
                fileInfo.language ||
                path
                  .basename(filePath)
                  .match(/\.(zh-Hans|zh-Hant|en|ja|ko)/i)?.[1] ||
                'und',
              codec: '',
              index: 0,
            });
            console.log(
              `  外部字幕文件: ${filePath} (${fileInfo.language || 'und'})`,
            );
          }
        } catch (error) {
          console.error(`  分析失败: ${filePath}`, (error as Error).message);
        }
      }

      console.log('媒体流统计：');
      console.log(`视频流: ${mediaInfo.video.length} 个`);
      console.log(`音频流: ${mediaInfo.audio.length} 个`);
      console.log(`字幕流: ${mediaInfo.subtitle.length} 个`);

      // 如果没有找到任何媒体流，直接返回所有文件作为未使用的文件
      if (
        mediaInfo.video.length === 0 &&
        mediaInfo.audio.length === 0 &&
        (!embedSubtitles || mediaInfo.subtitle.length === 0)
      ) {
        console.log(
          '\n没有找到任何可处理的媒体流，返回所有文件作为未使用的文件',
        );
        const result = allInputFiles
          .map((file) => {
            const fileInfo = updatedInputDirs.find(
              (info) => info.filePath === file,
            );
            return {
              filePath: file,
              ...(fileInfo?.language ? { language: fileInfo.language } : {}),
            };
          })
          .filter((item) => {
            // 如果设置了不保留图片，则过滤掉图片文件
            if (/\.(jpg|jpeg|png|gif|bmp|webp)$/i.test(item.filePath)) {
              return false;
            }
            return true;
          });
        onComplete?.(result);
        return;
      }

      console.log('\n准备处理流...');

      const command = ffmpeg();

      // 如果提供了taskId，则注册该ffmpeg进程
      if (taskId) {
        this.registerProcess(taskId, command);
      }

      const isAudioFormat =
        ['m4a', 'mp3', 'ogg'].includes(outputFormat.toLowerCase()) ||
        mediaInfo.video.length === 0;

      if (
        isAudioFormat &&
        outputFormat != 'mp3' &&
        outputFormat != 'm4a' &&
        outputFormat != 'ogg'
      ) {
        outputFormat = settings.downloadTypeAudio.format;
      }

      // 为输出文件添加适当的后缀
      if (!path.extname(outputFilename)) {
        outputFilename = `${outputFilename}.${outputFormat}`;
        console.log(`输出文件名已更新为: ${outputFilename}`);
      }

      const outputOptions: string[] = [];

      if (isAudioFormat) {
        // 音频处理逻辑
        if (mediaInfo.video.length === 0 && mediaInfo.audio.length === 0) {
          console.log('没有找到任何音频流，无法处理');
          const result = allInputFiles
            .map((file) => {
              const fileInfo = updatedInputDirs.find(
                (info) => info.filePath === file,
              );
              return {
                filePath: file,
                ...(fileInfo?.language ? { language: fileInfo.language } : {}),
              };
            })
            .filter((item) => {
              // 如果设置了不保留图片，则过滤掉图片文件
              if (/\.(jpg|jpeg|png|gif|bmp|webp)$/i.test(item.filePath)) {
                return false;
              }
              return true;
            });
          onComplete?.(result);
          return;
        }

        const sourceFile =
          mediaInfo.video.length > 0
            ? mediaInfo.video[0].file
            : mediaInfo.audio[0].file;

        console.log(
          `${mediaInfo.video.length > 0 ? '检测到视频流，将提取音频' : '没有视频流，将处理音频'}并转换为 ${outputFormat} 格式`,
        );

        command.input(sourceFile);

        // 如果是音频格式且有图片文件，添加第一张图片作为封面
        if (imageFiles.length > 0) {
          console.log(`添加图片作为音频封面: ${imageFiles[0]}`);
          command.input(imageFiles[0]);

          // 如果是ogg格式，则使用libvorbis编码器处理图片
          if (outputFormat.toLowerCase() === 'ogg') {
            outputOptions.push(
              '-map',
              '0:a:0', // 使用第一个音频流
              '-map',
              '1:v', // 使用图片作为视频流
              '-qscale:v',
              '10', // 设置视频质量为10
              '-disposition:v',
              'attached_pic', // 设置为封面图片
            );
          } else {
            // 统一使用mjpeg编码器处理所有图片格式，并标记为封面
            outputOptions.push(
              '-map',
              '0:a:0', // 使用第一个音频流
              '-map',
              '1:v', // 使用图片作为视频流
              '-c:v',
              'mjpeg', // 统一使用mjpeg编码器
              '-disposition:v',
              'attached_pic', // 标记为封面图片
            );
          }

          mediaInfo.image.push(imageFiles[0]);
        }

        // 音频编码选项
        switch (outputFormat.toLowerCase()) {
          case 'mp3':
            outputOptions.push('-c:a', 'libmp3lame');
            break;
          case 'm4a':
            outputOptions.push('-c:a', 'aac');
            break;
          case 'ogg':
            outputOptions.push('-c:a', 'libvorbis');
            break;
        }
      } else {
        // 视频处理逻辑
        // 添加视频输入
        for (const stream of mediaInfo.video) {
          console.log('添加视频输入:', stream.file);
          command.input(stream.file);
        }

        // 添加音频输入
        for (const stream of mediaInfo.audio) {
          console.log('添加音频输入:', stream.file);
          command.input(stream.file);
        }

        // 添加字幕输入
        if (embedSubtitles) {
          for (const stream of mediaInfo.subtitle) {
            console.log('添加字幕输入:', stream.file);
            command.input(stream.file);
          }
        }

        // 计算每个输入文件的索引
        let inputIndex = 0;
        const videoInputIndices = mediaInfo.video.map(() => inputIndex++);
        const audioInputIndices = mediaInfo.audio.map(() => inputIndex++);
        const subtitleInputIndices = embedSubtitles
          ? mediaInfo.subtitle.map(() => inputIndex++)
          : [];

        // 设置视频编码器和质量
        switch (outputFormat) {
          case 'mp4':
            // mp4格式处理 - 检查原始编码
            if (videoInputIndices.length > 0) {
              const videoStream = mediaInfo.video[0];
              // 如果原视频编码包含h264，使用copy
              if (videoStream.codec?.toLowerCase().includes('h264')) {
                outputOptions.push('-c:v', 'copy');
              } else {
                outputOptions.push('-c:v', 'libx264');
              }
            }

            // 检查音频编码
            if (audioInputIndices.length > 0) {
              const audioStream = mediaInfo.audio[0];
              // 如果原音频编码包含aac，使用copy
              if (audioStream.codec?.toLowerCase().includes('aac')) {
                outputOptions.push('-c:a', 'copy');
              } else {
                outputOptions.push('-c:a', 'aac');
              }
            }

            // 设置字幕编码为mov_text
            outputOptions.push('-c:s', 'mov_text');
            break;
          case 'mkv':
            // mkv 使用 copy 编码
            command.outputOptions(['-c:v', 'copy', '-c:a', 'copy']);
            break;
          default:
            // 对于其他格式，保持原始编码
            command.outputOptions(['-c:v', 'copy', '-c:a', 'copy']);
            break;
        }

        // 视频输出选项 - 使用第一个视频流
        if (videoInputIndices.length > 0) {
          outputOptions.push('-map', `${videoInputIndices[0]}:v:0`);
        }

        // 添加音频流映射
        audioInputIndices.forEach((inputIdx, index) => {
          outputOptions.push('-map', `${inputIdx}:a:0`);

          // 设置音频元数据
          const audioStream = mediaInfo.audio[index];
          if (audioStream.language) {
            // 获取ISO 639-2标准的三位字母语言代码
            const langCode = getLanguageCode(audioStream.language);
            console.log(
              `设置音频流 ${index} 的语言为: ${audioStream.language} -> ${langCode}`,
            );

            // 添加语言元数据
            outputOptions.push(
              `-metadata:s:a:${index}`,
              `language=${langCode}`,
            );

            // 尝试添加标题元数据
            const langKey = audioStream.language.toLowerCase();
            const langParts = langKey.split('-');
            const simpleLangKey = langParts[0];

            // 查找显示名称
            let title =
              LANGUAGE_NAMES[langKey] || LANGUAGE_NAMES[simpleLangKey] || '';
            if (!title) {
              // 尝试查找大写形式的键
              const capitalizedKey = langKey
                .split('-')
                .map((part) => part.charAt(0).toUpperCase() + part.slice(1))
                .join('-');
              title = LANGUAGE_NAMES[capitalizedKey] || '';
            }

            if (title) {
              // 对标题进行转义处理，使用单引号包裹，并将标题中的单引号转义
              const escapedTitle = title.replace(/'/g, "'\\''");
              outputOptions.push(
                `-metadata:s:a:${index}`,
                `title=${escapedTitle}`,
              );
            }
          }
        });

        // 添加字幕流映射
        if (embedSubtitles) {
          subtitleInputIndices.forEach((inputIdx, index) => {
            outputOptions.push('-map', `${inputIdx}:s?`);

            // 设置字幕元数据
            const stream = mediaInfo.subtitle[index];
            if (stream.language) {
              // 获取ISO 639-2标准的三位字母语言代码
              const langCode = getLanguageCode(stream.language);
              console.log(
                `设置字幕流 ${index} 的语言为: ${stream.language} -> ${langCode}`,
              );

              // 添加语言元数据
              outputOptions.push(
                `-metadata:s:s:${index}`,
                `language=${langCode}`,
              );

              // 尝试添加标题元数据
              const langKey = stream.language.toLowerCase();
              const langParts = langKey.split('-');
              const simpleLangKey = langParts[0];

              // 查找显示名称
              let title =
                LANGUAGE_NAMES[langKey] || LANGUAGE_NAMES[simpleLangKey] || '';
              if (!title) {
                // 尝试查找大写形式的键
                const capitalizedKey = langKey
                  .split('-')
                  .map((part) => part.charAt(0).toUpperCase() + part.slice(1))
                  .join('-');
                title = LANGUAGE_NAMES[capitalizedKey] || '';
              }

              if (title) {
                // 对标题进行转义处理，使用单引号包裹，并将标题中的单引号转义
                const escapedTitle = title.replace(/'/g, "'\\''");
                outputOptions.push(
                  `-metadata:s:s:${index}`,
                  `title=${escapedTitle}`,
                );
              }
            }
          });
        }
      }

      command
        .outputOptions(outputOptions)
        .output(outputFilename)
        .on('start', (commandLine) => {
          console.log('FFmpeg 命令:', commandLine);
        })
        .on('progress', (progress) => {
          onProgress?.(progress);
        })
        .on('end', async () => {
          // 构建结果数组
          const result: { filePath: string; language?: string }[] = [
            // 添加合并后的输出文件
            { filePath: outputFilename },
          ];

          // 添加未使用的文件
          const usedFiles = [
            ...mediaInfo.video.map((stream) => stream.file),
            ...mediaInfo.audio.map((stream) => stream.file),
            ...(embedSubtitles
              ? mediaInfo.subtitle.map((stream) => stream.file)
              : []),
            ...mediaInfo.image,
          ];

          // 如果settingsthumbnail为true，则将图片文件添加到返回的列表
          if (settings.thumbnail && imageFiles.length > 0) {
            result.push({ filePath: imageFiles[0] });
          }

          // 找出未使用的文件
          const unusedFiles = allInputFiles.filter(
            (file) => !usedFiles.includes(file),
          );
          console.log('result:', result);

          console.log('未使用的文件:', unusedFiles);

          // 将未使用的文件添加到结果中，并尝试保留语言信息
          unusedFiles.forEach((file) => {
            const fileInfo = updatedInputDirs.find(
              (info) => info.filePath === file,
            );
            // 如果是封面图，则不添加到结果中
            if (fileInfo?.type === 'thumbnail') {
              return;
            }
            if (fileInfo) {
              result.push({
                filePath: file,
                // 如果fileInfo.language存在，则添加到结果中，否则不添加
                ...(fileInfo.language ? { language: fileInfo.language } : {}),
              });
            } else {
              result.push({ filePath: file });
            }
          });

          onComplete?.(result);
        })
        .on('error', (err: Error) => {
          onError?.(err);
        })
        .run();
    } catch (error) {
      onError?.(error as Error);
    }
  }

  // 使用自定义参数处理媒体
  public async processWithOptions(
    inputs: string | string[],
    output: string,
    commandOptions: FFmpegCommandOptions,
    onProgress?: (progress: FFmpegProgress) => void,
    onComplete?: () => void,
    onError?: (error: Error) => void,
  ): Promise<void> {
    try {
      const command = ffmpeg();

      // 添加输入
      if (Array.isArray(inputs)) {
        inputs.forEach((input, index) => {
          command.input(input);
          // 添加输入选项（如果有）
          if (commandOptions.inputOptions?.[index]) {
            command.inputOption(commandOptions.inputOptions[index]);
          }
        });
      } else {
        command.input(inputs);
        // 添加输入选项（如果有）
        if (commandOptions.inputOptions?.[0]) {
          command.inputOption(commandOptions.inputOptions[0]);
        }
      }

      // 添加流映射（如果有）
      if (commandOptions.streamMappings?.length) {
        command.outputOptions(commandOptions.streamMappings);
      }

      // 添加输出选项（如果有）
      if (commandOptions.outputOptions?.length) {
        command.outputOptions(commandOptions.outputOptions);
      }

      // 监听事件
      command
        .on('start', (commandLine) => {
          console.log('FFmpeg 命令:', commandLine);
        })
        .on('progress', (progress: FFmpegProgress) => {
          if (onProgress) {
            onProgress({
              percent: progress.percent,
              currentFps: progress.currentFps,
              currentKbps: progress.currentKbps,
              targetSize: progress.targetSize,
              timemark: progress.timemark,
              ...progress,
            });
          }
        })
        .on('end', () => {
          if (onComplete) {
            onComplete();
          }
        })
        .on('error', (err: Error) => {
          console.error('处理失败:', err);
          Sentry.captureException('处理失败:' + err);
          if (onError) {
            onError(err);
          }
        })
        .on('stderr', (stderrLine) => {
          console.log('FFmpeg 输出:', stderrLine);
        });

      // 开始处理
      await command.save(output);
    } catch (error) {
      console.error('处理时发生错误:', error);
      Sentry.captureException('处理时发生错误:' + error);
      if (onError) {
        onError(error instanceof Error ? error : new Error(String(error)));
      }
      throw error;
    }
  }

  // 取消任务
  public cancelTask(taskId: string): void {
    const processes = this.ffmpegProcesses.get(taskId);
    if (processes && processes.length > 0) {
      console.log(`正在取消FFmpeg任务: ${taskId}, 进程数: ${processes.length}`);
      // 终止所有与该taskId关联的ffmpeg进程
      for (const process of processes) {
        try {
          process.kill('SIGKILL');
        } catch (error) {
          console.error('终止FFmpeg进程失败:', error);
        }
      }
      // 清除该taskId的所有进程记录
      this.ffmpegProcesses.delete(taskId);
    }
  }

  // 注册进程
  private registerProcess(taskId: string, process: ffmpeg.FfmpegCommand): void {
    if (!this.ffmpegProcesses.has(taskId)) {
      this.ffmpegProcesses.set(taskId, []);
    }
    this.ffmpegProcesses.get(taskId)?.push(process);
  }
}
