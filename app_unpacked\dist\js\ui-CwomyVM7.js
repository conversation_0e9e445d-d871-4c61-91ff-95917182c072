import{r as zs,R as ye,a as f,g as Vn,b as Gn,c as Bs}from"./vendor-DjSYeWVf.js";var cr={exports:{}},pt={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ln;function _s(){if(ln)return pt;ln=1;var e=zs(),t=Symbol.for("react.element"),r=Symbol.for("react.fragment"),n=Object.prototype.hasOwnProperty,o=e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,a={key:!0,ref:!0,__self:!0,__source:!0};function s(i,c,l){var b,u={},m=null,g=null;l!==void 0&&(m=""+l),c.key!==void 0&&(m=""+c.key),c.ref!==void 0&&(g=c.ref);for(b in c)n.call(c,b)&&!a.hasOwnProperty(b)&&(u[b]=c[b]);if(i&&i.defaultProps)for(b in c=i.defaultProps,c)u[b]===void 0&&(u[b]=c[b]);return{$$typeof:t,type:i,key:m,ref:g,props:u,_owner:o.current}}return pt.Fragment=r,pt.jsx=s,pt.jsxs=s,pt}var cn;function Hs(){return cn||(cn=1,cr.exports=_s()),cr.exports}var d=Hs(),Yn={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},dn=ye.createContext&&ye.createContext(Yn),$s=["attr","size","title"];function Ws(e,t){if(e==null)return{};var r=Vs(e,t),n,o;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(o=0;o<a.length;o++)n=a[o],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function Vs(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function Ht(){return Ht=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ht.apply(this,arguments)}function un(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,n)}return r}function $t(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?un(Object(r),!0).forEach(function(n){Gs(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):un(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Gs(e,t,r){return t=Ys(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Ys(e){var t=Ks(e,"string");return typeof t=="symbol"?t:t+""}function Ks(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Kn(e){return e&&e.map((t,r)=>ye.createElement(t.tag,$t({key:r},t.attr),Kn(t.child)))}function Pu(e){return t=>ye.createElement(qs,Ht({attr:$t({},e.attr)},t),Kn(e.child))}function qs(e){var t=r=>{var{attr:n,size:o,title:a}=e,s=Ws(e,$s),i=o||r.size||"1em",c;return r.className&&(c=r.className),e.className&&(c=(c?c+" ":"")+e.className),ye.createElement("svg",Ht({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},r.attr,n,s,{className:c,style:$t($t({color:e.color||r.color},r.style),e.style),height:i,width:i,xmlns:"http://www.w3.org/2000/svg"}),a&&ye.createElement("title",null,a),e.children)};return dn!==void 0?ye.createElement(dn.Consumer,null,r=>t(r)):t(Yn)}var qn={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},fn=ye.createContext&&ye.createContext(qn),Us=["attr","size","title"];function Xs(e,t){if(e==null)return{};var r=Js(e,t),n,o;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(o=0;o<a.length;o++)n=a[o],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function Js(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function Wt(){return Wt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Wt.apply(this,arguments)}function gn(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,n)}return r}function Vt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?gn(Object(r),!0).forEach(function(n){Zs(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):gn(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Zs(e,t,r){return t=Qs(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Qs(e){var t=ei(e,"string");return typeof t=="symbol"?t:t+""}function ei(e,t){if(typeof e!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(typeof n!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Un(e){return e&&e.map((t,r)=>ye.createElement(t.tag,Vt({key:r},t.attr),Un(t.child)))}function fe(e){return t=>ye.createElement(ti,Wt({attr:Vt({},e.attr)},t),Un(e.child))}function ti(e){var t=r=>{var{attr:n,size:o,title:a}=e,s=Xs(e,Us),i=o||r.size||"1em",c;return r.className&&(c=r.className),e.className&&(c=(c?c+" ":"")+e.className),ye.createElement("svg",Wt({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},r.attr,n,s,{className:c,style:Vt(Vt({color:e.color||r.color},r.style),e.style),height:i,width:i,xmlns:"http://www.w3.org/2000/svg"}),a&&ye.createElement("title",null,a),e.children)};return fn!==void 0?ye.createElement(fn.Consumer,null,r=>t(r)):t(qn)}function ri(e){return fe({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z",clipRule:"evenodd"},child:[]}]})(e)}function ni(e){return fe({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z",clipRule:"evenodd"},child:[]}]})(e)}function oi(e){return fe({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z",clipRule:"evenodd"},child:[]}]})(e)}function Xn(e){return fe({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z",clipRule:"evenodd"},child:[]}]})(e)}function ai(e){return fe({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z",clipRule:"evenodd"},child:[]}]})(e)}function si(e){return fe({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z",clipRule:"evenodd"},child:[]}]})(e)}function ii(e){return fe({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"},child:[]}]})(e)}function li(e){return fe({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"},child:[]}]})(e)}function Jn(e){return fe({attr:{fill:"none",viewBox:"0 0 24 24",strokeWidth:"2",stroke:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{strokeLinecap:"round",strokeLinejoin:"round",d:"M19 9l-7 7-7-7"},child:[]}]})(e)}function ci(e){return fe({attr:{fill:"none",viewBox:"0 0 24 24",strokeWidth:"2",stroke:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 19l-7-7 7-7"},child:[]}]})(e)}function Zn(e){return fe({attr:{fill:"none",viewBox:"0 0 24 24",strokeWidth:"2",stroke:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 5l7 7-7 7"},child:[]}]})(e)}function di(e){return fe({attr:{fill:"none",viewBox:"0 0 24 24",strokeWidth:"2",stroke:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{strokeLinecap:"round",strokeLinejoin:"round",d:"M5 15l7-7 7 7"},child:[]}]})(e)}function ui(e){return fe({attr:{fill:"none",viewBox:"0 0 24 24",strokeWidth:"2",stroke:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 18L18 6M6 6l12 12"},child:[]}]})(e)}const Er="-";function fi(e){const t=bi(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:n}=e;function o(s){const i=s.split(Er);return i[0]===""&&i.length!==1&&i.shift(),Qn(i,t)||gi(s)}function a(s,i){const c=r[s]||[];return i&&n[s]?[...c,...n[s]]:c}return{getClassGroupId:o,getConflictingClassGroupIds:a}}function Qn(e,t){var s;if(e.length===0)return t.classGroupId;const r=e[0],n=t.nextPart.get(r),o=n?Qn(e.slice(1),n):void 0;if(o)return o;if(t.validators.length===0)return;const a=e.join(Er);return(s=t.validators.find(({validator:i})=>i(a)))==null?void 0:s.classGroupId}const bn=/^\[(.+)\]$/;function gi(e){if(bn.test(e)){const t=bn.exec(e)[1],r=t==null?void 0:t.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}}function bi(e){const{theme:t,prefix:r}=e,n={nextPart:new Map,validators:[]};return mi(Object.entries(e.classGroups),r).forEach(([a,s])=>{vr(s,n,a,t)}),n}function vr(e,t,r,n){e.forEach(o=>{if(typeof o=="string"){const a=o===""?t:pn(t,o);a.classGroupId=r;return}if(typeof o=="function"){if(pi(o)){vr(o(n),t,r,n);return}t.validators.push({validator:o,classGroupId:r});return}Object.entries(o).forEach(([a,s])=>{vr(s,pn(t,a),r,n)})})}function pn(e,t){let r=e;return t.split(Er).forEach(n=>{r.nextPart.has(n)||r.nextPart.set(n,{nextPart:new Map,validators:[]}),r=r.nextPart.get(n)}),r}function pi(e){return e.isThemeGetter}function mi(e,t){return t?e.map(([r,n])=>{const o=n.map(a=>typeof a=="string"?t+a:typeof a=="object"?Object.fromEntries(Object.entries(a).map(([s,i])=>[t+s,i])):a);return[r,o]}):e}function hi(e){if(e<1)return{get:()=>{},set:()=>{}};let t=0,r=new Map,n=new Map;function o(a,s){r.set(a,s),t++,t>e&&(t=0,n=r,r=new Map)}return{get(a){let s=r.get(a);if(s!==void 0)return s;if((s=n.get(a))!==void 0)return o(a,s),s},set(a,s){r.has(a)?r.set(a,s):o(a,s)}}}const eo="!";function yi(e){const{separator:t,experimentalParseClassName:r}=e,n=t.length===1,o=t[0],a=t.length;function s(i){const c=[];let l=0,b=0,u;for(let w=0;w<i.length;w++){let v=i[w];if(l===0){if(v===o&&(n||i.slice(w,w+a)===t)){c.push(i.slice(b,w)),b=w+a;continue}if(v==="/"){u=w;continue}}v==="["?l++:v==="]"&&l--}const m=c.length===0?i:i.substring(b),g=m.startsWith(eo),p=g?m.substring(1):m,h=u&&u>b?u-b:void 0;return{modifiers:c,hasImportantModifier:g,baseClassName:p,maybePostfixModifierPosition:h}}return r?function(c){return r({className:c,parseClassName:s})}:s}function xi(e){if(e.length<=1)return e;const t=[];let r=[];return e.forEach(n=>{n[0]==="["?(t.push(...r.sort(),n),r=[]):r.push(n)}),t.push(...r.sort()),t}function wi(e){return{cache:hi(e.cacheSize),parseClassName:yi(e),...fi(e)}}const vi=/\s+/;function ki(e,t){const{parseClassName:r,getClassGroupId:n,getConflictingClassGroupIds:o}=t,a=new Set;return e.trim().split(vi).map(s=>{const{modifiers:i,hasImportantModifier:c,baseClassName:l,maybePostfixModifierPosition:b}=r(s);let u=!!b,m=n(u?l.substring(0,b):l);if(!m){if(!u)return{isTailwindClass:!1,originalClassName:s};if(m=n(l),!m)return{isTailwindClass:!1,originalClassName:s};u=!1}const g=xi(i).join(":");return{isTailwindClass:!0,modifierId:c?g+eo:g,classGroupId:m,originalClassName:s,hasPostfixModifier:u}}).reverse().filter(s=>{if(!s.isTailwindClass)return!0;const{modifierId:i,classGroupId:c,hasPostfixModifier:l}=s,b=i+c;return a.has(b)?!1:(a.add(b),o(c,l).forEach(u=>a.add(i+u)),!0)}).reverse().map(s=>s.originalClassName).join(" ")}function Ci(){let e=0,t,r,n="";for(;e<arguments.length;)(t=arguments[e++])&&(r=to(t))&&(n&&(n+=" "),n+=r);return n}function to(e){if(typeof e=="string")return e;let t,r="";for(let n=0;n<e.length;n++)e[n]&&(t=to(e[n]))&&(r&&(r+=" "),r+=t);return r}function Ti(e,...t){let r,n,o,a=s;function s(c){const l=t.reduce((b,u)=>u(b),e());return r=wi(l),n=r.cache.get,o=r.cache.set,a=i,i(c)}function i(c){const l=n(c);if(l)return l;const b=ki(c,r);return o(c,b),b}return function(){return a(Ci.apply(null,arguments))}}function ae(e){const t=r=>r[e]||[];return t.isThemeGetter=!0,t}const ro=/^\[(?:([a-z-]+):)?(.+)\]$/i,Ni=/^\d+\/\d+$/,Ri=new Set(["px","full","screen"]),ji=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,Ei=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,Ii=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,Si=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,Oi=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/;function Me(e){return qe(e)||Ri.has(e)||Ni.test(e)}function ze(e){return ut(e,"length",Bi)}function qe(e){return!!e&&!Number.isNaN(Number(e))}function Mt(e){return ut(e,"number",qe)}function mt(e){return!!e&&Number.isInteger(Number(e))}function Mi(e){return e.endsWith("%")&&qe(e.slice(0,-1))}function X(e){return ro.test(e)}function Be(e){return ji.test(e)}const Pi=new Set(["length","size","percentage"]);function Di(e){return ut(e,Pi,no)}function Li(e){return ut(e,"position",no)}const Ai=new Set(["image","url"]);function Fi(e){return ut(e,Ai,Hi)}function zi(e){return ut(e,"",_i)}function ht(){return!0}function ut(e,t,r){const n=ro.exec(e);return n?n[1]?typeof t=="string"?n[1]===t:t.has(n[1]):r(n[2]):!1}function Bi(e){return Ei.test(e)&&!Ii.test(e)}function no(){return!1}function _i(e){return Si.test(e)}function Hi(e){return Oi.test(e)}function $i(){const e=ae("colors"),t=ae("spacing"),r=ae("blur"),n=ae("brightness"),o=ae("borderColor"),a=ae("borderRadius"),s=ae("borderSpacing"),i=ae("borderWidth"),c=ae("contrast"),l=ae("grayscale"),b=ae("hueRotate"),u=ae("invert"),m=ae("gap"),g=ae("gradientColorStops"),p=ae("gradientColorStopPositions"),h=ae("inset"),w=ae("margin"),v=ae("opacity"),C=ae("padding"),T=ae("saturate"),y=ae("scale"),I=ae("sepia"),A=ae("skew"),B=ae("space"),_=ae("translate"),W=()=>["auto","contain","none"],S=()=>["auto","hidden","clip","visible","scroll"],L=()=>["auto",X,t],N=()=>[X,t],O=()=>["",Me,ze],k=()=>["auto",qe,X],M=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],R=()=>["solid","dashed","dotted","double","none"],P=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],D=()=>["start","end","center","between","around","evenly","stretch"],H=()=>["","0",X],E=()=>["auto","avoid","all","avoid-page","page","left","right","column"],Y=()=>[qe,Mt],ne=()=>[qe,X];return{cacheSize:500,separator:":",theme:{colors:[ht],spacing:[Me,ze],blur:["none","",Be,X],brightness:Y(),borderColor:[e],borderRadius:["none","","full",Be,X],borderSpacing:N(),borderWidth:O(),contrast:Y(),grayscale:H(),hueRotate:ne(),invert:H(),gap:N(),gradientColorStops:[e],gradientColorStopPositions:[Mi,ze],inset:L(),margin:L(),opacity:Y(),padding:N(),saturate:Y(),scale:Y(),sepia:H(),skew:ne(),space:N(),translate:N()},classGroups:{aspect:[{aspect:["auto","square","video",X]}],container:["container"],columns:[{columns:[Be]}],"break-after":[{"break-after":E()}],"break-before":[{"break-before":E()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...M(),X]}],overflow:[{overflow:S()}],"overflow-x":[{"overflow-x":S()}],"overflow-y":[{"overflow-y":S()}],overscroll:[{overscroll:W()}],"overscroll-x":[{"overscroll-x":W()}],"overscroll-y":[{"overscroll-y":W()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[h]}],"inset-x":[{"inset-x":[h]}],"inset-y":[{"inset-y":[h]}],start:[{start:[h]}],end:[{end:[h]}],top:[{top:[h]}],right:[{right:[h]}],bottom:[{bottom:[h]}],left:[{left:[h]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",mt,X]}],basis:[{basis:L()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",X]}],grow:[{grow:H()}],shrink:[{shrink:H()}],order:[{order:["first","last","none",mt,X]}],"grid-cols":[{"grid-cols":[ht]}],"col-start-end":[{col:["auto",{span:["full",mt,X]},X]}],"col-start":[{"col-start":k()}],"col-end":[{"col-end":k()}],"grid-rows":[{"grid-rows":[ht]}],"row-start-end":[{row:["auto",{span:[mt,X]},X]}],"row-start":[{"row-start":k()}],"row-end":[{"row-end":k()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",X]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",X]}],gap:[{gap:[m]}],"gap-x":[{"gap-x":[m]}],"gap-y":[{"gap-y":[m]}],"justify-content":[{justify:["normal",...D()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...D(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...D(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[C]}],px:[{px:[C]}],py:[{py:[C]}],ps:[{ps:[C]}],pe:[{pe:[C]}],pt:[{pt:[C]}],pr:[{pr:[C]}],pb:[{pb:[C]}],pl:[{pl:[C]}],m:[{m:[w]}],mx:[{mx:[w]}],my:[{my:[w]}],ms:[{ms:[w]}],me:[{me:[w]}],mt:[{mt:[w]}],mr:[{mr:[w]}],mb:[{mb:[w]}],ml:[{ml:[w]}],"space-x":[{"space-x":[B]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[B]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",X,t]}],"min-w":[{"min-w":[X,t,"min","max","fit"]}],"max-w":[{"max-w":[X,t,"none","full","min","max","fit","prose",{screen:[Be]},Be]}],h:[{h:[X,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[X,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[X,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[X,t,"auto","min","max","fit"]}],"font-size":[{text:["base",Be,ze]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",Mt]}],"font-family":[{font:[ht]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractons"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",X]}],"line-clamp":[{"line-clamp":["none",qe,Mt]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",Me,X]}],"list-image":[{"list-image":["none",X]}],"list-style-type":[{list:["none","disc","decimal",X]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[v]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[v]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...R(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",Me,ze]}],"underline-offset":[{"underline-offset":["auto",Me,X]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:N()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",X]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",X]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[v]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...M(),Li]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",Di]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},Fi]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[p]}],"gradient-via-pos":[{via:[p]}],"gradient-to-pos":[{to:[p]}],"gradient-from":[{from:[g]}],"gradient-via":[{via:[g]}],"gradient-to":[{to:[g]}],rounded:[{rounded:[a]}],"rounded-s":[{"rounded-s":[a]}],"rounded-e":[{"rounded-e":[a]}],"rounded-t":[{"rounded-t":[a]}],"rounded-r":[{"rounded-r":[a]}],"rounded-b":[{"rounded-b":[a]}],"rounded-l":[{"rounded-l":[a]}],"rounded-ss":[{"rounded-ss":[a]}],"rounded-se":[{"rounded-se":[a]}],"rounded-ee":[{"rounded-ee":[a]}],"rounded-es":[{"rounded-es":[a]}],"rounded-tl":[{"rounded-tl":[a]}],"rounded-tr":[{"rounded-tr":[a]}],"rounded-br":[{"rounded-br":[a]}],"rounded-bl":[{"rounded-bl":[a]}],"border-w":[{border:[i]}],"border-w-x":[{"border-x":[i]}],"border-w-y":[{"border-y":[i]}],"border-w-s":[{"border-s":[i]}],"border-w-e":[{"border-e":[i]}],"border-w-t":[{"border-t":[i]}],"border-w-r":[{"border-r":[i]}],"border-w-b":[{"border-b":[i]}],"border-w-l":[{"border-l":[i]}],"border-opacity":[{"border-opacity":[v]}],"border-style":[{border:[...R(),"hidden"]}],"divide-x":[{"divide-x":[i]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[i]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[v]}],"divide-style":[{divide:R()}],"border-color":[{border:[o]}],"border-color-x":[{"border-x":[o]}],"border-color-y":[{"border-y":[o]}],"border-color-t":[{"border-t":[o]}],"border-color-r":[{"border-r":[o]}],"border-color-b":[{"border-b":[o]}],"border-color-l":[{"border-l":[o]}],"divide-color":[{divide:[o]}],"outline-style":[{outline:["",...R()]}],"outline-offset":[{"outline-offset":[Me,X]}],"outline-w":[{outline:[Me,ze]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:O()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[v]}],"ring-offset-w":[{"ring-offset":[Me,ze]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",Be,zi]}],"shadow-color":[{shadow:[ht]}],opacity:[{opacity:[v]}],"mix-blend":[{"mix-blend":[...P(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":P()}],filter:[{filter:["","none"]}],blur:[{blur:[r]}],brightness:[{brightness:[n]}],contrast:[{contrast:[c]}],"drop-shadow":[{"drop-shadow":["","none",Be,X]}],grayscale:[{grayscale:[l]}],"hue-rotate":[{"hue-rotate":[b]}],invert:[{invert:[u]}],saturate:[{saturate:[T]}],sepia:[{sepia:[I]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[r]}],"backdrop-brightness":[{"backdrop-brightness":[n]}],"backdrop-contrast":[{"backdrop-contrast":[c]}],"backdrop-grayscale":[{"backdrop-grayscale":[l]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[b]}],"backdrop-invert":[{"backdrop-invert":[u]}],"backdrop-opacity":[{"backdrop-opacity":[v]}],"backdrop-saturate":[{"backdrop-saturate":[T]}],"backdrop-sepia":[{"backdrop-sepia":[I]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[s]}],"border-spacing-x":[{"border-spacing-x":[s]}],"border-spacing-y":[{"border-spacing-y":[s]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",X]}],duration:[{duration:ne()}],ease:[{ease:["linear","in","out","in-out",X]}],delay:[{delay:ne()}],animate:[{animate:["none","spin","ping","pulse","bounce",X]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[y]}],"scale-x":[{"scale-x":[y]}],"scale-y":[{"scale-y":[y]}],rotate:[{rotate:[mt,X]}],"translate-x":[{"translate-x":[_]}],"translate-y":[{"translate-y":[_]}],"skew-x":[{"skew-x":[A]}],"skew-y":[{"skew-y":[A]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",X]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",X]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":N()}],"scroll-mx":[{"scroll-mx":N()}],"scroll-my":[{"scroll-my":N()}],"scroll-ms":[{"scroll-ms":N()}],"scroll-me":[{"scroll-me":N()}],"scroll-mt":[{"scroll-mt":N()}],"scroll-mr":[{"scroll-mr":N()}],"scroll-mb":[{"scroll-mb":N()}],"scroll-ml":[{"scroll-ml":N()}],"scroll-p":[{"scroll-p":N()}],"scroll-px":[{"scroll-px":N()}],"scroll-py":[{"scroll-py":N()}],"scroll-ps":[{"scroll-ps":N()}],"scroll-pe":[{"scroll-pe":N()}],"scroll-pt":[{"scroll-pt":N()}],"scroll-pr":[{"scroll-pr":N()}],"scroll-pb":[{"scroll-pb":N()}],"scroll-pl":[{"scroll-pl":N()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",X]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[Me,ze,Mt]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}}const x=Ti($i);function Ye(e){return e!==null&&typeof e=="object"&&e.constructor===Object}function wt(e){if(!Ye(e))return e;const t={};for(const r in e)t[r]=wt(e[r]);return t}function j(e,t){if(Ye(t)&&Object.keys(t).length===0)return wt({...e,...t});const r={...e,...t};if(Ye(t)&&Ye(e))for(const n in t)Ye(t[n])&&n in e&&Ye(e[n])?r[n]=j(e[n],t[n]):r[n]=Ye(t[n])?wt(t[n]):t[n];return r}const Wi={root:{base:"divide-y divide-gray-200 border-gray-200 dark:divide-gray-700 dark:border-gray-700",flush:{off:"rounded-lg border",on:"border-b"}},content:{base:"p-5 first:rounded-t-lg last:rounded-b-lg dark:bg-gray-900"},title:{arrow:{base:"h-6 w-6 shrink-0",open:{off:"",on:"rotate-180"}},base:"flex w-full items-center justify-between p-5 text-left font-medium text-gray-500 first:rounded-t-lg last:rounded-b-lg dark:text-gray-400",flush:{off:"hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 dark:hover:bg-gray-800 dark:focus:ring-gray-800",on:"bg-transparent dark:bg-transparent"},heading:"",open:{off:"",on:"bg-gray-100 text-gray-900 dark:bg-gray-800 dark:text-white"}}},Vi={base:"flex flex-col gap-2 p-4 text-sm",borderAccent:"border-t-4",closeButton:{base:"-m-1.5 ml-auto inline-flex h-8 w-8 rounded-lg p-1.5 focus:ring-2",icon:"h-5 w-5",color:{info:"bg-cyan-100 text-cyan-500 hover:bg-cyan-200 focus:ring-cyan-400 dark:bg-cyan-200 dark:text-cyan-600 dark:hover:bg-cyan-300",gray:"bg-gray-100 text-gray-500 hover:bg-gray-200 focus:ring-gray-400 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-800 dark:hover:text-white",failure:"bg-red-100 text-red-500 hover:bg-red-200 focus:ring-red-400 dark:bg-red-200 dark:text-red-600 dark:hover:bg-red-300",success:"bg-green-100 text-green-500 hover:bg-green-200 focus:ring-green-400 dark:bg-green-200 dark:text-green-600 dark:hover:bg-green-300",warning:"bg-yellow-100 text-yellow-500 hover:bg-yellow-200 focus:ring-yellow-400 dark:bg-yellow-200 dark:text-yellow-600 dark:hover:bg-yellow-300",red:"bg-red-100 text-red-500 hover:bg-red-200 focus:ring-red-400 dark:bg-red-200 dark:text-red-600 dark:hover:bg-red-300",green:"bg-green-100 text-green-500 hover:bg-green-200 focus:ring-green-400 dark:bg-green-200 dark:text-green-600 dark:hover:bg-green-300",yellow:"bg-yellow-100 text-yellow-500 hover:bg-yellow-200 focus:ring-yellow-400 dark:bg-yellow-200 dark:text-yellow-600 dark:hover:bg-yellow-300",blue:"bg-blue-100 text-blue-500 hover:bg-blue-200 focus:ring-blue-400 dark:bg-blue-200 dark:text-blue-600 dark:hover:bg-blue-300",cyan:"bg-cyan-100 text-cyan-500 hover:bg-cyan-200 focus:ring-cyan-400 dark:bg-cyan-200 dark:text-cyan-600 dark:hover:bg-cyan-300",pink:"bg-pink-100 text-pink-500 hover:bg-pink-200 focus:ring-pink-400 dark:bg-pink-200 dark:text-pink-600 dark:hover:bg-pink-300",lime:"bg-lime-100 text-lime-500 hover:bg-lime-200 focus:ring-lime-400 dark:bg-lime-200 dark:text-lime-600 dark:hover:bg-lime-300",dark:"bg-gray-100 text-gray-500 hover:bg-gray-200 focus:ring-gray-400 dark:bg-gray-200 dark:text-gray-600 dark:hover:bg-gray-300",indigo:"bg-indigo-100 text-indigo-500 hover:bg-indigo-200 focus:ring-indigo-400 dark:bg-indigo-200 dark:text-indigo-600 dark:hover:bg-indigo-300",purple:"bg-purple-100 text-purple-500 hover:bg-purple-200 focus:ring-purple-400 dark:bg-purple-200 dark:text-purple-600 dark:hover:bg-purple-300",teal:"bg-teal-100 text-teal-500 hover:bg-teal-200 focus:ring-teal-400 dark:bg-teal-200 dark:text-teal-600 dark:hover:bg-teal-300",light:"bg-gray-50 text-gray-500 hover:bg-gray-100 focus:ring-gray-200 dark:bg-gray-600 dark:text-gray-200 dark:hover:bg-gray-700 dark:hover:text-white"}},color:{info:"border-cyan-500 bg-cyan-100 text-cyan-700 dark:bg-cyan-200 dark:text-cyan-800",gray:"border-gray-500 bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300",failure:"border-red-500 bg-red-100 text-red-700 dark:bg-red-200 dark:text-red-800",success:"border-green-500 bg-green-100 text-green-700 dark:bg-green-200 dark:text-green-800",warning:"border-yellow-500 bg-yellow-100 text-yellow-700 dark:bg-yellow-200 dark:text-yellow-800",red:"border-red-500 bg-red-100 text-red-700 dark:bg-red-200 dark:text-red-800",green:"border-green-500 bg-green-100 text-green-700 dark:bg-green-200 dark:text-green-800",yellow:"border-yellow-500 bg-yellow-100 text-yellow-700 dark:bg-yellow-200 dark:text-yellow-800",blue:"border-blue-500 bg-blue-100 text-blue-700 dark:bg-blue-200 dark:text-blue-800",cyan:"border-cyan-500 bg-cyan-100 text-cyan-700 dark:bg-cyan-200 dark:text-cyan-800",pink:"border-pink-500 bg-pink-100 text-pink-700 dark:bg-pink-200 dark:text-pink-800",lime:"border-lime-500 bg-lime-100 text-lime-700 dark:bg-lime-200 dark:text-lime-800",dark:"border-gray-600 bg-gray-800 text-gray-200 dark:bg-gray-900 dark:text-gray-300",indigo:"border-indigo-500 bg-indigo-100 text-indigo-700 dark:bg-indigo-200 dark:text-indigo-800",purple:"border-purple-500 bg-purple-100 text-purple-700 dark:bg-purple-200 dark:text-purple-800",teal:"border-teal-500 bg-teal-100 text-teal-700 dark:bg-teal-200 dark:text-teal-800",light:"border-gray-400 bg-gray-50 text-gray-600 dark:bg-gray-500 dark:text-gray-200"},icon:"mr-3 inline h-5 w-5 flex-shrink-0",rounded:"rounded-lg",wrapper:"flex items-center"},Gi={root:{base:"flex items-center justify-center space-x-4 rounded",bordered:"p-1 ring-2",rounded:"rounded-full",color:{dark:"ring-gray-800 dark:ring-gray-800",failure:"ring-red-500 dark:ring-red-700",gray:"ring-gray-500 dark:ring-gray-400",info:"ring-cyan-400 dark:ring-cyan-800",light:"ring-gray-300 dark:ring-gray-500",purple:"ring-purple-500 dark:ring-purple-600",success:"ring-green-500 dark:ring-green-500",warning:"ring-yellow-300 dark:ring-yellow-500",pink:"ring-pink-500 dark:ring-pink-500"},img:{base:"rounded",off:"relative overflow-hidden bg-gray-100 dark:bg-gray-600",on:"",placeholder:"absolute -bottom-1 h-auto w-auto text-gray-400"},size:{xs:"h-6 w-6",sm:"h-8 w-8",md:"h-10 w-10",lg:"h-20 w-20",xl:"h-36 w-36"},stacked:"ring-2 ring-gray-300 dark:ring-gray-500",statusPosition:{"bottom-left":"-bottom-1 -left-1","bottom-center":"-bottom-1","bottom-right":"-bottom-1 -right-1","top-left":"-left-1 -top-1","top-center":"-top-1","top-right":"-right-1 -top-1","center-right":"-right-1",center:"","center-left":"-left-1"},status:{away:"bg-yellow-400",base:"absolute h-3.5 w-3.5 rounded-full border-2 border-white dark:border-gray-800",busy:"bg-red-400",offline:"bg-gray-400",online:"bg-green-400"},initials:{text:"font-medium text-gray-600 dark:text-gray-300",base:"relative inline-flex items-center justify-center overflow-hidden bg-gray-100 dark:bg-gray-600"}},group:{base:"flex -space-x-4"},groupCounter:{base:"relative flex h-10 w-10 items-center justify-center rounded-full bg-gray-700 text-xs font-medium text-white ring-2 ring-gray-300 hover:bg-gray-600 dark:ring-gray-500"}},Yi={root:{base:"flex h-fit items-center gap-1 font-semibold",color:{info:"bg-cyan-100 text-cyan-800 group-hover:bg-cyan-200 dark:bg-cyan-200 dark:text-cyan-800 dark:group-hover:bg-cyan-300",gray:"bg-gray-100 text-gray-800 group-hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:group-hover:bg-gray-600",failure:"bg-red-100 text-red-800 group-hover:bg-red-200 dark:bg-red-200 dark:text-red-900 dark:group-hover:bg-red-300",success:"bg-green-100 text-green-800 group-hover:bg-green-200 dark:bg-green-200 dark:text-green-900 dark:group-hover:bg-green-300",warning:"bg-yellow-100 text-yellow-800 group-hover:bg-yellow-200 dark:bg-yellow-200 dark:text-yellow-900 dark:group-hover:bg-yellow-300",indigo:"bg-indigo-100 text-indigo-800 group-hover:bg-indigo-200 dark:bg-indigo-200 dark:text-indigo-900 dark:group-hover:bg-indigo-300",purple:"bg-purple-100 text-purple-800 group-hover:bg-purple-200 dark:bg-purple-200 dark:text-purple-900 dark:group-hover:bg-purple-300",pink:"bg-pink-100 text-pink-800 group-hover:bg-pink-200 dark:bg-pink-200 dark:text-pink-900 dark:group-hover:bg-pink-300",blue:"bg-blue-100 text-blue-800 group-hover:bg-blue-200 dark:bg-blue-200 dark:text-blue-900 dark:group-hover:bg-blue-300",cyan:"bg-cyan-100 text-cyan-800 group-hover:bg-cyan-200 dark:bg-cyan-200 dark:text-cyan-900 dark:group-hover:bg-cyan-300",dark:"bg-gray-600 text-gray-100 group-hover:bg-gray-500 dark:bg-gray-900 dark:text-gray-200 dark:group-hover:bg-gray-700",light:"bg-gray-200 text-gray-800 group-hover:bg-gray-300 dark:bg-gray-400 dark:text-gray-900 dark:group-hover:bg-gray-500",green:"bg-green-100 text-green-800 group-hover:bg-green-200 dark:bg-green-200 dark:text-green-900 dark:group-hover:bg-green-300",lime:"bg-lime-100 text-lime-800 group-hover:bg-lime-200 dark:bg-lime-200 dark:text-lime-900 dark:group-hover:bg-lime-300",red:"bg-red-100 text-red-800 group-hover:bg-red-200 dark:bg-red-200 dark:text-red-900 dark:group-hover:bg-red-300",teal:"bg-teal-100 text-teal-800 group-hover:bg-teal-200 dark:bg-teal-200 dark:text-teal-900 dark:group-hover:bg-teal-300",yellow:"bg-yellow-100 text-yellow-800 group-hover:bg-yellow-200 dark:bg-yellow-200 dark:text-yellow-900 dark:group-hover:bg-yellow-300"},href:"group",size:{xs:"p-1 text-xs",sm:"p-1.5 text-sm"}},icon:{off:"rounded px-2 py-0.5",on:"rounded-full p-1.5",size:{xs:"h-3 w-3",sm:"h-3.5 w-3.5"}}},Ki={root:{base:"text-xl font-semibold italic text-gray-900 dark:text-white"}},qi={root:{base:"",list:"flex items-center"},item:{base:"group flex items-center",chevron:"mx-1 h-4 w-4 text-gray-400 group-first:hidden md:mx-2",href:{off:"flex items-center text-sm font-medium text-gray-500 dark:text-gray-400",on:"flex items-center text-sm font-medium text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white"},icon:"mr-2 h-4 w-4"}},Ui={base:"group relative flex items-stretch justify-center p-0.5 text-center font-medium transition-[color,background-color,border-color,text-decoration-color,fill,stroke,box-shadow] focus:z-10 focus:outline-none",fullSized:"w-full",color:{dark:"border border-transparent bg-gray-800 text-white focus:ring-4 focus:ring-gray-300 enabled:hover:bg-gray-900 dark:border-gray-700 dark:bg-gray-800 dark:focus:ring-gray-800 dark:enabled:hover:bg-gray-700",failure:"border border-transparent bg-red-700 text-white focus:ring-4 focus:ring-red-300 enabled:hover:bg-red-800 dark:bg-red-600 dark:focus:ring-red-900 dark:enabled:hover:bg-red-700",gray:":ring-cyan-700 border border-gray-200 bg-white text-gray-900 focus:text-cyan-700 focus:ring-4 enabled:hover:bg-gray-100 enabled:hover:text-cyan-700 dark:border-gray-600 dark:bg-transparent dark:text-gray-400 dark:enabled:hover:bg-gray-700 dark:enabled:hover:text-white",info:"border border-transparent bg-cyan-700 text-white focus:ring-4 focus:ring-cyan-300 enabled:hover:bg-cyan-800 dark:bg-cyan-600 dark:focus:ring-cyan-800 dark:enabled:hover:bg-cyan-700",light:"border border-gray-300 bg-white text-gray-900 focus:ring-4 focus:ring-cyan-300 enabled:hover:bg-gray-100 dark:border-gray-600 dark:bg-gray-600 dark:text-white dark:focus:ring-gray-700 dark:enabled:hover:border-gray-700 dark:enabled:hover:bg-gray-700",purple:"border border-transparent bg-purple-700 text-white focus:ring-4 focus:ring-purple-300 enabled:hover:bg-purple-800 dark:bg-purple-600 dark:focus:ring-purple-900 dark:enabled:hover:bg-purple-700",success:"border border-transparent bg-green-700 text-white focus:ring-4 focus:ring-green-300 enabled:hover:bg-green-800 dark:bg-green-600 dark:focus:ring-green-800 dark:enabled:hover:bg-green-700",warning:"border border-transparent bg-yellow-400 text-white focus:ring-4 focus:ring-yellow-300 enabled:hover:bg-yellow-500 dark:focus:ring-yellow-900",blue:"border border-transparent bg-blue-700 text-white focus:ring-4 focus:ring-blue-300 enabled:hover:bg-blue-800 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800",cyan:"border border-cyan-300 bg-white text-cyan-900 focus:ring-4 focus:ring-cyan-300 enabled:hover:bg-cyan-100 dark:border-cyan-600 dark:bg-cyan-600 dark:text-white dark:focus:ring-cyan-700 dark:enabled:hover:border-cyan-700 dark:enabled:hover:bg-cyan-700",green:"border border-green-300 bg-white text-green-900 focus:ring-4 focus:ring-green-300 enabled:hover:bg-green-100 dark:border-green-600 dark:bg-green-600 dark:text-white dark:focus:ring-green-700 dark:enabled:hover:border-green-700 dark:enabled:hover:bg-green-700",indigo:"border border-indigo-300 bg-white text-indigo-900 focus:ring-4 focus:ring-indigo-300 enabled:hover:bg-indigo-100 dark:border-indigo-600 dark:bg-indigo-600 dark:text-white dark:focus:ring-indigo-700 dark:enabled:hover:border-indigo-700 dark:enabled:hover:bg-indigo-700",lime:"border border-lime-300 bg-white text-lime-900 focus:ring-4 focus:ring-lime-300 enabled:hover:bg-lime-100 dark:border-lime-600 dark:bg-lime-600 dark:text-white dark:focus:ring-lime-700 dark:enabled:hover:border-lime-700 dark:enabled:hover:bg-lime-700",pink:"border border-pink-300 bg-white text-pink-900 focus:ring-4 focus:ring-pink-300 enabled:hover:bg-pink-100 dark:border-pink-600 dark:bg-pink-600 dark:text-white dark:focus:ring-pink-700 dark:enabled:hover:border-pink-700 dark:enabled:hover:bg-pink-700",red:"border border-red-300 bg-white text-red-900 focus:ring-4 focus:ring-red-300 enabled:hover:bg-red-100 dark:border-red-600 dark:bg-red-600 dark:text-white dark:focus:ring-red-700 dark:enabled:hover:border-red-700 dark:enabled:hover:bg-red-700",teal:"border border-teal-300 bg-white text-teal-900 focus:ring-4 focus:ring-teal-300 enabled:hover:bg-teal-100 dark:border-teal-600 dark:bg-teal-600 dark:text-white dark:focus:ring-teal-700 dark:enabled:hover:border-teal-700 dark:enabled:hover:bg-teal-700",yellow:"border border-yellow-300 bg-white text-yellow-900 focus:ring-4 focus:ring-yellow-300 enabled:hover:bg-yellow-100 dark:border-yellow-600 dark:bg-yellow-600 dark:text-white dark:focus:ring-yellow-700 dark:enabled:hover:border-yellow-700 dark:enabled:hover:bg-yellow-700"},disabled:"cursor-not-allowed opacity-50",isProcessing:"cursor-wait",spinnerSlot:"absolute top-0 flex h-full items-center",spinnerLeftPosition:{xs:"left-2",sm:"left-3",md:"left-4",lg:"left-5",xl:"left-6"},gradient:{cyan:"bg-gradient-to-r from-cyan-400 via-cyan-500 to-cyan-600 text-white focus:ring-4 focus:ring-cyan-300 enabled:hover:bg-gradient-to-br dark:focus:ring-cyan-800",failure:"bg-gradient-to-r from-red-400 via-red-500 to-red-600 text-white focus:ring-4 focus:ring-red-300 enabled:hover:bg-gradient-to-br dark:focus:ring-red-800",info:"bg-gradient-to-r from-cyan-500 via-cyan-600 to-cyan-700 text-white focus:ring-4 focus:ring-cyan-300 enabled:hover:bg-gradient-to-br dark:focus:ring-cyan-800",lime:"bg-gradient-to-r from-lime-200 via-lime-400 to-lime-500 text-gray-900 focus:ring-4 focus:ring-lime-300 enabled:hover:bg-gradient-to-br dark:focus:ring-lime-800",pink:"bg-gradient-to-r from-pink-400 via-pink-500 to-pink-600 text-white focus:ring-4 focus:ring-pink-300 enabled:hover:bg-gradient-to-br dark:focus:ring-pink-800",purple:"bg-gradient-to-r from-purple-500 via-purple-600 to-purple-700 text-white focus:ring-4 focus:ring-purple-300 enabled:hover:bg-gradient-to-br dark:focus:ring-purple-800",success:"bg-gradient-to-r from-green-400 via-green-500 to-green-600 text-white focus:ring-4 focus:ring-green-300 enabled:hover:bg-gradient-to-br dark:focus:ring-green-800",teal:"bg-gradient-to-r from-teal-400 via-teal-500 to-teal-600 text-white focus:ring-4 focus:ring-teal-300 enabled:hover:bg-gradient-to-br dark:focus:ring-teal-800"},gradientDuoTone:{cyanToBlue:"bg-gradient-to-r from-cyan-500 to-cyan-500 text-white focus:ring-4 focus:ring-cyan-300 enabled:hover:bg-gradient-to-bl dark:focus:ring-cyan-800",greenToBlue:"bg-gradient-to-br from-green-400 to-cyan-600 text-white focus:ring-4 focus:ring-green-200 enabled:hover:bg-gradient-to-bl dark:focus:ring-green-800",pinkToOrange:"bg-gradient-to-br from-pink-500 to-orange-400 text-white focus:ring-4 focus:ring-pink-200 enabled:hover:bg-gradient-to-bl dark:focus:ring-pink-800",purpleToBlue:"bg-gradient-to-br from-purple-600 to-cyan-500 text-white focus:ring-4 focus:ring-cyan-300 enabled:hover:bg-gradient-to-bl dark:focus:ring-cyan-800",purpleToPink:"bg-gradient-to-r from-purple-500 to-pink-500 text-white focus:ring-4 focus:ring-purple-200 enabled:hover:bg-gradient-to-l dark:focus:ring-purple-800",redToYellow:"bg-gradient-to-r from-red-200 via-red-300 to-yellow-200 text-gray-900 focus:ring-4 focus:ring-red-100 enabled:hover:bg-gradient-to-bl dark:focus:ring-red-400",tealToLime:"bg-gradient-to-r from-teal-200 to-lime-200 text-gray-900 focus:ring-4 focus:ring-lime-200 enabled:hover:bg-gradient-to-l enabled:hover:from-teal-200 enabled:hover:to-lime-200 enabled:hover:text-gray-900 dark:focus:ring-teal-700"},inner:{base:"flex items-stretch transition-all duration-200",position:{none:"",start:"rounded-r-none",middle:"rounded-none",end:"rounded-l-none"},outline:"border border-transparent",isProcessingPadding:{xs:"pl-8",sm:"pl-10",md:"pl-12",lg:"pl-16",xl:"pl-20"}},label:"ml-2 inline-flex h-4 w-4 items-center justify-center rounded-full bg-cyan-200 text-xs font-semibold text-cyan-800",outline:{color:{gray:"border border-gray-900 dark:border-white",default:"border-0",light:""},off:"",on:"flex w-full justify-center bg-white text-gray-900 transition-all duration-75 ease-in group-enabled:group-hover:bg-opacity-0 group-enabled:group-hover:text-inherit dark:bg-gray-900 dark:text-white",pill:{off:"rounded-md",on:"rounded-full"}},pill:{off:"rounded-lg",on:"rounded-full"},size:{xs:"px-2 py-1 text-xs",sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-sm",lg:"px-5 py-2.5 text-base",xl:"px-6 py-3 text-base"}},Xi={base:"inline-flex",position:{none:"",start:"rounded-r-none focus:ring-2",middle:"rounded-none border-l-0 pl-0 focus:ring-2",end:"rounded-l-none border-l-0 pl-0 focus:ring-2"}},Ji={root:{base:"flex rounded-lg border border-gray-200 bg-white shadow-md dark:border-gray-700 dark:bg-gray-800",children:"flex h-full flex-col justify-center gap-4 p-6",horizontal:{off:"flex-col",on:"flex-col md:max-w-xl md:flex-row"},href:"hover:bg-gray-100 dark:hover:bg-gray-700"},img:{base:"",horizontal:{off:"rounded-t-lg",on:"h-96 w-full rounded-t-lg object-cover md:h-auto md:w-48 md:rounded-none md:rounded-l-lg"}}},Zi={root:{base:"relative h-full w-full",leftControl:"absolute left-0 top-0 flex h-full items-center justify-center px-4 focus:outline-none",rightControl:"absolute right-0 top-0 flex h-full items-center justify-center px-4 focus:outline-none"},indicators:{active:{off:"bg-white/50 hover:bg-white dark:bg-gray-800/50 dark:hover:bg-gray-800",on:"bg-white dark:bg-gray-800"},base:"h-3 w-3 rounded-full",wrapper:"absolute bottom-5 left-1/2 flex -translate-x-1/2 space-x-3"},item:{base:"absolute left-1/2 top-1/2 block w-full -translate-x-1/2 -translate-y-1/2",wrapper:{off:"w-full flex-shrink-0 transform cursor-default snap-center",on:"w-full flex-shrink-0 transform cursor-grab snap-center"}},control:{base:"inline-flex h-8 w-8 items-center justify-center rounded-full bg-white/30 group-hover:bg-white/50 group-focus:outline-none group-focus:ring-4 group-focus:ring-white dark:bg-gray-800/30 dark:group-hover:bg-gray-800/60 dark:group-focus:ring-gray-800/70 sm:h-10 sm:w-10",icon:"h-5 w-5 text-white dark:text-gray-800 sm:h-6 sm:w-6"},scrollContainer:{base:"flex h-full snap-mandatory overflow-y-hidden overflow-x-scroll scroll-smooth rounded-lg",snap:"snap-x"}},Qi={root:{base:"h-4 w-4 rounded border border-gray-300 bg-gray-100 focus:ring-2 dark:border-gray-600 dark:bg-gray-700",color:{default:"text-cyan-600 focus:ring-cyan-600 dark:ring-offset-gray-800 dark:focus:ring-cyan-600",dark:"text-gray-800 focus:ring-gray-800 dark:ring-offset-gray-800 dark:focus:ring-gray-800",failure:"text-red-900 focus:ring-red-900 dark:ring-offset-red-900 dark:focus:ring-red-900",gray:"text-gray-900 focus:ring-gray-900 dark:ring-offset-gray-900 dark:focus:ring-gray-900",info:"text-cyan-800 focus:ring-cyan-800 dark:ring-offset-gray-800 dark:focus:ring-cyan-800",light:"text-gray-900 focus:ring-gray-900 dark:ring-offset-gray-900 dark:focus:ring-gray-900",purple:"text-purple-600 focus:ring-purple-600 dark:ring-offset-purple-600 dark:focus:ring-purple-600",success:"text-green-800 focus:ring-green-800 dark:ring-offset-green-800 dark:focus:ring-green-800",warning:"text-yellow-400 focus:ring-yellow-400 dark:ring-offset-yellow-400 dark:focus:ring-yellow-400",blue:"text-blue-700 focus:ring-blue-600 dark:ring-offset-blue-700 dark:focus:ring-blue-700",cyan:"text-cyan-600 focus:ring-cyan-600 dark:ring-offset-cyan-600 dark:focus:ring-cyan-600",green:"text-green-600 focus:ring-green-600 dark:ring-offset-green-600 dark:focus:ring-green-600",indigo:"text-indigo-700 focus:ring-indigo-700 dark:ring-offset-indigo-700 dark:focus:ring-indigo-700",lime:"text-lime-700 focus:ring-lime-700 dark:ring-offset-lime-700 dark:focus:ring-lime-700",pink:"text-pink-600 focus:ring-pink-600 dark:ring-offset-pink-600 dark:focus:ring-pink-600",red:"text-red-600 focus:ring-red-600 dark:ring-offset-red-600 dark:focus:ring-red-600",teal:"text-teal-600 focus:ring-teal-600 dark:ring-offset-teal-600 dark:focus:ring-teal-600",yellow:"text-yellow-400 focus:ring-yellow-400 dark:ring-offset-yellow-400 dark:focus:ring-yellow-400"}}},el={button:{base:"inline-flex w-full items-center justify-center rounded-lg bg-blue-700 px-5 py-3 hover:bg-blue-800 focus:outline-none focus:ring-4 focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800",label:"text-center text-sm font-medium text-white sm:w-auto"},withIcon:{base:"absolute end-2 top-1/2 inline-flex -translate-y-1/2 items-center justify-center rounded-lg p-2 text-gray-500 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-800",icon:{defaultIcon:"h-4 w-4",successIcon:"h-4 w-4 text-blue-700 dark:text-blue-500"}},withIconText:{base:"absolute end-2.5 top-1/2 inline-flex -translate-y-1/2 items-center justify-center rounded-lg border border-gray-200 bg-white px-2.5 py-2 text-gray-900 hover:bg-gray-100 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700",icon:{defaultIcon:"me-1.5 h-3 w-3",successIcon:"me-1.5 h-3 w-3 text-blue-700 dark:text-blue-500"},label:{base:"inline-flex items-center",defaultText:"text-xs font-semibold",successText:"text-xs font-semibold text-blue-700 dark:text-blue-500"}}},tl={root:{base:"rounded-lg p-2.5 text-sm text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-4 focus:ring-gray-200 dark:text-gray-400 dark:hover:bg-gray-700 dark:focus:ring-gray-700",icon:"h-5 w-5"}},rl={root:{base:"relative"},popup:{root:{base:"absolute top-10 z-50 block pt-2",inline:"relative top-0 z-auto",inner:"inline-block rounded-lg bg-white p-4 shadow-lg dark:bg-gray-700"},header:{base:"",title:"px-2 py-3 text-center font-semibold text-gray-900 dark:text-white",selectors:{base:"mb-2 flex justify-between",button:{base:"rounded-lg bg-white px-5 py-2.5 text-sm font-semibold text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-200 dark:bg-gray-700 dark:text-white dark:hover:bg-gray-600",prev:"",next:"",view:""}}},view:{base:"p-1"},footer:{base:"mt-2 flex space-x-2",button:{base:"w-full rounded-lg px-5 py-2 text-center text-sm font-medium focus:ring-4 focus:ring-cyan-300",today:"bg-cyan-700 text-white hover:bg-cyan-800 dark:bg-cyan-600 dark:hover:bg-cyan-700",clear:"border border-gray-300 bg-white text-gray-900 hover:bg-gray-100 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:hover:bg-gray-600"}}},views:{days:{header:{base:"mb-1 grid grid-cols-7",title:"h-6 text-center text-sm font-medium leading-6 text-gray-500 dark:text-gray-400"},items:{base:"grid w-64 grid-cols-7",item:{base:"block flex-1 cursor-pointer rounded-lg border-0 text-center text-sm font-semibold leading-9 text-gray-900 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-600",selected:"bg-cyan-700 text-white hover:bg-cyan-600",disabled:"text-gray-500"}}},months:{items:{base:"grid w-64 grid-cols-4",item:{base:"block flex-1 cursor-pointer rounded-lg border-0 text-center text-sm font-semibold leading-9 text-gray-900 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-600",selected:"bg-cyan-700 text-white hover:bg-cyan-600",disabled:"text-gray-500"}}},years:{items:{base:"grid w-64 grid-cols-4",item:{base:"block flex-1 cursor-pointer rounded-lg border-0 text-center text-sm font-semibold leading-9 text-gray-900 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-600",selected:"bg-cyan-700 text-white hover:bg-cyan-600",disabled:"text-gray-500"}}},decades:{items:{base:"grid w-64 grid-cols-4",item:{base:"block flex-1 cursor-pointer rounded-lg border-0 text-center text-sm font-semibold leading-9 text-gray-900 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-600",selected:"bg-cyan-700 text-white hover:bg-cyan-600",disabled:"text-gray-500"}}}}},nl={root:{base:"fixed z-40 overflow-y-auto bg-white p-4 transition-transform dark:bg-gray-800",backdrop:"fixed inset-0 z-30 bg-gray-900/50 dark:bg-gray-900/80",edge:"bottom-16",position:{top:{on:"left-0 right-0 top-0 w-full transform-none",off:"left-0 right-0 top-0 w-full -translate-y-full"},right:{on:"right-0 top-0 h-screen w-80 transform-none",off:"right-0 top-0 h-screen w-80 translate-x-full"},bottom:{on:"bottom-0 left-0 right-0 w-full transform-none",off:"bottom-0 left-0 right-0 w-full translate-y-full"},left:{on:"left-0 top-0 h-screen w-80 transform-none",off:"left-0 top-0 h-screen w-80 -translate-x-full"}}},header:{inner:{closeButton:"absolute end-2.5 top-2.5 flex h-8 w-8 items-center justify-center rounded-lg bg-transparent text-sm text-gray-400 hover:bg-gray-200 hover:text-gray-900 dark:hover:bg-gray-600 dark:hover:text-white",closeIcon:"h-4 w-4",titleIcon:"me-2.5 h-4 w-4",titleText:"mb-4 inline-flex items-center text-base font-semibold text-gray-500 dark:text-gray-400"},collapsed:{on:"hidden",off:"block"}},items:{base:""}},_e={arrowIcon:"ml-2 h-4 w-4",content:"py-1 focus:outline-none",floating:{animation:"transition-opacity",arrow:{base:"absolute z-10 h-2 w-2 rotate-45",style:{dark:"bg-gray-900 dark:bg-gray-700",light:"bg-white",auto:"bg-white dark:bg-gray-700"},placement:"-4px"},base:"z-10 w-fit divide-y divide-gray-100 rounded shadow focus:outline-none",content:"py-1 text-sm text-gray-700 dark:text-gray-200",divider:"my-1 h-px bg-gray-100 dark:bg-gray-600",header:"block px-4 py-2 text-sm text-gray-700 dark:text-gray-200",hidden:"invisible opacity-0",item:{container:"",base:"flex w-full cursor-pointer items-center justify-start px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 focus:bg-gray-100 focus:outline-none dark:text-gray-200 dark:hover:bg-gray-600 dark:hover:text-white dark:focus:bg-gray-600 dark:focus:text-white",icon:"mr-2 h-4 w-4"},style:{dark:"bg-gray-900 text-white dark:bg-gray-700",light:"border border-gray-200 bg-white text-gray-900",auto:"border border-gray-200 bg-white text-gray-900 dark:border-none dark:bg-gray-700 dark:text-white"},target:"w-fit"},inlineWrapper:"flex items-center"},ol={root:{base:"flex"},field:{base:"relative w-full",input:{base:"block w-full overflow-hidden rounded-lg border disabled:cursor-not-allowed disabled:opacity-50",sizes:{sm:"sm:text-xs",md:"text-sm",lg:"sm:text-base"},colors:{gray:"border-gray-300 bg-gray-50 text-gray-900 focus:border-cyan-500 focus:ring-cyan-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-cyan-500 dark:focus:ring-cyan-500",info:"border-cyan-500 bg-cyan-50 text-cyan-900 placeholder-cyan-700 focus:border-cyan-500 focus:ring-cyan-500 dark:border-cyan-400 dark:bg-cyan-100 dark:focus:border-cyan-500 dark:focus:ring-cyan-500",failure:"border-red-500 bg-red-50 text-red-900 placeholder-red-700 focus:border-red-500 focus:ring-red-500 dark:border-red-400 dark:bg-red-100 dark:focus:border-red-500 dark:focus:ring-red-500",warning:"border-yellow-500 bg-yellow-50 text-yellow-900 placeholder-yellow-700 focus:border-yellow-500 focus:ring-yellow-500 dark:border-yellow-400 dark:bg-yellow-100 dark:focus:border-yellow-500 dark:focus:ring-yellow-500",success:"border-green-500 bg-green-50 text-green-900 placeholder-green-700 focus:border-green-500 focus:ring-green-500 dark:border-green-400 dark:bg-green-100 dark:focus:border-green-500 dark:focus:ring-green-500"}}}},al={input:{default:{filled:{sm:"peer block w-full appearance-none rounded-t-lg border-0 border-b-2 border-gray-300 bg-gray-50 px-2.5 pb-2.5 pt-5 text-xs text-gray-900 focus:border-blue-600 focus:outline-none focus:ring-0 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:focus:border-blue-500",md:"peer block w-full appearance-none rounded-t-lg border-0 border-b-2 border-gray-300 bg-gray-50 px-2.5 pb-2.5 pt-5 text-sm text-gray-900 focus:border-blue-600 focus:outline-none focus:ring-0 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:focus:border-blue-500"},outlined:{sm:"peer block w-full appearance-none rounded-lg border border-gray-300 bg-transparent px-2.5 pb-2.5 pt-4 text-xs text-gray-900 focus:border-blue-600 focus:outline-none focus:ring-0 dark:border-gray-600 dark:text-white dark:focus:border-blue-500",md:"peer block w-full appearance-none rounded-lg border border-gray-300 bg-transparent px-2.5 pb-2.5 pt-4 text-sm text-gray-900 focus:border-blue-600 focus:outline-none focus:ring-0 dark:border-gray-600 dark:text-white dark:focus:border-blue-500"},standard:{sm:"peer block w-full appearance-none border-0 border-b-2 border-gray-300 bg-transparent px-0 py-2.5 text-xs text-gray-900 focus:border-blue-600 focus:outline-none focus:ring-0 dark:border-gray-600 dark:text-white dark:focus:border-blue-500",md:"peer block w-full appearance-none border-0 border-b-2 border-gray-300 bg-transparent px-0 py-2.5 text-sm text-gray-900 focus:border-blue-600 focus:outline-none focus:ring-0 dark:border-gray-600 dark:text-white dark:focus:border-blue-500"}},success:{filled:{sm:"peer block w-full appearance-none rounded-t-lg border-0 border-b-2 border-green-600 bg-gray-50 px-2.5 pb-2.5 pt-5 text-xs text-gray-900 focus:border-green-600 focus:outline-none focus:ring-0 dark:border-green-500 dark:bg-gray-700 dark:text-white dark:focus:border-green-500",md:"peer block w-full appearance-none rounded-t-lg border-0 border-b-2 border-green-600 bg-gray-50 px-2.5 pb-2.5 pt-5 text-sm text-gray-900 focus:border-green-600 focus:outline-none focus:ring-0 dark:border-green-500 dark:bg-gray-700 dark:text-white dark:focus:border-green-500"},outlined:{sm:"peer block w-full appearance-none rounded-lg border border-green-600 bg-transparent px-2.5 pb-2.5 pt-4 text-xs text-gray-900 focus:border-green-600 focus:outline-none focus:ring-0 dark:border-green-500 dark:text-white dark:focus:border-green-500",md:"peer block w-full appearance-none rounded-lg border border-green-600 bg-transparent px-2.5 pb-2.5 pt-4 text-sm text-gray-900 focus:border-green-600 focus:outline-none focus:ring-0 dark:border-green-500 dark:text-white dark:focus:border-green-500"},standard:{sm:"peer block w-full appearance-none border-0 border-b-2 border-green-600 bg-transparent px-0 py-2.5 text-xs text-gray-900 focus:border-green-600 focus:outline-none focus:ring-0 dark:border-green-500 dark:text-white dark:focus:border-green-500",md:"peer block w-full appearance-none border-0 border-b-2 border-green-600 bg-transparent px-0 py-2.5 text-sm text-gray-900 focus:border-green-600 focus:outline-none focus:ring-0 dark:border-green-500 dark:text-white dark:focus:border-green-500"}},error:{filled:{sm:"peer block w-full appearance-none rounded-t-lg border-0 border-b-2 border-red-600 bg-gray-50 px-2.5 pb-2.5 pt-5 text-xs text-gray-900 focus:border-red-600 focus:outline-none focus:ring-0 dark:border-red-500 dark:bg-gray-700 dark:text-white dark:focus:border-red-500",md:"peer block w-full appearance-none rounded-t-lg border-0 border-b-2 border-red-600 bg-gray-50 px-2.5 pb-2.5 pt-5 text-sm text-gray-900 focus:border-red-600 focus:outline-none focus:ring-0 dark:border-red-500 dark:bg-gray-700 dark:text-white dark:focus:border-red-500"},outlined:{sm:"peer block w-full appearance-none rounded-lg border border-red-600 bg-transparent px-2.5 pb-2.5 pt-4 text-xs text-gray-900 focus:border-red-600 focus:outline-none focus:ring-0 dark:border-red-500 dark:text-white dark:focus:border-red-500",md:"peer block w-full appearance-none rounded-lg border border-red-600 bg-transparent px-2.5 pb-2.5 pt-4 text-sm text-gray-900 focus:border-red-600 focus:outline-none focus:ring-0 dark:border-red-500 dark:text-white dark:focus:border-red-500"},standard:{sm:"peer block w-full appearance-none border-0 border-b-2 border-red-600 bg-transparent px-0 py-2.5 text-xs text-gray-900 focus:border-red-600 focus:outline-none focus:ring-0 dark:border-red-500 dark:text-white dark:focus:border-red-500",md:"peer block w-full appearance-none border-0 border-b-2 border-red-600 bg-transparent px-0 py-2.5 text-sm text-gray-900 focus:border-red-600 focus:outline-none focus:ring-0 dark:border-red-500 dark:text-white dark:focus:border-red-500"}}},label:{default:{filled:{sm:"absolute left-2.5 top-4 z-10 origin-[0] -translate-y-4 scale-75 text-xs text-gray-500 transition-transform duration-300 peer-placeholder-shown:translate-y-0 peer-placeholder-shown:scale-100 peer-focus:-translate-y-4 peer-focus:scale-75 peer-focus:text-blue-600 dark:text-gray-400 peer-focus:dark:text-blue-500",md:"absolute left-2.5 top-4 z-10 origin-[0] -translate-y-4 scale-75 text-sm text-gray-500 transition-transform duration-300 peer-placeholder-shown:translate-y-0 peer-placeholder-shown:scale-100 peer-focus:-translate-y-4 peer-focus:scale-75 peer-focus:text-blue-600 dark:text-gray-400 peer-focus:dark:text-blue-500"},outlined:{sm:"absolute left-1 top-2 z-10 origin-[0] -translate-y-4 scale-75 bg-white px-2 text-xs text-gray-500 transition-transform duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:scale-100 peer-focus:top-2 peer-focus:-translate-y-4 peer-focus:scale-75 peer-focus:px-2 peer-focus:text-blue-600 dark:bg-gray-900 dark:text-gray-400 peer-focus:dark:text-blue-500",md:"absolute left-1 top-2 z-10 origin-[0] -translate-y-4 scale-75 bg-white px-2 text-sm text-gray-500 transition-transform duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:scale-100 peer-focus:top-2 peer-focus:-translate-y-4 peer-focus:scale-75 peer-focus:px-2 peer-focus:text-blue-600 dark:bg-gray-900 dark:text-gray-400 peer-focus:dark:text-blue-500"},standard:{sm:"absolute top-3 -z-10 origin-[0] -translate-y-6 scale-75 text-xs text-gray-500 transition-transform duration-300 peer-placeholder-shown:translate-y-0 peer-placeholder-shown:scale-100 peer-focus:left-0 peer-focus:-translate-y-6 peer-focus:scale-75 peer-focus:text-blue-600 dark:text-gray-400 peer-focus:dark:text-blue-500",md:"absolute top-3 -z-10 origin-[0] -translate-y-6 scale-75 text-sm text-gray-500 transition-transform duration-300 peer-placeholder-shown:translate-y-0 peer-placeholder-shown:scale-100 peer-focus:left-0 peer-focus:-translate-y-6 peer-focus:scale-75 peer-focus:text-blue-600 dark:text-gray-400 peer-focus:dark:text-blue-500"}},success:{filled:{sm:"absolute left-2.5 top-4 z-10 origin-[0] -translate-y-4 scale-75 text-sm text-green-600 transition-transform duration-300 peer-placeholder-shown:translate-y-0 peer-placeholder-shown:scale-100 peer-focus:-translate-y-4 peer-focus:scale-75 dark:text-green-500",md:"absolute left-2.5 top-4 z-10 origin-[0] -translate-y-4 scale-75 text-sm text-green-600 transition-transform duration-300 peer-placeholder-shown:translate-y-0 peer-placeholder-shown:scale-100 peer-focus:-translate-y-4 peer-focus:scale-75 dark:text-green-500"},outlined:{sm:"absolute left-1 top-2 z-10 origin-[0] -translate-y-4 scale-75 bg-white px-2 text-sm text-green-600 transition-transform duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:scale-100 peer-focus:top-2 peer-focus:-translate-y-4 peer-focus:scale-75 peer-focus:px-2 dark:bg-gray-900 dark:text-green-500",md:"absolute left-1 top-2 z-10 origin-[0] -translate-y-4 scale-75 bg-white px-2 text-sm text-green-600 transition-transform duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:scale-100 peer-focus:top-2 peer-focus:-translate-y-4 peer-focus:scale-75 peer-focus:px-2 dark:bg-gray-900 dark:text-green-500"},standard:{sm:"absolute top-3 -z-10 origin-[0] -translate-y-6 scale-75 text-xs text-green-600 transition-transform duration-300 peer-placeholder-shown:translate-y-0 peer-placeholder-shown:scale-100 peer-focus:left-0 peer-focus:-translate-y-6 peer-focus:scale-75 dark:text-green-500",md:"absolute top-3 -z-10 origin-[0] -translate-y-6 scale-75 text-sm text-green-600 transition-transform duration-300 peer-placeholder-shown:translate-y-0 peer-placeholder-shown:scale-100 peer-focus:left-0 peer-focus:-translate-y-6 peer-focus:scale-75 dark:text-green-500"}},error:{filled:{sm:"absolute left-2.5 top-4 z-10 origin-[0] -translate-y-4 scale-75 text-xs text-red-600 transition-transform duration-300 peer-placeholder-shown:translate-y-0 peer-placeholder-shown:scale-100 peer-focus:-translate-y-4 peer-focus:scale-75 dark:text-red-500",md:"absolute left-2.5 top-4 z-10 origin-[0] -translate-y-4 scale-75 text-xs text-red-600 transition-transform duration-300 peer-placeholder-shown:translate-y-0 peer-placeholder-shown:scale-100 peer-focus:-translate-y-4 peer-focus:scale-75 dark:text-red-500"},outlined:{sm:"absolute left-1 top-2 z-10 origin-[0] -translate-y-4 scale-75 bg-white px-2 text-xs text-red-600 transition-transform duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:scale-100 peer-focus:top-2 peer-focus:-translate-y-4 peer-focus:scale-75 peer-focus:px-2 dark:bg-gray-900 dark:text-red-500",md:"absolute left-1 top-2 z-10 origin-[0] -translate-y-4 scale-75 bg-white px-2 text-xs text-red-600 transition-transform duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:scale-100 peer-focus:top-2 peer-focus:-translate-y-4 peer-focus:scale-75 peer-focus:px-2 dark:bg-gray-900 dark:text-red-500"},standard:{sm:"absolute top-3 -z-10 origin-[0] -translate-y-6 scale-75 text-xs text-red-600 transition-transform duration-300 peer-placeholder-shown:translate-y-0 peer-placeholder-shown:scale-100 peer-focus:left-0 peer-focus:-translate-y-6 peer-focus:scale-75 dark:text-red-500",md:"absolute top-3 -z-10 origin-[0] -translate-y-6 scale-75 text-sm text-red-600 transition-transform duration-300 peer-placeholder-shown:translate-y-0 peer-placeholder-shown:scale-100 peer-focus:left-0 peer-focus:-translate-y-6 peer-focus:scale-75 dark:text-red-500"}}},helperText:{default:"mt-2 text-xs text-gray-600 dark:text-gray-400",success:"mt-2 text-xs text-green-600 dark:text-green-400",error:"mt-2 text-xs text-red-600 dark:text-red-400"}},sl={root:{base:"w-full rounded-lg bg-white shadow dark:bg-gray-800 md:flex md:items-center md:justify-between",container:"w-full p-6",bgDark:"bg-gray-800"},groupLink:{base:"flex flex-wrap text-sm text-gray-500 dark:text-white",link:{base:"me-4 last:mr-0 md:mr-6",href:"hover:underline"},col:"flex-col space-y-4"},icon:{base:"text-gray-500 dark:hover:text-white",size:"h-5 w-5"},title:{base:"mb-6 text-sm font-semibold uppercase text-gray-500 dark:text-white"},divider:{base:"my-6 w-full border-gray-200 dark:border-gray-700 sm:mx-auto lg:my-8"},copyright:{base:"text-sm text-gray-500 dark:text-gray-400 sm:text-center",href:"ml-1 hover:underline",span:"ml-1"},brand:{base:"mb-4 flex items-center sm:mb-0",img:"mr-3 h-8",span:"self-center whitespace-nowrap text-2xl font-semibold text-gray-800 dark:text-white"}},il={root:{base:"mt-2 text-sm",colors:{gray:"text-gray-500 dark:text-gray-400",info:"text-cyan-700 dark:text-cyan-800",success:"text-green-600 dark:text-green-500",failure:"text-red-600 dark:text-red-500",warning:"text-yellow-500 dark:text-yellow-600"}}},ll={root:{base:"my-8 h-px border-0 bg-gray-200 dark:bg-gray-700"},trimmed:{base:"mx-auto my-4 h-1 w-48 rounded border-0 bg-gray-100 dark:bg-gray-700 md:my-10"},icon:{base:"inline-flex w-full items-center justify-center",hrLine:"my-8 h-1 w-64 rounded border-0 bg-gray-200 dark:bg-gray-700",icon:{base:"absolute left-1/2 -translate-x-1/2 bg-white px-4 dark:bg-gray-900",icon:"h-4 w-4 text-gray-700 dark:text-gray-300"}},text:{base:"inline-flex w-full items-center justify-center",hrLine:"my-8 h-px w-64 border-0 bg-gray-200 dark:bg-gray-700",text:"absolute left-1/2 -translate-x-1/2 bg-white px-3 font-medium text-gray-900 dark:bg-gray-900 dark:text-white"},square:{base:"mx-auto my-8 h-8 w-8 rounded border-0 bg-gray-200 dark:bg-gray-700 md:my-12"}},cl={root:{base:"rounded-lg border border-gray-200 bg-gray-100 px-2 py-1.5 text-xs font-semibold text-gray-800 dark:border-gray-500 dark:bg-gray-600 dark:text-gray-100",icon:"inline-block"}},dl={root:{base:"text-sm font-medium",disabled:"opacity-50",colors:{default:"text-gray-900 dark:text-white",info:"text-cyan-500 dark:text-cyan-600",failure:"text-red-700 dark:text-red-500",warning:"text-yellow-500 dark:text-yellow-600",success:"text-green-700 dark:text-green-500"}}},ul={root:{base:"list-inside space-y-1 text-gray-500 dark:text-gray-400",ordered:{off:"list-disc",on:"list-decimal"},horizontal:"flex list-none flex-wrap items-center justify-center space-x-4 space-y-0",unstyled:"list-none",nested:"mt-2 ps-5"},item:{withIcon:{off:"",on:"flex items-center"},icon:"me-2 h-3.5 w-3.5 flex-shrink-0"}},fl={root:{base:"list-none rounded-lg border border-gray-200 bg-white text-left text-sm font-medium text-gray-900 dark:border-gray-600 dark:bg-gray-700 dark:text-white"},item:{base:"[&>*]:first:rounded-t-lg [&>*]:last:rounded-b-lg [&>*]:last:border-b-0",link:{base:"flex w-full items-center border-b border-gray-200 px-4 py-2 dark:border-gray-600",active:{off:"hover:bg-gray-100 hover:text-cyan-700 focus:text-cyan-700 focus:outline-none focus:ring-2 focus:ring-cyan-700 dark:border-gray-600 dark:hover:bg-gray-600 dark:hover:text-white dark:focus:text-white dark:focus:ring-gray-500",on:"bg-cyan-700 text-white dark:bg-gray-800"},disabled:{off:"",on:"cursor-not-allowed bg-gray-100 text-gray-900 hover:bg-gray-100 hover:text-gray-900 focus:text-gray-900"},href:{off:"",on:""},icon:"mr-2 h-4 w-4 fill-current"}}},zt={root:{base:"bg-white px-2 py-2.5 dark:border-gray-700 dark:bg-gray-800 sm:px-4",rounded:{on:"rounded",off:""},bordered:{on:"border",off:""},inner:{base:"mx-auto flex flex-wrap items-center justify-between",fluid:{on:"",off:"container"}}},brand:{base:"flex items-center"},collapse:{base:"w-full md:block md:w-auto",list:"mt-4 flex flex-col md:mt-0 md:flex-row md:space-x-8 md:text-sm md:font-medium",hidden:{on:"hidden",off:""}},link:{base:"block py-2 pl-3 pr-4 md:p-0",active:{on:"bg-cyan-700 text-white dark:text-white md:bg-transparent md:text-cyan-700",off:"border-b border-gray-100 text-gray-700 hover:bg-gray-50 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white md:border-0 md:hover:bg-transparent md:hover:text-cyan-700 md:dark:hover:bg-transparent md:dark:hover:text-white"},disabled:{on:"text-gray-400 hover:cursor-not-allowed dark:text-gray-600",off:""}},toggle:{base:"inline-flex items-center rounded-lg p-2 text-sm text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-200 dark:text-gray-400 dark:hover:bg-gray-700 dark:focus:ring-gray-600 md:hidden",icon:"h-6 w-6 shrink-0"}},gl={...zt,dropdown:{base:"",toggle:{..._e,floating:{..._e.floating,base:x(_e.floating.base,"mt-2 block"),content:x(_e.floating.content,"text-gray-500 dark:text-gray-400"),style:{..._e.floating.style,auto:x(_e.floating.style.auto,"text-gray-500 dark:text-gray-400")}},inlineWrapper:x(_e.inlineWrapper,"flex w-full items-center justify-between")}},dropdownToggle:{base:x(zt.link.base,zt.link.active.off,"flex w-full items-center justify-between")}},bl={root:{base:"fixed inset-x-0 top-0 z-50 h-screen overflow-y-auto overflow-x-hidden md:inset-0 md:h-full",show:{on:"flex bg-gray-900 bg-opacity-50 dark:bg-opacity-80",off:"hidden"},sizes:{sm:"max-w-sm",md:"max-w-md",lg:"max-w-lg",xl:"max-w-xl","2xl":"max-w-2xl","3xl":"max-w-3xl","4xl":"max-w-4xl","5xl":"max-w-5xl","6xl":"max-w-6xl","7xl":"max-w-7xl"},positions:{"top-left":"items-start justify-start","top-center":"items-start justify-center","top-right":"items-start justify-end","center-left":"items-center justify-start",center:"items-center justify-center","center-right":"items-center justify-end","bottom-right":"items-end justify-end","bottom-center":"items-end justify-center","bottom-left":"items-end justify-start"}},content:{base:"relative h-full w-full p-4 md:h-auto",inner:"relative flex max-h-[90dvh] flex-col rounded-lg bg-white shadow dark:bg-gray-700"},body:{base:"flex-1 overflow-auto p-6",popup:"pt-0"},header:{base:"flex items-start justify-between rounded-t border-b p-5 dark:border-gray-600",popup:"border-b-0 p-2",title:"text-xl font-medium text-gray-900 dark:text-white",close:{base:"ml-auto inline-flex items-center rounded-lg bg-transparent p-1.5 text-sm text-gray-400 hover:bg-gray-200 hover:text-gray-900 dark:hover:bg-gray-600 dark:hover:text-white",icon:"h-5 w-5"}},footer:{base:"flex items-center space-x-2 rounded-b border-gray-200 p-6 dark:border-gray-600",popup:"border-t"}},pl={base:"",layout:{table:{base:"text-sm text-gray-700 dark:text-gray-400",span:"font-semibold text-gray-900 dark:text-white"}},pages:{base:"xs:mt-0 mt-2 inline-flex items-center -space-x-px",showIcon:"inline-flex",previous:{base:"ml-0 rounded-l-lg border border-gray-300 bg-white px-3 py-2 leading-tight text-gray-500 enabled:hover:bg-gray-100 enabled:hover:text-gray-700 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 enabled:dark:hover:bg-gray-700 enabled:dark:hover:text-white",icon:"h-5 w-5"},next:{base:"rounded-r-lg border border-gray-300 bg-white px-3 py-2 leading-tight text-gray-500 enabled:hover:bg-gray-100 enabled:hover:text-gray-700 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 enabled:dark:hover:bg-gray-700 enabled:dark:hover:text-white",icon:"h-5 w-5"},selector:{base:"w-12 border border-gray-300 bg-white py-2 leading-tight text-gray-500 enabled:hover:bg-gray-100 enabled:hover:text-gray-700 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 enabled:dark:hover:bg-gray-700 enabled:dark:hover:text-white",active:"bg-cyan-50 text-cyan-600 hover:bg-cyan-100 hover:text-cyan-700 dark:border-gray-700 dark:bg-gray-700 dark:text-white",disabled:"cursor-not-allowed opacity-50"}}},ml={base:"absolute z-20 inline-block w-max max-w-[100vw] bg-white outline-none border border-gray-200 rounded-lg shadow-sm dark:border-gray-600 dark:bg-gray-800",content:"z-10 overflow-hidden rounded-[7px]",arrow:{base:"absolute h-2 w-2 z-0 rotate-45 mix-blend-lighten bg-white border border-gray-200 dark:border-gray-600 dark:bg-gray-800 dark:mix-blend-color",placement:"-4px"}},hl={base:"w-full overflow-hidden rounded-full bg-gray-200 dark:bg-gray-700",label:"mb-1 flex justify-between font-medium dark:text-white",bar:"space-x-2 rounded-full text-center font-medium leading-none text-cyan-300 dark:text-cyan-100",color:{dark:"bg-gray-600 dark:bg-gray-300",blue:"bg-blue-600",red:"bg-red-600 dark:bg-red-500",green:"bg-green-600 dark:bg-green-500",yellow:"bg-yellow-400",indigo:"bg-indigo-600 dark:bg-indigo-500",purple:"bg-purple-600 dark:bg-purple-500",cyan:"bg-cyan-600",gray:"bg-gray-500",lime:"bg-lime-600",pink:"bg-pink-500",teal:"bg-teal-600"},size:{sm:"h-1.5",md:"h-2.5",lg:"h-4",xl:"h-6"}},yl={root:{base:"h-4 w-4 border border-gray-300 text-cyan-600 focus:ring-2 focus:ring-cyan-500 dark:border-gray-600 dark:bg-gray-700 dark:focus:bg-cyan-600 dark:focus:ring-cyan-600"}},xl={root:{base:"flex"},field:{base:"relative w-full",input:{base:"w-full cursor-pointer appearance-none rounded-lg bg-gray-200 dark:bg-gray-700",sizes:{sm:"h-1",md:"h-2",lg:"h-3"}}}},wl={root:{base:"flex items-center"},star:{empty:"text-gray-300 dark:text-gray-500",filled:"text-yellow-400",sizes:{sm:"h-5 w-5",md:"h-7 w-7",lg:"h-10 w-10"}}},vl={base:"flex items-center",label:"text-sm font-medium text-cyan-600 dark:text-cyan-500",progress:{base:"mx-4 h-5 w-2/4 rounded bg-gray-200 dark:bg-gray-700",fill:"h-5 rounded bg-yellow-400",label:"text-sm font-medium text-cyan-600 dark:text-cyan-500"}},kl={base:"flex",addon:"inline-flex items-center rounded-l-md border border-r-0 border-gray-300 bg-gray-200 px-3 text-sm text-gray-900 dark:border-gray-600 dark:bg-gray-600 dark:text-gray-400",field:{base:"relative w-full",icon:{base:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",svg:"h-5 w-5 text-gray-500 dark:text-gray-400"},select:{base:"block w-full border disabled:cursor-not-allowed disabled:opacity-50",withIcon:{on:"pl-10",off:""},withAddon:{on:"rounded-r-lg",off:"rounded-lg"},withShadow:{on:"shadow-sm dark:shadow-sm-light",off:""},sizes:{sm:"p-2 sm:text-xs",md:"p-2.5 text-sm",lg:"p-4 sm:text-base"},colors:{gray:"border-gray-300 bg-gray-50 text-gray-900 focus:border-cyan-500 focus:ring-cyan-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-cyan-500 dark:focus:ring-cyan-500",info:"border-cyan-500 bg-cyan-50 text-cyan-900 placeholder-cyan-700 focus:border-cyan-500 focus:ring-cyan-500 dark:border-cyan-400 dark:bg-cyan-100 dark:focus:border-cyan-500 dark:focus:ring-cyan-500",failure:"border-red-500 bg-red-50 text-red-900 placeholder-red-700 focus:border-red-500 focus:ring-red-500 dark:border-red-400 dark:bg-red-100 dark:focus:border-red-500 dark:focus:ring-red-500",warning:"border-yellow-500 bg-yellow-50 text-yellow-900 placeholder-yellow-700 focus:border-yellow-500 focus:ring-yellow-500 dark:border-yellow-400 dark:bg-yellow-100 dark:focus:border-yellow-500 dark:focus:ring-yellow-500",success:"border-green-500 bg-green-50 text-green-900 placeholder-green-700 focus:border-green-500 focus:ring-green-500 dark:border-green-400 dark:bg-green-100 dark:focus:border-green-500 dark:focus:ring-green-500"}}}},Cl={root:{base:"h-full",collapsed:{on:"w-16",off:"w-64"},inner:"h-full overflow-y-auto overflow-x-hidden rounded bg-gray-50 px-3 py-4 dark:bg-gray-800"},collapse:{button:"group flex w-full items-center rounded-lg p-2 text-base font-normal text-gray-900 transition duration-75 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-700",icon:{base:"h-6 w-6 text-gray-500 transition duration-75 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white",open:{off:"",on:"text-gray-900"}},label:{base:"ml-3 flex-1 whitespace-nowrap text-left",icon:{base:"h-6 w-6 transition delay-0 ease-in-out",open:{on:"rotate-180",off:""}}},list:"space-y-2 py-2"},cta:{base:"mt-6 rounded-lg bg-gray-100 p-4 dark:bg-gray-700",color:{blue:"bg-cyan-50 dark:bg-cyan-900",dark:"bg-dark-50 dark:bg-dark-900",failure:"bg-red-50 dark:bg-red-900",gray:"bg-alternative-50 dark:bg-alternative-900",green:"bg-green-50 dark:bg-green-900",light:"bg-light-50 dark:bg-light-900",red:"bg-red-50 dark:bg-red-900",purple:"bg-purple-50 dark:bg-purple-900",success:"bg-green-50 dark:bg-green-900",yellow:"bg-yellow-50 dark:bg-yellow-900",warning:"bg-yellow-50 dark:bg-yellow-900"}},item:{base:"flex items-center justify-center rounded-lg p-2 text-base font-normal text-gray-900 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-700",active:"bg-gray-100 dark:bg-gray-700",collapsed:{insideCollapse:"group w-full pl-8 transition duration-75",noIcon:"font-bold"},content:{base:"flex-1 whitespace-nowrap px-3"},icon:{base:"h-6 w-6 flex-shrink-0 text-gray-500 transition duration-75 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white",active:"text-gray-700 dark:text-gray-100"},label:"",listItem:""},items:{base:""},itemGroup:{base:"mt-4 space-y-2 border-t border-gray-200 pt-4 first:mt-0 first:border-t-0 first:pt-0 dark:border-gray-700"},logo:{base:"mb-5 flex items-center pl-2.5",collapsed:{on:"hidden",off:"self-center whitespace-nowrap text-xl font-semibold dark:text-white"},img:"mr-3 h-6 sm:h-7"}},Tl={base:"inline animate-spin text-gray-200",color:{failure:"fill-red-600",gray:"fill-gray-600",info:"fill-cyan-600",pink:"fill-pink-600",purple:"fill-purple-600",success:"fill-green-500",warning:"fill-yellow-400"},light:{off:{base:"dark:text-gray-600",color:{failure:"",gray:"dark:fill-gray-300",info:"",pink:"",purple:"",success:"",warning:""}},on:{base:"",color:{failure:"",gray:"",info:"",pink:"",purple:"",success:"",warning:""}}},size:{xs:"h-3 w-3",sm:"h-4 w-4",md:"h-6 w-6",lg:"h-8 w-8",xl:"h-10 w-10"}},Nl={root:{base:"w-full text-left text-sm text-gray-500 dark:text-gray-400",shadow:"absolute left-0 top-0 -z-10 h-full w-full rounded-lg bg-white drop-shadow-md dark:bg-black",wrapper:"relative"},body:{base:"group/body",cell:{base:"px-6 py-4 group-first/body:group-first/row:first:rounded-tl-lg group-first/body:group-first/row:last:rounded-tr-lg group-last/body:group-last/row:first:rounded-bl-lg group-last/body:group-last/row:last:rounded-br-lg"}},head:{base:"group/head text-xs uppercase text-gray-700 dark:text-gray-400",cell:{base:"bg-gray-50 px-6 py-3 group-first/head:first:rounded-tl-lg group-first/head:last:rounded-tr-lg dark:bg-gray-700"}},row:{base:"group/row",hovered:"hover:bg-gray-50 dark:hover:bg-gray-600",striped:"odd:bg-white even:bg-gray-50 odd:dark:bg-gray-800 even:dark:bg-gray-700"}},Rl={base:"flex flex-col gap-2",tablist:{base:"flex text-center",variant:{default:"flex-wrap border-b border-gray-200 dark:border-gray-700",underline:"-mb-px flex-wrap border-b border-gray-200 dark:border-gray-700",pills:"flex-wrap space-x-2 text-sm font-medium text-gray-500 dark:text-gray-400",fullWidth:"grid w-full grid-flow-col divide-x divide-gray-200 rounded-none text-sm font-medium shadow dark:divide-gray-700 dark:text-gray-400"},tabitem:{base:"flex items-center justify-center rounded-t-lg p-4 text-sm font-medium first:ml-0 focus:outline-none focus:ring-4 focus:ring-cyan-300 disabled:cursor-not-allowed disabled:text-gray-400 disabled:dark:text-gray-500",variant:{default:{base:"rounded-t-lg",active:{on:"bg-gray-100 text-cyan-600 dark:bg-gray-800 dark:text-cyan-500",off:"text-gray-500 hover:bg-gray-50 hover:text-gray-600 dark:text-gray-400 dark:hover:bg-gray-800 dark:hover:text-gray-300"}},underline:{base:"rounded-t-lg",active:{on:"active rounded-t-lg border-b-2 border-cyan-600 text-cyan-600 dark:border-cyan-500 dark:text-cyan-500",off:"border-b-2 border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-600 dark:text-gray-400 dark:hover:text-gray-300"}},pills:{base:"",active:{on:"rounded-lg bg-cyan-600 text-white",off:"rounded-lg hover:bg-gray-100 hover:text-gray-900 dark:hover:bg-gray-800 dark:hover:text-white"}},fullWidth:{base:"ml-0 flex w-full rounded-none first:ml-0",active:{on:"active rounded-none bg-gray-100 p-4 text-gray-900 dark:bg-gray-700 dark:text-white",off:"rounded-none bg-white hover:bg-gray-50 hover:text-gray-700 dark:bg-gray-800 dark:hover:bg-gray-700 dark:hover:text-white"}}},icon:"mr-2 h-5 w-5"}},tabitemcontainer:{base:"",variant:{default:"",underline:"",pills:"",fullWidth:""}},tabpanel:"py-3"},jl={base:"block w-full rounded-lg border text-sm disabled:cursor-not-allowed disabled:opacity-50",colors:{gray:"border-gray-300 bg-gray-50 text-gray-900 focus:border-cyan-500 focus:ring-cyan-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-cyan-500 dark:focus:ring-cyan-500",info:"border-cyan-500 bg-cyan-50 text-cyan-900 placeholder-cyan-700 focus:border-cyan-500 focus:ring-cyan-500 dark:border-cyan-400 dark:bg-cyan-100 dark:focus:border-cyan-500 dark:focus:ring-cyan-500",failure:"border-red-500 bg-red-50 text-red-900 placeholder-red-700 focus:border-red-500 focus:ring-red-500 dark:border-red-400 dark:bg-red-100 dark:focus:border-red-500 dark:focus:ring-red-500",warning:"border-yellow-500 bg-yellow-50 text-yellow-900 placeholder-yellow-700 focus:border-yellow-500 focus:ring-yellow-500 dark:border-yellow-400 dark:bg-yellow-100 dark:focus:border-yellow-500 dark:focus:ring-yellow-500",success:"border-green-500 bg-green-50 text-green-900 placeholder-green-700 focus:border-green-500 focus:ring-green-500 dark:border-green-400 dark:bg-green-100 dark:focus:border-green-500 dark:focus:ring-green-500"},withShadow:{on:"shadow-sm dark:shadow-sm-light",off:""}},El={base:"flex",addon:"inline-flex items-center rounded-l-md border border-r-0 border-gray-300 bg-gray-200 px-3 text-sm text-gray-900 dark:border-gray-600 dark:bg-gray-600 dark:text-gray-400",field:{base:"relative w-full",icon:{base:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",svg:"h-5 w-5 text-gray-500 dark:text-gray-400"},rightIcon:{base:"pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3",svg:"h-5 w-5 text-gray-500 dark:text-gray-400"},input:{base:"block w-full border disabled:cursor-not-allowed disabled:opacity-50",sizes:{sm:"p-2 sm:text-xs",md:"p-2.5 text-sm",lg:"p-4 sm:text-base"},colors:{gray:"border-gray-300 bg-gray-50 text-gray-900 focus:border-cyan-500 focus:ring-cyan-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-cyan-500 dark:focus:ring-cyan-500",info:"border-cyan-500 bg-cyan-50 text-cyan-900 placeholder-cyan-700 focus:border-cyan-500 focus:ring-cyan-500 dark:border-cyan-400 dark:bg-cyan-100 dark:focus:border-cyan-500 dark:focus:ring-cyan-500",failure:"border-red-500 bg-red-50 text-red-900 placeholder-red-700 focus:border-red-500 focus:ring-red-500 dark:border-red-400 dark:bg-red-100 dark:focus:border-red-500 dark:focus:ring-red-500",warning:"border-yellow-500 bg-yellow-50 text-yellow-900 placeholder-yellow-700 focus:border-yellow-500 focus:ring-yellow-500 dark:border-yellow-400 dark:bg-yellow-100 dark:focus:border-yellow-500 dark:focus:ring-yellow-500",success:"border-green-500 bg-green-50 text-green-900 placeholder-green-700 focus:border-green-500 focus:ring-green-500 dark:border-green-400 dark:bg-green-100 dark:focus:border-green-500 dark:focus:ring-green-500"},withRightIcon:{on:"pr-10",off:""},withIcon:{on:"pl-10",off:""},withAddon:{on:"rounded-r-lg",off:"rounded-lg"},withShadow:{on:"shadow-sm dark:shadow-sm-light",off:""}}}},Il={root:{direction:{horizontal:"sm:flex",vertical:"relative border-l border-gray-200 dark:border-gray-700"}},item:{root:{horizontal:"relative mb-6 sm:mb-0",vertical:"mb-10 ml-6"},content:{root:{base:"",horizontal:"mt-3 sm:pr-8",vertical:""},body:{base:"mb-4 text-base font-normal text-gray-500 dark:text-gray-400"},time:{base:"mb-1 text-sm font-normal leading-none text-gray-400 dark:text-gray-500"},title:{base:"text-lg font-semibold text-gray-900 dark:text-white"}},point:{horizontal:"flex items-center",line:"hidden h-0.5 w-full bg-gray-200 dark:bg-gray-700 sm:flex",marker:{base:{horizontal:"absolute -left-1.5 h-3 w-3 rounded-full border border-white bg-gray-200 dark:border-gray-900 dark:bg-gray-700",vertical:"absolute -left-1.5 mt-1.5 h-3 w-3 rounded-full border border-white bg-gray-200 dark:border-gray-900 dark:bg-gray-700"},icon:{base:"h-3 w-3 text-cyan-600 dark:text-cyan-300",wrapper:"absolute -left-3 flex h-6 w-6 items-center justify-center rounded-full bg-cyan-200 ring-8 ring-white dark:bg-cyan-900 dark:ring-gray-900"}},vertical:""}}},Sl={root:{base:"flex w-full max-w-xs items-center rounded-lg bg-white p-4 text-gray-500 shadow dark:bg-gray-800 dark:text-gray-400",closed:"opacity-0 ease-out"},toggle:{base:"-m-1.5 ml-auto inline-flex h-8 w-8 rounded-lg bg-white p-1.5 text-gray-400 hover:bg-gray-100 hover:text-gray-900 focus:ring-2 focus:ring-gray-300 dark:bg-gray-800 dark:text-gray-500 dark:hover:bg-gray-700 dark:hover:text-white",icon:"h-5 w-5 shrink-0"}},Ol={root:{base:"group flex rounded-lg focus:outline-none",active:{on:"cursor-pointer",off:"cursor-not-allowed opacity-50"},label:"ms-3 mt-0.5 text-start text-sm font-medium text-gray-900 dark:text-gray-300"},toggle:{base:"relative rounded-full border after:absolute after:rounded-full after:bg-white after:transition-all group-focus:ring-4 group-focus:ring-cyan-500/25",checked:{on:"after:translate-x-full after:border-white rtl:after:-translate-x-full",off:"border-gray-200 bg-gray-200 dark:border-gray-600 dark:bg-gray-700",color:{blue:"border-cyan-700 bg-cyan-700",dark:"bg-dark-700 border-dark-900",failure:"border-red-900 bg-red-700",gray:"border-gray-600 bg-gray-500",green:"border-green-700 bg-green-600",light:"bg-light-700 border-light-900",red:"border-red-900 bg-red-700",purple:"border-purple-900 bg-purple-700",success:"border-green-500 bg-green-500",yellow:"border-yellow-400 bg-yellow-400",warning:"border-yellow-600 bg-yellow-600",cyan:"border-cyan-500 bg-cyan-500",lime:"border-lime-400 bg-lime-400",indigo:"border-indigo-400 bg-indigo-400",teal:"bg-gradient-to-r from-teal-400 via-teal-500 to-teal-600 hover:bg-gradient-to-br focus:ring-4",info:"border-cyan-600 bg-cyan-600",pink:"border-pink-600 bg-pink-600"}},sizes:{sm:"h-5 w-9 min-w-9 after:left-px after:top-px after:h-4 after:w-4 rtl:after:right-px",md:"h-6 w-11 min-w-11 after:left-px after:top-px after:h-5 after:w-5 rtl:after:right-px",lg:"h-7 w-14 min-w-14 after:left-1 after:top-0.5 after:h-6 after:w-6 rtl:after:right-1"}}},Ml={target:"w-fit",animation:"transition-opacity",arrow:{base:"absolute z-10 h-2 w-2 rotate-45",style:{dark:"bg-gray-900 dark:bg-gray-700",light:"bg-white",auto:"bg-white dark:bg-gray-700"},placement:"-4px"},base:"absolute z-10 inline-block rounded-lg px-3 py-2 text-sm font-medium shadow-sm",hidden:"invisible opacity-0",style:{dark:"bg-gray-900 text-white dark:bg-gray-700",light:"border border-gray-200 bg-white text-gray-900",auto:"border border-gray-200 bg-white text-gray-900 dark:border-none dark:bg-gray-700 dark:text-white"},content:"relative z-20"},Pl={accordion:Wi,alert:Vi,avatar:Gi,badge:Yi,blockquote:Ki,breadcrumb:qi,button:Ui,buttonGroup:Xi,card:Ji,carousel:Zi,checkbox:Qi,clipboard:el,datepicker:rl,darkThemeToggle:tl,drawer:nl,dropdown:_e,fileInput:ol,floatingLabel:al,footer:sl,helperText:il,hr:ll,kbd:cl,label:dl,listGroup:fl,list:ul,megaMenu:gl,modal:bl,navbar:zt,pagination:pl,popover:ml,progress:hl,radio:yl,rangeSlider:xl,rating:wl,ratingAdvanced:vl,select:kl,textInput:El,textarea:jl,toggleSwitch:Ol,sidebar:Cl,spinner:Tl,table:Nl,tabs:Rl,timeline:Il,toast:Sl,tooltip:Ml},Dl={theme:wt(Pl)};function z(){return wt(Dl.theme)}const oo=f.createContext(void 0);function ao(){const e=f.useContext(oo);if(!e)throw new Error("useAccordionContext should be used within the AccordionPanelContext provider!");return e}const so=({children:e,className:t,theme:r={},...n})=>{const{isOpen:o}=ao(),a=j(z().accordion.content,r);return d.jsx("div",{className:x(a.base,t),"data-testid":"flowbite-accordion-content",hidden:!o,...n,children:e})},io=({children:e,...t})=>{const{alwaysOpen:r}=t,[n,o]=f.useState(t.isOpen),a=r?{...t,isOpen:n,setOpen:()=>o(!n)}:t;return d.jsx(oo.Provider,{value:a,children:e})},lo=({as:e="h2",children:t,className:r,theme:n={},...o})=>{const{arrowIcon:a,flush:s,isOpen:i,setOpen:c}=ao(),l=()=>typeof c<"u"&&c(),b=j(z().accordion.title,n);return d.jsxs("button",{className:x(b.base,b.flush[s?"on":"off"],b.open[i?"on":"off"],r),onClick:l,type:"button",...o,children:[d.jsx(e,{className:b.heading,"data-testid":"flowbite-accordion-heading",children:t}),a&&d.jsx(a,{"aria-hidden":!0,className:x(b.arrow.base,b.arrow.open[i?"on":"off"]),"data-testid":"flowbite-accordion-arrow"})]})},co=({alwaysOpen:e=!1,arrowIcon:t=Xn,children:r,flush:n=!1,collapseAll:o=!1,className:a,theme:s={},...i})=>{const[c,l]=f.useState(o?-1:0),b=f.useMemo(()=>f.Children.map(r,(m,g)=>f.cloneElement(m,{alwaysOpen:e,arrowIcon:t,flush:n,isOpen:c===g,setOpen:()=>l(c===g?-1:g)})),[e,t,r,n,c]),u=j(z().accordion.root,s);return d.jsx("div",{className:x(u.base,u.flush[n?"on":"off"],a),"data-testid":"flowbite-accordion",...i,children:b})};co.displayName="Accordion";io.displayName="Accordion.Panel";lo.displayName="Accordion.Title";so.displayName="Accordion.Content";Object.assign(co,{Panel:io,Title:lo,Content:so});const uo=({children:e,className:t,theme:r={},...n})=>{const o=j(z().avatar.group,r);return d.jsx("div",{"data-testid":"avatar-group-element",className:x(o.base,t),...n,children:e})};uo.displayName="Avatar.Group";const fo=({className:e,href:t,theme:r={},total:n,...o})=>{const a=j(z().avatar.groupCounter,r);return d.jsxs("a",{href:t,className:x(a.base,e),...o,children:["+",n]})};fo.displayName="Avatar.GroupCounter";const go=({alt:e="",bordered:t=!1,children:r,className:n,color:o="light",img:a,placeholderInitials:s="",rounded:i=!1,size:c="md",stacked:l=!1,status:b,statusPosition:u="top-left",theme:m={},...g})=>{const p=j(z().avatar,m),h=x(p.root.img.base,t&&p.root.bordered,t&&p.root.color[o],i&&p.root.rounded,l&&p.root.stacked,p.root.img.on,p.root.size[c]),w={className:x(h,p.root.img.on),"data-testid":"flowbite-avatar-img"};return d.jsxs("div",{className:x(p.root.base,n),"data-testid":"flowbite-avatar",...g,children:[d.jsxs("div",{className:"relative",children:[a?typeof a=="string"?d.jsx("img",{alt:e,src:a,...w}):a({alt:e,...w}):s?d.jsx("div",{className:x(p.root.img.off,p.root.initials.base,l&&p.root.stacked,t&&p.root.bordered,t&&p.root.color[o],p.root.size[c],i&&p.root.rounded),"data-testid":"flowbite-avatar-initials-placeholder",children:d.jsx("span",{className:x(p.root.initials.text),"data-testid":"flowbite-avatar-initials-placeholder-text",children:s})}):d.jsx("div",{className:x(h,p.root.img.off),"data-testid":"flowbite-avatar-img",children:d.jsx("svg",{className:p.root.img.placeholder,fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:d.jsx("path",{fillRule:"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z",clipRule:"evenodd"})})}),b&&d.jsx("span",{"data-testid":"flowbite-avatar-status",className:x(p.root.status.base,p.root.status[b],p.root.statusPosition[u])})]}),r&&d.jsx("div",{children:r})]})};go.displayName="Avatar";Object.assign(go,{Group:uo,Counter:fo});const bo=({children:e,color:t="info",href:r,icon:n,size:o="xs",className:a,theme:s={},...i})=>{const c=j(z().badge,s),l=()=>d.jsxs("span",{className:x(c.root.base,c.root.color[t],c.root.size[o],c.icon[n?"on":"off"],a),"data-testid":"flowbite-badge",...i,children:[n&&d.jsx(n,{"aria-hidden":!0,className:c.icon.size[o],"data-testid":"flowbite-badge-icon"}),e&&d.jsx("span",{children:e})]});return r?d.jsx("a",{className:c.root.href,href:r,children:d.jsx(l,{})}):d.jsx(l,{})};bo.displayName="Badge";const po=({className:e,color:t="info",light:r,size:n="md",theme:o={},...a})=>{const s=j(z().spinner,o);return d.jsx("span",{role:"status",...a,children:d.jsxs("svg",{fill:"none",viewBox:"0 0 100 101",className:x(s.base,s.color[t],s.light[r?"on":"off"].base,s.light[r?"on":"off"].color[t],s.size[n],e),children:[d.jsx("path",{d:"M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z",fill:"currentColor"}),d.jsx("path",{d:"M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z",fill:"currentFill"})]})})};po.displayName="Spinner";const Ir=f.forwardRef(({children:e,as:t,href:r,type:n="button",...o},a)=>{const s=t||(r?"a":"button");return f.createElement(s,{ref:a,href:r,type:n,...o},e)});Ir.displayName="ButtonBaseComponent";const mo=(e,t,r)=>f.Children.map(e,(n,o)=>{if(f.isValidElement(n)){const a=n.type==Sr?{positionInGroup:Ll(o,f.Children.count(e))}:{};return n.props.children?f.cloneElement(n,{...n.props,children:mo(n.props.children,t,r),...a}):f.cloneElement(n,{outline:t,pill:r,...a})}return n}),Ll=(e,t)=>e===0?"start":e===t-1?"end":"middle",ho=({children:e,className:t,outline:r,pill:n,theme:o={},...a})=>{const s=f.useMemo(()=>mo(e,r,n),[e,r,n]),i=j(z().buttonGroup,o);return d.jsx("div",{className:x(i.base,t),role:"group",...a,children:s})};ho.displayName="Button.Group";const yo=f.forwardRef(({children:e,className:t,color:r="info",disabled:n,fullSized:o,isProcessing:a=!1,processingLabel:s="Loading...",processingSpinner:i,gradientDuoTone:c,gradientMonochrome:l,label:b,outline:u=!1,pill:m=!1,positionInGroup:g="none",size:p="md",theme:h={},...w},v)=>{const{buttonGroup:C,button:T}=z(),y=j(T,h),I=w;return d.jsx(Ir,{ref:v,disabled:n,className:x(y.base,n&&y.disabled,!c&&!l&&y.color[r],c&&!l&&y.gradientDuoTone[c],!c&&l&&y.gradient[l],u&&(y.outline.color[r]??y.outline.color.default),y.pill[m?"on":"off"],o&&y.fullSized,C.position[g],t),...I,children:d.jsx("span",{className:x(y.inner.base,y.outline[u?"on":"off"],y.outline.pill[u&&m?"on":"off"],y.size[p],u&&!y.outline.color[r]&&y.inner.outline,a&&y.isProcessing,a&&y.inner.isProcessingPadding[p],y.inner.position[g]),children:d.jsxs(d.Fragment,{children:[a&&d.jsx("span",{className:x(y.spinnerSlot,y.spinnerLeftPosition[p]),children:i||d.jsx(po,{size:p})}),typeof e<"u"?e:d.jsx("span",{"data-testid":"flowbite-button-label",className:x(y.label),children:a?s:b})]})})})});yo.displayName="Button";const Sr=Object.assign(yo,{Group:ho}),xo=({children:e,...t})=>{const r=n=>{const a=n.target.closest('[role="banner"]');a==null||a.remove()};return d.jsx(Sr,{onClick:r,...t,children:e})};xo.displayName="Banner.CollapseButton";const wo=({children:e,...t})=>d.jsx("div",{"data-testid":"flowbite-banner",role:"banner",tabIndex:-1,...t,children:e});wo.displayName="Banner";Object.assign(wo,{CollapseButton:xo});const vo=f.forwardRef(({children:e,className:t,href:r,icon:n,theme:o={},...a},s)=>{const i=typeof r<"u",c=i?"a":"span",l=j(z().breadcrumb.item,o);return d.jsxs("li",{className:x(l.base,t),...a,children:[d.jsx(Zn,{"aria-hidden":!0,className:l.chevron,"data-testid":"flowbite-breadcrumb-separator"}),d.jsxs(c,{ref:s,className:l.href[i?"on":"off"],"data-testid":"flowbite-breadcrumb-item",href:r,children:[n&&d.jsx(n,{"aria-hidden":!0,className:l.icon}),e]})]})});vo.displayName="Breadcrumb.Item";const ko=({children:e,className:t,theme:r={},...n})=>{const o=j(z().breadcrumb.root,r);return d.jsx("nav",{"aria-label":"Breadcrumb",className:x(o.base,t),...n,children:d.jsx("ol",{className:o.list,children:e})})};ko.displayName="Breadcrumb";Object.assign(ko,{Item:vo});var dr={exports:{}};/*!
	Copyright (c) 2018 Jed Watson.
	Licensed under the MIT License (MIT), see
	http://jedwatson.github.io/classnames
*/var mn;function Al(){return mn||(mn=1,function(e){(function(){var t={}.hasOwnProperty;function r(){for(var a="",s=0;s<arguments.length;s++){var i=arguments[s];i&&(a=o(a,n(i)))}return a}function n(a){if(typeof a=="string"||typeof a=="number")return a;if(typeof a!="object")return"";if(Array.isArray(a))return r.apply(null,a);if(a.toString!==Object.prototype.toString&&!a.toString.toString().includes("[native code]"))return a.toString();var s="";for(var i in a)t.call(a,i)&&a[i]&&(s=o(s,i));return s}function o(a,s){return s?a?a+" "+s:a+s:a}e.exports?(r.default=r,e.exports=r):window.classNames=r})()}(dr)),dr.exports}var Fl=Al();const zl=Vn(Fl);var Pt={exports:{}},hn;function Bl(){if(hn)return Pt.exports;hn=1;function e(t,r=100,n={}){if(typeof t!="function")throw new TypeError(`Expected the first parameter to be a function, got \`${typeof t}\`.`);if(r<0)throw new RangeError("`wait` must not be negative.");const{immediate:o}=typeof n=="boolean"?{immediate:n}:n;let a,s,i,c,l;function b(){const g=a,p=s;return a=void 0,s=void 0,l=t.apply(g,p),l}function u(){const g=Date.now()-c;g<r&&g>=0?i=setTimeout(u,r-g):(i=void 0,o||(l=b()))}const m=function(...g){if(a&&this!==a)throw new Error("Debounced method called with different contexts.");a=this,s=g,c=Date.now();const p=o&&!i;return i||(i=setTimeout(u,r)),p&&(l=b()),l};return m.clear=()=>{i&&(clearTimeout(i),i=void 0)},m.flush=()=>{i&&m.trigger()},m.trigger=()=>{l=b(),m.clear()},m}return Pt.exports.debounce=e,Pt.exports=e,Pt.exports}var _l=Bl();const Hl=Vn(_l);var $l=Object.defineProperty,Wl=(e,t,r)=>t in e?$l(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,ue=(e,t,r)=>Wl(e,typeof t!="symbol"?t+"":t,r);const Vl=300,Gl=0;class Yl extends f.PureComponent{constructor(t){super(t),ue(this,"container"),ue(this,"scrolling"),ue(this,"started"),ue(this,"pressed"),ue(this,"isMobile",!1),ue(this,"internal"),ue(this,"scrollLeft"),ue(this,"scrollTop"),ue(this,"clientX"),ue(this,"clientY"),ue(this,"onEndScroll",()=>{this.scrolling=!1,!this.pressed&&this.started&&this.processEnd()}),ue(this,"onScroll",()=>{const r=this.container.current;(r.scrollLeft!==this.scrollLeft||r.scrollTop!==this.scrollTop)&&(this.scrolling=!0,this.processScroll(),this.onEndScroll())}),ue(this,"onTouchStart",r=>{const{nativeMobileScroll:n}=this.props;if(this.isDraggable(r.target))if(this.internal=!0,n&&this.scrolling)this.pressed=!0;else{const o=r.touches[0];this.processClick(o.clientX,o.clientY),!n&&this.props.stopPropagation&&r.stopPropagation()}}),ue(this,"onTouchEnd",()=>{const{nativeMobileScroll:r}=this.props;this.pressed&&(this.started&&(!this.scrolling||!r)?this.processEnd():this.pressed=!1,this.forceUpdate())}),ue(this,"onTouchMove",r=>{const{nativeMobileScroll:n}=this.props;if(this.pressed&&(!n||!this.isMobile)){const o=r.touches[0];o&&this.processMove(o.clientX,o.clientY),r.preventDefault(),this.props.stopPropagation&&r.stopPropagation()}}),ue(this,"onMouseDown",r=>{var n,o;this.isDraggable(r.target)&&this.isScrollable()&&(this.internal=!0,((o=(n=this.props)==null?void 0:n.buttons)==null?void 0:o.indexOf(r.button))!==-1&&(this.processClick(r.clientX,r.clientY),r.preventDefault(),this.props.stopPropagation&&r.stopPropagation()))}),ue(this,"onMouseMove",r=>{this.pressed&&(this.processMove(r.clientX,r.clientY),r.preventDefault(),this.props.stopPropagation&&r.stopPropagation())}),ue(this,"onMouseUp",r=>{this.pressed&&(this.started?this.processEnd():(this.internal=!1,this.pressed=!1,this.forceUpdate(),this.props.onClick&&this.props.onClick(r)),r.preventDefault(),this.props.stopPropagation&&r.stopPropagation())}),this.container=ye.createRef(),this.onEndScroll=Hl(this.onEndScroll,Vl),this.scrolling=!1,this.started=!1,this.pressed=!1,this.internal=!1,this.getRef=this.getRef.bind(this)}componentDidMount(){const{nativeMobileScroll:t}=this.props,r=this.container.current;window.addEventListener("mouseup",this.onMouseUp),window.addEventListener("mousemove",this.onMouseMove),window.addEventListener("touchmove",this.onTouchMove,{passive:!1}),window.addEventListener("touchend",this.onTouchEnd),r.addEventListener("touchstart",this.onTouchStart,{passive:!1}),r.addEventListener("mousedown",this.onMouseDown,{passive:!1}),t&&(this.isMobile=this.isMobileDevice(),this.isMobile&&this.forceUpdate())}componentWillUnmount(){window.removeEventListener("mouseup",this.onMouseUp),window.removeEventListener("mousemove",this.onMouseMove),window.removeEventListener("touchmove",this.onTouchMove),window.removeEventListener("touchend",this.onTouchEnd)}getElement(){return this.container.current}isMobileDevice(){return typeof window.orientation<"u"||navigator.userAgent.indexOf("IEMobile")!==-1}isDraggable(t){const r=this.props.ignoreElements;if(r){const n=t.closest(r);return n===null||n.contains(this.getElement())}else return!0}isScrollable(){const t=this.container.current;return t&&(t.scrollWidth>t.clientWidth||t.scrollHeight>t.clientHeight)}processClick(t,r){const n=this.container.current;this.scrollLeft=n==null?void 0:n.scrollLeft,this.scrollTop=n==null?void 0:n.scrollTop,this.clientX=t,this.clientY=r,this.pressed=!0}processStart(t=!0){const{onStartScroll:r}=this.props;this.started=!0,t&&document.body.classList.add("cursor-grab"),r&&r({external:!this.internal}),this.forceUpdate()}processScroll(){if(this.started){const{onScroll:t}=this.props;t&&t({external:!this.internal})}else this.processStart(!1)}processMove(t,r){const{horizontal:n,vertical:o,activationDistance:a,onScroll:s}=this.props,i=this.container.current;this.started?(n&&(i.scrollLeft-=t-this.clientX),o&&(i.scrollTop-=r-this.clientY),s&&s({external:!this.internal}),this.clientX=t,this.clientY=r,this.scrollLeft=i.scrollLeft,this.scrollTop=i.scrollTop):(n&&Math.abs(t-this.clientX)>a||o&&Math.abs(r-this.clientY)>a)&&(this.clientX=t,this.clientY=r,this.processStart())}processEnd(){const{onEndScroll:t}=this.props;this.container.current&&t&&t({external:!this.internal}),this.pressed=!1,this.started=!1,this.scrolling=!1,this.internal=!1,document.body.classList.remove("cursor-grab"),this.forceUpdate()}getRef(t){[this.container,this.props.innerRef].forEach(r=>{r&&(typeof r=="function"?r(t):r.current=t)})}render(){const{children:t,draggingClassName:r,className:n,style:o,hideScrollbars:a}=this.props;return d.jsx("div",{className:zl(n,this.pressed&&r,{"!scroll-auto [&>*]:pointer-events-none [&>*]:cursor-grab":this.pressed,"overflow-auto":this.isMobile,"overflow-hidden !overflow-x-hidden [overflow:-moz-scrollbars-none] [scrollbar-width:none]":a,"[&::-webkit-scrollbar]:[-webkit-appearance:none !important] [&::-webkit-scrollbar]:!hidden [&::-webkit-scrollbar]:!h-0 [&::-webkit-scrollbar]:!w-0 [&::-webkit-scrollbar]:!bg-transparent":a}),style:o,ref:this.getRef,onScroll:this.onScroll,children:t})}}ue(Yl,"defaultProps",{nativeMobileScroll:!0,hideScrollbars:!0,activationDistance:10,vertical:!0,horizontal:!0,stopPropagation:!1,style:{},buttons:[Gl]});const Kl=f.forwardRef(({className:e,color:t="default",theme:r={},...n},o)=>{const a=j(z().checkbox,r);return d.jsx("input",{ref:o,type:"checkbox",className:x(a.root.base,a.root.color[t],e),...n})});Kl.displayName="Checkbox";function er(){return typeof window<"u"}function Ge(e){return Co(e)?(e.nodeName||"").toLowerCase():"#document"}function we(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function Se(e){var t;return(t=(Co(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function Co(e){return er()?e instanceof Node||e instanceof we(e).Node:!1}function ee(e){return er()?e instanceof Element||e instanceof we(e).Element:!1}function de(e){return er()?e instanceof HTMLElement||e instanceof we(e).HTMLElement:!1}function kr(e){return!er()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof we(e).ShadowRoot}function Nt(e){const{overflow:t,overflowX:r,overflowY:n,display:o}=ke(e);return/auto|scroll|overlay|hidden|clip/.test(t+n+r)&&!["inline","contents"].includes(o)}function ql(e){return["table","td","th"].includes(Ge(e))}function tr(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch{return!1}})}function Or(e){const t=Mr(),r=ee(e)?ke(e):e;return["transform","translate","scale","rotate","perspective"].some(n=>r[n]?r[n]!=="none":!1)||(r.containerType?r.containerType!=="normal":!1)||!t&&(r.backdropFilter?r.backdropFilter!=="none":!1)||!t&&(r.filter?r.filter!=="none":!1)||["transform","translate","scale","rotate","perspective","filter"].some(n=>(r.willChange||"").includes(n))||["paint","layout","strict","content"].some(n=>(r.contain||"").includes(n))}function Ul(e){let t=De(e);for(;de(t)&&!We(t);){if(Or(t))return t;if(tr(t))return null;t=De(t)}return null}function Mr(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function We(e){return["html","body","#document"].includes(Ge(e))}function ke(e){return we(e).getComputedStyle(e)}function rr(e){return ee(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function De(e){if(Ge(e)==="html")return e;const t=e.assignedSlot||e.parentNode||kr(e)&&e.host||Se(e);return kr(t)?t.host:t}function To(e){const t=De(e);return We(t)?e.ownerDocument?e.ownerDocument.body:e.body:de(t)&&Nt(t)?t:To(t)}function $e(e,t,r){var n;t===void 0&&(t=[]),r===void 0&&(r=!0);const o=To(e),a=o===((n=e.ownerDocument)==null?void 0:n.body),s=we(o);if(a){const i=Cr(s);return t.concat(s,s.visualViewport||[],Nt(o)?o:[],i&&r?$e(i):[])}return t.concat(o,$e(o,[],r))}function Cr(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function Ne(e){let t=e.activeElement;for(;((r=t)==null||(r=r.shadowRoot)==null?void 0:r.activeElement)!=null;){var r;t=t.shadowRoot.activeElement}return t}function ce(e,t){if(!e||!t)return!1;const r=t.getRootNode==null?void 0:t.getRootNode();if(e.contains(t))return!0;if(r&&kr(r)){let n=t;for(;n;){if(e===n)return!0;n=n.parentNode||n.host}}return!1}function Pr(){const e=navigator.userAgentData;return e!=null&&e.platform?e.platform:navigator.platform}function No(){const e=navigator.userAgentData;return e&&Array.isArray(e.brands)?e.brands.map(t=>{let{brand:r,version:n}=t;return r+"/"+n}).join(" "):navigator.userAgent}function Ro(e){return e.mozInputSource===0&&e.isTrusted?!0:Tr()&&e.pointerType?e.type==="click"&&e.buttons===1:e.detail===0&&!e.pointerType}function Dr(e){return Xl()?!1:!Tr()&&e.width===0&&e.height===0||Tr()&&e.width===1&&e.height===1&&e.pressure===0&&e.detail===0&&e.pointerType==="mouse"||e.width<1&&e.height<1&&e.pressure===0&&e.detail===0&&e.pointerType==="touch"}function Lr(){return/apple/i.test(navigator.vendor)}function Tr(){const e=/android/i;return e.test(Pr())||e.test(No())}function jo(){return Pr().toLowerCase().startsWith("mac")&&!navigator.maxTouchPoints}function Xl(){return No().includes("jsdom/")}function vt(e,t){const r=["mouse","pen"];return t||r.push("",void 0),r.includes(e)}function Jl(e){return"nativeEvent"in e}function Zl(e){return e.matches("html,body")}function ge(e){return(e==null?void 0:e.ownerDocument)||document}function ur(e,t){if(t==null)return!1;if("composedPath"in e)return e.composedPath().includes(t);const r=e;return r.target!=null&&t.contains(r.target)}function Pe(e){return"composedPath"in e?e.composedPath()[0]:e.target}const Ql="input:not([type='hidden']):not([disabled]),[contenteditable]:not([contenteditable='false']),textarea:not([disabled])";function Ar(e){return de(e)&&e.matches(Ql)}function be(e){e.preventDefault(),e.stopPropagation()}function Eo(e){return e?e.getAttribute("role")==="combobox"&&Ar(e):!1}const ec=["top","right","bottom","left"],yn=["start","end"],xn=ec.reduce((e,t)=>e.concat(t,t+"-"+yn[0],t+"-"+yn[1]),[]),at=Math.min,Ue=Math.max,Gt=Math.round,et=Math.floor,Ee=e=>({x:e,y:e}),tc={left:"right",right:"left",bottom:"top",top:"bottom"},rc={start:"end",end:"start"};function Nr(e,t,r){return Ue(e,at(t,r))}function ft(e,t){return typeof e=="function"?e(t):e}function Le(e){return e.split("-")[0]}function Ie(e){return e.split("-")[1]}function Io(e){return e==="x"?"y":"x"}function Fr(e){return e==="y"?"height":"width"}function st(e){return["top","bottom"].includes(Le(e))?"y":"x"}function zr(e){return Io(st(e))}function So(e,t,r){r===void 0&&(r=!1);const n=Ie(e),o=zr(e),a=Fr(o);let s=o==="x"?n===(r?"end":"start")?"right":"left":n==="start"?"bottom":"top";return t.reference[a]>t.floating[a]&&(s=Kt(s)),[s,Kt(s)]}function nc(e){const t=Kt(e);return[Yt(e),t,Yt(t)]}function Yt(e){return e.replace(/start|end/g,t=>rc[t])}function oc(e,t,r){const n=["left","right"],o=["right","left"],a=["top","bottom"],s=["bottom","top"];switch(e){case"top":case"bottom":return r?t?o:n:t?n:o;case"left":case"right":return t?a:s;default:return[]}}function ac(e,t,r,n){const o=Ie(e);let a=oc(Le(e),r==="start",n);return o&&(a=a.map(s=>s+"-"+o),t&&(a=a.concat(a.map(Yt)))),a}function Kt(e){return e.replace(/left|right|bottom|top/g,t=>tc[t])}function sc(e){return{top:0,right:0,bottom:0,left:0,...e}}function Oo(e){return typeof e!="number"?sc(e):{top:e,right:e,bottom:e,left:e}}function qt(e){const{x:t,y:r,width:n,height:o}=e;return{width:n,height:o,top:r,left:t,right:t+n,bottom:r+o,x:t,y:r}}/*!
* tabbable 6.2.0
* @license MIT, https://github.com/focus-trap/tabbable/blob/master/LICENSE
*/var ic=["input:not([inert])","select:not([inert])","textarea:not([inert])","a[href]:not([inert])","button:not([inert])","[tabindex]:not(slot):not([inert])","audio[controls]:not([inert])","video[controls]:not([inert])",'[contenteditable]:not([contenteditable="false"]):not([inert])',"details>summary:first-of-type:not([inert])","details:not([inert])"],Ut=ic.join(","),Mo=typeof Element>"u",it=Mo?function(){}:Element.prototype.matches||Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector,Xt=!Mo&&Element.prototype.getRootNode?function(e){var t;return e==null||(t=e.getRootNode)===null||t===void 0?void 0:t.call(e)}:function(e){return e==null?void 0:e.ownerDocument},Jt=function e(t,r){var n;r===void 0&&(r=!0);var o=t==null||(n=t.getAttribute)===null||n===void 0?void 0:n.call(t,"inert"),a=o===""||o==="true",s=a||r&&t&&e(t.parentNode);return s},lc=function(t){var r,n=t==null||(r=t.getAttribute)===null||r===void 0?void 0:r.call(t,"contenteditable");return n===""||n==="true"},cc=function(t,r,n){if(Jt(t))return[];var o=Array.prototype.slice.apply(t.querySelectorAll(Ut));return r&&it.call(t,Ut)&&o.unshift(t),o=o.filter(n),o},dc=function e(t,r,n){for(var o=[],a=Array.from(t);a.length;){var s=a.shift();if(!Jt(s,!1))if(s.tagName==="SLOT"){var i=s.assignedElements(),c=i.length?i:s.children,l=e(c,!0,n);n.flatten?o.push.apply(o,l):o.push({scopeParent:s,candidates:l})}else{var b=it.call(s,Ut);b&&n.filter(s)&&(r||!t.includes(s))&&o.push(s);var u=s.shadowRoot||typeof n.getShadowRoot=="function"&&n.getShadowRoot(s),m=!Jt(u,!1)&&(!n.shadowRootFilter||n.shadowRootFilter(s));if(u&&m){var g=e(u===!0?s.children:u.children,!0,n);n.flatten?o.push.apply(o,g):o.push({scopeParent:s,candidates:g})}else a.unshift.apply(a,s.children)}}return o},Po=function(t){return!isNaN(parseInt(t.getAttribute("tabindex"),10))},Do=function(t){if(!t)throw new Error("No node provided");return t.tabIndex<0&&(/^(AUDIO|VIDEO|DETAILS)$/.test(t.tagName)||lc(t))&&!Po(t)?0:t.tabIndex},uc=function(t,r){var n=Do(t);return n<0&&r&&!Po(t)?0:n},fc=function(t,r){return t.tabIndex===r.tabIndex?t.documentOrder-r.documentOrder:t.tabIndex-r.tabIndex},Lo=function(t){return t.tagName==="INPUT"},gc=function(t){return Lo(t)&&t.type==="hidden"},bc=function(t){var r=t.tagName==="DETAILS"&&Array.prototype.slice.apply(t.children).some(function(n){return n.tagName==="SUMMARY"});return r},pc=function(t,r){for(var n=0;n<t.length;n++)if(t[n].checked&&t[n].form===r)return t[n]},mc=function(t){if(!t.name)return!0;var r=t.form||Xt(t),n=function(i){return r.querySelectorAll('input[type="radio"][name="'+i+'"]')},o;if(typeof window<"u"&&typeof window.CSS<"u"&&typeof window.CSS.escape=="function")o=n(window.CSS.escape(t.name));else try{o=n(t.name)}catch(s){return console.error("Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s",s.message),!1}var a=pc(o,t.form);return!a||a===t},hc=function(t){return Lo(t)&&t.type==="radio"},yc=function(t){return hc(t)&&!mc(t)},xc=function(t){var r,n=t&&Xt(t),o=(r=n)===null||r===void 0?void 0:r.host,a=!1;if(n&&n!==t){var s,i,c;for(a=!!((s=o)!==null&&s!==void 0&&(i=s.ownerDocument)!==null&&i!==void 0&&i.contains(o)||t!=null&&(c=t.ownerDocument)!==null&&c!==void 0&&c.contains(t));!a&&o;){var l,b,u;n=Xt(o),o=(l=n)===null||l===void 0?void 0:l.host,a=!!((b=o)!==null&&b!==void 0&&(u=b.ownerDocument)!==null&&u!==void 0&&u.contains(o))}}return a},wn=function(t){var r=t.getBoundingClientRect(),n=r.width,o=r.height;return n===0&&o===0},wc=function(t,r){var n=r.displayCheck,o=r.getShadowRoot;if(getComputedStyle(t).visibility==="hidden")return!0;var a=it.call(t,"details>summary:first-of-type"),s=a?t.parentElement:t;if(it.call(s,"details:not([open]) *"))return!0;if(!n||n==="full"||n==="legacy-full"){if(typeof o=="function"){for(var i=t;t;){var c=t.parentElement,l=Xt(t);if(c&&!c.shadowRoot&&o(c)===!0)return wn(t);t.assignedSlot?t=t.assignedSlot:!c&&l!==t.ownerDocument?t=l.host:t=c}t=i}if(xc(t))return!t.getClientRects().length;if(n!=="legacy-full")return!0}else if(n==="non-zero-area")return wn(t);return!1},vc=function(t){if(/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(t.tagName))for(var r=t.parentElement;r;){if(r.tagName==="FIELDSET"&&r.disabled){for(var n=0;n<r.children.length;n++){var o=r.children.item(n);if(o.tagName==="LEGEND")return it.call(r,"fieldset[disabled] *")?!0:!o.contains(t)}return!0}r=r.parentElement}return!1},kc=function(t,r){return!(r.disabled||Jt(r)||gc(r)||wc(r,t)||bc(r)||vc(r))},Rr=function(t,r){return!(yc(r)||Do(r)<0||!kc(t,r))},Cc=function(t){var r=parseInt(t.getAttribute("tabindex"),10);return!!(isNaN(r)||r>=0)},Tc=function e(t){var r=[],n=[];return t.forEach(function(o,a){var s=!!o.scopeParent,i=s?o.scopeParent:o,c=uc(i,s),l=s?e(o.candidates):i;c===0?s?r.push.apply(r,l):r.push(i):n.push({documentOrder:a,tabIndex:c,item:o,isScope:s,content:l})}),n.sort(fc).reduce(function(o,a){return a.isScope?o.push.apply(o,a.content):o.push(a.content),o},[]).concat(r)},kt=function(t,r){r=r||{};var n;return r.getShadowRoot?n=dc([t],r.includeContainer,{filter:Rr.bind(null,r),flatten:!1,getShadowRoot:r.getShadowRoot,shadowRootFilter:Cc}):n=cc(t,r.includeContainer,Rr.bind(null,r)),Tc(n)},Nc=function(t,r){if(r=r||{},!t)throw new Error("No node provided");return it.call(t,Ut)===!1?!1:Rr(r,t)};function vn(e,t,r){let{reference:n,floating:o}=e;const a=st(t),s=zr(t),i=Fr(s),c=Le(t),l=a==="y",b=n.x+n.width/2-o.width/2,u=n.y+n.height/2-o.height/2,m=n[i]/2-o[i]/2;let g;switch(c){case"top":g={x:b,y:n.y-o.height};break;case"bottom":g={x:b,y:n.y+n.height};break;case"right":g={x:n.x+n.width,y:u};break;case"left":g={x:n.x-o.width,y:u};break;default:g={x:n.x,y:n.y}}switch(Ie(t)){case"start":g[s]-=m*(r&&l?-1:1);break;case"end":g[s]+=m*(r&&l?-1:1);break}return g}const Rc=async(e,t,r)=>{const{placement:n="bottom",strategy:o="absolute",middleware:a=[],platform:s}=r,i=a.filter(Boolean),c=await(s.isRTL==null?void 0:s.isRTL(t));let l=await s.getElementRects({reference:e,floating:t,strategy:o}),{x:b,y:u}=vn(l,n,c),m=n,g={},p=0;for(let h=0;h<i.length;h++){const{name:w,fn:v}=i[h],{x:C,y:T,data:y,reset:I}=await v({x:b,y:u,initialPlacement:n,placement:m,strategy:o,middlewareData:g,rects:l,platform:s,elements:{reference:e,floating:t}});b=C??b,u=T??u,g={...g,[w]:{...g[w],...y}},I&&p<=50&&(p++,typeof I=="object"&&(I.placement&&(m=I.placement),I.rects&&(l=I.rects===!0?await s.getElementRects({reference:e,floating:t,strategy:o}):I.rects),{x:b,y:u}=vn(l,m,c)),h=-1)}return{x:b,y:u,placement:m,strategy:o,middlewareData:g}};async function Br(e,t){var r;t===void 0&&(t={});const{x:n,y:o,platform:a,rects:s,elements:i,strategy:c}=e,{boundary:l="clippingAncestors",rootBoundary:b="viewport",elementContext:u="floating",altBoundary:m=!1,padding:g=0}=ft(t,e),p=Oo(g),w=i[m?u==="floating"?"reference":"floating":u],v=qt(await a.getClippingRect({element:(r=await(a.isElement==null?void 0:a.isElement(w)))==null||r?w:w.contextElement||await(a.getDocumentElement==null?void 0:a.getDocumentElement(i.floating)),boundary:l,rootBoundary:b,strategy:c})),C=u==="floating"?{x:n,y:o,width:s.floating.width,height:s.floating.height}:s.reference,T=await(a.getOffsetParent==null?void 0:a.getOffsetParent(i.floating)),y=await(a.isElement==null?void 0:a.isElement(T))?await(a.getScale==null?void 0:a.getScale(T))||{x:1,y:1}:{x:1,y:1},I=qt(a.convertOffsetParentRelativeRectToViewportRelativeRect?await a.convertOffsetParentRelativeRectToViewportRelativeRect({elements:i,rect:C,offsetParent:T,strategy:c}):C);return{top:(v.top-I.top+p.top)/y.y,bottom:(I.bottom-v.bottom+p.bottom)/y.y,left:(v.left-I.left+p.left)/y.x,right:(I.right-v.right+p.right)/y.x}}const jc=e=>({name:"arrow",options:e,async fn(t){const{x:r,y:n,placement:o,rects:a,platform:s,elements:i,middlewareData:c}=t,{element:l,padding:b=0}=ft(e,t)||{};if(l==null)return{};const u=Oo(b),m={x:r,y:n},g=zr(o),p=Fr(g),h=await s.getDimensions(l),w=g==="y",v=w?"top":"left",C=w?"bottom":"right",T=w?"clientHeight":"clientWidth",y=a.reference[p]+a.reference[g]-m[g]-a.floating[p],I=m[g]-a.reference[g],A=await(s.getOffsetParent==null?void 0:s.getOffsetParent(l));let B=A?A[T]:0;(!B||!await(s.isElement==null?void 0:s.isElement(A)))&&(B=i.floating[T]||a.floating[p]);const _=y/2-I/2,W=B/2-h[p]/2-1,S=at(u[v],W),L=at(u[C],W),N=S,O=B-h[p]-L,k=B/2-h[p]/2+_,M=Nr(N,k,O),R=!c.arrow&&Ie(o)!=null&&k!==M&&a.reference[p]/2-(k<N?S:L)-h[p]/2<0,P=R?k<N?k-N:k-O:0;return{[g]:m[g]+P,data:{[g]:M,centerOffset:k-M-P,...R&&{alignmentOffset:P}},reset:R}}});function Ec(e,t,r){return(e?[...r.filter(o=>Ie(o)===e),...r.filter(o=>Ie(o)!==e)]:r.filter(o=>Le(o)===o)).filter(o=>e?Ie(o)===e||(t?Yt(o)!==o:!1):!0)}const Ic=function(e){return e===void 0&&(e={}),{name:"autoPlacement",options:e,async fn(t){var r,n,o;const{rects:a,middlewareData:s,placement:i,platform:c,elements:l}=t,{crossAxis:b=!1,alignment:u,allowedPlacements:m=xn,autoAlignment:g=!0,...p}=ft(e,t),h=u!==void 0||m===xn?Ec(u||null,g,m):m,w=await Br(t,p),v=((r=s.autoPlacement)==null?void 0:r.index)||0,C=h[v];if(C==null)return{};const T=So(C,a,await(c.isRTL==null?void 0:c.isRTL(l.floating)));if(i!==C)return{reset:{placement:h[0]}};const y=[w[Le(C)],w[T[0]],w[T[1]]],I=[...((n=s.autoPlacement)==null?void 0:n.overflows)||[],{placement:C,overflows:y}],A=h[v+1];if(A)return{data:{index:v+1,overflows:I},reset:{placement:A}};const B=I.map(S=>{const L=Ie(S.placement);return[S.placement,L&&b?S.overflows.slice(0,2).reduce((N,O)=>N+O,0):S.overflows[0],S.overflows]}).sort((S,L)=>S[1]-L[1]),W=((o=B.filter(S=>S[2].slice(0,Ie(S[0])?2:3).every(L=>L<=0))[0])==null?void 0:o[0])||B[0][0];return W!==i?{data:{index:v+1,overflows:I},reset:{placement:W}}:{}}}},Sc=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var r,n;const{placement:o,middlewareData:a,rects:s,initialPlacement:i,platform:c,elements:l}=t,{mainAxis:b=!0,crossAxis:u=!0,fallbackPlacements:m,fallbackStrategy:g="bestFit",fallbackAxisSideDirection:p="none",flipAlignment:h=!0,...w}=ft(e,t);if((r=a.arrow)!=null&&r.alignmentOffset)return{};const v=Le(o),C=st(i),T=Le(i)===i,y=await(c.isRTL==null?void 0:c.isRTL(l.floating)),I=m||(T||!h?[Kt(i)]:nc(i)),A=p!=="none";!m&&A&&I.push(...ac(i,h,p,y));const B=[i,...I],_=await Br(t,w),W=[];let S=((n=a.flip)==null?void 0:n.overflows)||[];if(b&&W.push(_[v]),u){const k=So(o,s,y);W.push(_[k[0]],_[k[1]])}if(S=[...S,{placement:o,overflows:W}],!W.every(k=>k<=0)){var L,N;const k=(((L=a.flip)==null?void 0:L.index)||0)+1,M=B[k];if(M)return{data:{index:k,overflows:S},reset:{placement:M}};let R=(N=S.filter(P=>P.overflows[0]<=0).sort((P,D)=>P.overflows[1]-D.overflows[1])[0])==null?void 0:N.placement;if(!R)switch(g){case"bestFit":{var O;const P=(O=S.filter(D=>{if(A){const H=st(D.placement);return H===C||H==="y"}return!0}).map(D=>[D.placement,D.overflows.filter(H=>H>0).reduce((H,E)=>H+E,0)]).sort((D,H)=>D[1]-H[1])[0])==null?void 0:O[0];P&&(R=P);break}case"initialPlacement":R=i;break}if(o!==R)return{reset:{placement:R}}}return{}}}};async function Oc(e,t){const{placement:r,platform:n,elements:o}=e,a=await(n.isRTL==null?void 0:n.isRTL(o.floating)),s=Le(r),i=Ie(r),c=st(r)==="y",l=["left","top"].includes(s)?-1:1,b=a&&c?-1:1,u=ft(t,e);let{mainAxis:m,crossAxis:g,alignmentAxis:p}=typeof u=="number"?{mainAxis:u,crossAxis:0,alignmentAxis:null}:{mainAxis:0,crossAxis:0,alignmentAxis:null,...u};return i&&typeof p=="number"&&(g=i==="end"?p*-1:p),c?{x:g*b,y:m*l}:{x:m*l,y:g*b}}const Mc=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var r,n;const{x:o,y:a,placement:s,middlewareData:i}=t,c=await Oc(t,e);return s===((r=i.offset)==null?void 0:r.placement)&&(n=i.arrow)!=null&&n.alignmentOffset?{}:{x:o+c.x,y:a+c.y,data:{...c,placement:s}}}}},Pc=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:r,y:n,placement:o}=t,{mainAxis:a=!0,crossAxis:s=!1,limiter:i={fn:w=>{let{x:v,y:C}=w;return{x:v,y:C}}},...c}=ft(e,t),l={x:r,y:n},b=await Br(t,c),u=st(Le(o)),m=Io(u);let g=l[m],p=l[u];if(a){const w=m==="y"?"top":"left",v=m==="y"?"bottom":"right",C=g+b[w],T=g-b[v];g=Nr(C,g,T)}if(s){const w=u==="y"?"top":"left",v=u==="y"?"bottom":"right",C=p+b[w],T=p-b[v];p=Nr(C,p,T)}const h=i.fn({...t,[m]:g,[u]:p});return{...h,data:{x:h.x-r,y:h.y-n}}}}};function Ao(e){const t=ke(e);let r=parseFloat(t.width)||0,n=parseFloat(t.height)||0;const o=de(e),a=o?e.offsetWidth:r,s=o?e.offsetHeight:n,i=Gt(r)!==a||Gt(n)!==s;return i&&(r=a,n=s),{width:r,height:n,$:i}}function _r(e){return ee(e)?e:e.contextElement}function ot(e){const t=_r(e);if(!de(t))return Ee(1);const r=t.getBoundingClientRect(),{width:n,height:o,$:a}=Ao(t);let s=(a?Gt(r.width):r.width)/n,i=(a?Gt(r.height):r.height)/o;return(!s||!Number.isFinite(s))&&(s=1),(!i||!Number.isFinite(i))&&(i=1),{x:s,y:i}}const Dc=Ee(0);function Fo(e){const t=we(e);return!Mr()||!t.visualViewport?Dc:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function Lc(e,t,r){return t===void 0&&(t=!1),!r||t&&r!==we(e)?!1:t}function Je(e,t,r,n){t===void 0&&(t=!1),r===void 0&&(r=!1);const o=e.getBoundingClientRect(),a=_r(e);let s=Ee(1);t&&(n?ee(n)&&(s=ot(n)):s=ot(e));const i=Lc(a,r,n)?Fo(a):Ee(0);let c=(o.left+i.x)/s.x,l=(o.top+i.y)/s.y,b=o.width/s.x,u=o.height/s.y;if(a){const m=we(a),g=n&&ee(n)?we(n):n;let p=m,h=Cr(p);for(;h&&n&&g!==p;){const w=ot(h),v=h.getBoundingClientRect(),C=ke(h),T=v.left+(h.clientLeft+parseFloat(C.paddingLeft))*w.x,y=v.top+(h.clientTop+parseFloat(C.paddingTop))*w.y;c*=w.x,l*=w.y,b*=w.x,u*=w.y,c+=T,l+=y,p=we(h),h=Cr(p)}}return qt({width:b,height:u,x:c,y:l})}function Hr(e,t){const r=rr(e).scrollLeft;return t?t.left+r:Je(Se(e)).left+r}function zo(e,t,r){r===void 0&&(r=!1);const n=e.getBoundingClientRect(),o=n.left+t.scrollLeft-(r?0:Hr(e,n)),a=n.top+t.scrollTop;return{x:o,y:a}}function Ac(e){let{elements:t,rect:r,offsetParent:n,strategy:o}=e;const a=o==="fixed",s=Se(n),i=t?tr(t.floating):!1;if(n===s||i&&a)return r;let c={scrollLeft:0,scrollTop:0},l=Ee(1);const b=Ee(0),u=de(n);if((u||!u&&!a)&&((Ge(n)!=="body"||Nt(s))&&(c=rr(n)),de(n))){const g=Je(n);l=ot(n),b.x=g.x+n.clientLeft,b.y=g.y+n.clientTop}const m=s&&!u&&!a?zo(s,c,!0):Ee(0);return{width:r.width*l.x,height:r.height*l.y,x:r.x*l.x-c.scrollLeft*l.x+b.x+m.x,y:r.y*l.y-c.scrollTop*l.y+b.y+m.y}}function Fc(e){return Array.from(e.getClientRects())}function zc(e){const t=Se(e),r=rr(e),n=e.ownerDocument.body,o=Ue(t.scrollWidth,t.clientWidth,n.scrollWidth,n.clientWidth),a=Ue(t.scrollHeight,t.clientHeight,n.scrollHeight,n.clientHeight);let s=-r.scrollLeft+Hr(e);const i=-r.scrollTop;return ke(n).direction==="rtl"&&(s+=Ue(t.clientWidth,n.clientWidth)-o),{width:o,height:a,x:s,y:i}}function Bc(e,t){const r=we(e),n=Se(e),o=r.visualViewport;let a=n.clientWidth,s=n.clientHeight,i=0,c=0;if(o){a=o.width,s=o.height;const l=Mr();(!l||l&&t==="fixed")&&(i=o.offsetLeft,c=o.offsetTop)}return{width:a,height:s,x:i,y:c}}function _c(e,t){const r=Je(e,!0,t==="fixed"),n=r.top+e.clientTop,o=r.left+e.clientLeft,a=de(e)?ot(e):Ee(1),s=e.clientWidth*a.x,i=e.clientHeight*a.y,c=o*a.x,l=n*a.y;return{width:s,height:i,x:c,y:l}}function kn(e,t,r){let n;if(t==="viewport")n=Bc(e,r);else if(t==="document")n=zc(Se(e));else if(ee(t))n=_c(t,r);else{const o=Fo(e);n={x:t.x-o.x,y:t.y-o.y,width:t.width,height:t.height}}return qt(n)}function Bo(e,t){const r=De(e);return r===t||!ee(r)||We(r)?!1:ke(r).position==="fixed"||Bo(r,t)}function Hc(e,t){const r=t.get(e);if(r)return r;let n=$e(e,[],!1).filter(i=>ee(i)&&Ge(i)!=="body"),o=null;const a=ke(e).position==="fixed";let s=a?De(e):e;for(;ee(s)&&!We(s);){const i=ke(s),c=Or(s);!c&&i.position==="fixed"&&(o=null),(a?!c&&!o:!c&&i.position==="static"&&!!o&&["absolute","fixed"].includes(o.position)||Nt(s)&&!c&&Bo(e,s))?n=n.filter(b=>b!==s):o=i,s=De(s)}return t.set(e,n),n}function $c(e){let{element:t,boundary:r,rootBoundary:n,strategy:o}=e;const s=[...r==="clippingAncestors"?tr(t)?[]:Hc(t,this._c):[].concat(r),n],i=s[0],c=s.reduce((l,b)=>{const u=kn(t,b,o);return l.top=Ue(u.top,l.top),l.right=at(u.right,l.right),l.bottom=at(u.bottom,l.bottom),l.left=Ue(u.left,l.left),l},kn(t,i,o));return{width:c.right-c.left,height:c.bottom-c.top,x:c.left,y:c.top}}function Wc(e){const{width:t,height:r}=Ao(e);return{width:t,height:r}}function Vc(e,t,r){const n=de(t),o=Se(t),a=r==="fixed",s=Je(e,!0,a,t);let i={scrollLeft:0,scrollTop:0};const c=Ee(0);if(n||!n&&!a)if((Ge(t)!=="body"||Nt(o))&&(i=rr(t)),n){const m=Je(t,!0,a,t);c.x=m.x+t.clientLeft,c.y=m.y+t.clientTop}else o&&(c.x=Hr(o));const l=o&&!n&&!a?zo(o,i):Ee(0),b=s.left+i.scrollLeft-c.x-l.x,u=s.top+i.scrollTop-c.y-l.y;return{x:b,y:u,width:s.width,height:s.height}}function fr(e){return ke(e).position==="static"}function Cn(e,t){if(!de(e)||ke(e).position==="fixed")return null;if(t)return t(e);let r=e.offsetParent;return Se(e)===r&&(r=r.ownerDocument.body),r}function _o(e,t){const r=we(e);if(tr(e))return r;if(!de(e)){let o=De(e);for(;o&&!We(o);){if(ee(o)&&!fr(o))return o;o=De(o)}return r}let n=Cn(e,t);for(;n&&ql(n)&&fr(n);)n=Cn(n,t);return n&&We(n)&&fr(n)&&!Or(n)?r:n||Ul(e)||r}const Gc=async function(e){const t=this.getOffsetParent||_o,r=this.getDimensions,n=await r(e.floating);return{reference:Vc(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:n.width,height:n.height}}};function Yc(e){return ke(e).direction==="rtl"}const Kc={convertOffsetParentRelativeRectToViewportRelativeRect:Ac,getDocumentElement:Se,getClippingRect:$c,getOffsetParent:_o,getElementRects:Gc,getClientRects:Fc,getDimensions:Wc,getScale:ot,isElement:ee,isRTL:Yc};function Ho(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function qc(e,t){let r=null,n;const o=Se(e);function a(){var i;clearTimeout(n),(i=r)==null||i.disconnect(),r=null}function s(i,c){i===void 0&&(i=!1),c===void 0&&(c=1),a();const l=e.getBoundingClientRect(),{left:b,top:u,width:m,height:g}=l;if(i||t(),!m||!g)return;const p=et(u),h=et(o.clientWidth-(b+m)),w=et(o.clientHeight-(u+g)),v=et(b),T={rootMargin:-p+"px "+-h+"px "+-w+"px "+-v+"px",threshold:Ue(0,at(1,c))||1};let y=!0;function I(A){const B=A[0].intersectionRatio;if(B!==c){if(!y)return s();B?s(!1,B):n=setTimeout(()=>{s(!1,1e-7)},1e3)}B===1&&!Ho(l,e.getBoundingClientRect())&&s(),y=!1}try{r=new IntersectionObserver(I,{...T,root:o.ownerDocument})}catch{r=new IntersectionObserver(I,T)}r.observe(e)}return s(!0),a}function $o(e,t,r,n){n===void 0&&(n={});const{ancestorScroll:o=!0,ancestorResize:a=!0,elementResize:s=typeof ResizeObserver=="function",layoutShift:i=typeof IntersectionObserver=="function",animationFrame:c=!1}=n,l=_r(e),b=o||a?[...l?$e(l):[],...$e(t)]:[];b.forEach(v=>{o&&v.addEventListener("scroll",r,{passive:!0}),a&&v.addEventListener("resize",r)});const u=l&&i?qc(l,r):null;let m=-1,g=null;s&&(g=new ResizeObserver(v=>{let[C]=v;C&&C.target===l&&g&&(g.unobserve(t),cancelAnimationFrame(m),m=requestAnimationFrame(()=>{var T;(T=g)==null||T.observe(t)})),r()}),l&&!c&&g.observe(l),g.observe(t));let p,h=c?Je(e):null;c&&w();function w(){const v=Je(e);h&&!Ho(h,v)&&r(),h=v,p=requestAnimationFrame(w)}return r(),()=>{var v;b.forEach(C=>{o&&C.removeEventListener("scroll",r),a&&C.removeEventListener("resize",r)}),u==null||u(),(v=g)==null||v.disconnect(),g=null,c&&cancelAnimationFrame(p)}}const Uc=Mc,Xc=Ic,Jc=Pc,Zc=Sc,Tn=jc,Qc=(e,t,r)=>{const n=new Map,o={platform:Kc,...r},a={...o.platform,_c:n};return Rc(e,t,{...o,platform:a})};var Bt=typeof document<"u"?f.useLayoutEffect:f.useEffect;function Zt(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let r,n,o;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(r=e.length,r!==t.length)return!1;for(n=r;n--!==0;)if(!Zt(e[n],t[n]))return!1;return!0}if(o=Object.keys(e),r=o.length,r!==Object.keys(t).length)return!1;for(n=r;n--!==0;)if(!{}.hasOwnProperty.call(t,o[n]))return!1;for(n=r;n--!==0;){const a=o[n];if(!(a==="_owner"&&e.$$typeof)&&!Zt(e[a],t[a]))return!1}return!0}return e!==e&&t!==t}function Wo(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function Nn(e,t){const r=Wo(e);return Math.round(t*r)/r}function gr(e){const t=f.useRef(e);return Bt(()=>{t.current=e}),t}function ed(e){e===void 0&&(e={});const{placement:t="bottom",strategy:r="absolute",middleware:n=[],platform:o,elements:{reference:a,floating:s}={},transform:i=!0,whileElementsMounted:c,open:l}=e,[b,u]=f.useState({x:0,y:0,strategy:r,placement:t,middlewareData:{},isPositioned:!1}),[m,g]=f.useState(n);Zt(m,n)||g(n);const[p,h]=f.useState(null),[w,v]=f.useState(null),C=f.useCallback(D=>{D!==A.current&&(A.current=D,h(D))},[]),T=f.useCallback(D=>{D!==B.current&&(B.current=D,v(D))},[]),y=a||p,I=s||w,A=f.useRef(null),B=f.useRef(null),_=f.useRef(b),W=c!=null,S=gr(c),L=gr(o),N=gr(l),O=f.useCallback(()=>{if(!A.current||!B.current)return;const D={placement:t,strategy:r,middleware:m};L.current&&(D.platform=L.current),Qc(A.current,B.current,D).then(H=>{const E={...H,isPositioned:N.current!==!1};k.current&&!Zt(_.current,E)&&(_.current=E,Gn.flushSync(()=>{u(E)}))})},[m,t,r,L,N]);Bt(()=>{l===!1&&_.current.isPositioned&&(_.current.isPositioned=!1,u(D=>({...D,isPositioned:!1})))},[l]);const k=f.useRef(!1);Bt(()=>(k.current=!0,()=>{k.current=!1}),[]),Bt(()=>{if(y&&(A.current=y),I&&(B.current=I),y&&I){if(S.current)return S.current(y,I,O);O()}},[y,I,O,S,W]);const M=f.useMemo(()=>({reference:A,floating:B,setReference:C,setFloating:T}),[C,T]),R=f.useMemo(()=>({reference:y,floating:I}),[y,I]),P=f.useMemo(()=>{const D={position:r,left:0,top:0};if(!R.floating)return D;const H=Nn(R.floating,b.x),E=Nn(R.floating,b.y);return i?{...D,transform:"translate("+H+"px, "+E+"px)",...Wo(R.floating)>=1.5&&{willChange:"transform"}}:{position:r,left:H,top:E}},[r,i,R.floating,b.x,b.y]);return f.useMemo(()=>({...b,update:O,refs:M,elements:R,floatingStyles:P}),[b,O,M,R,P])}const td=e=>{function t(r){return{}.hasOwnProperty.call(r,"current")}return{name:"arrow",options:e,fn(r){const{element:n,padding:o}=typeof e=="function"?e(r):e;return n&&t(n)?n.current!=null?Tn({element:n.current,padding:o}).fn(r):{}:n?Tn({element:n,padding:o}).fn(r):{}}}},rd=(e,t)=>({...Uc(e),options:[e,t]}),nd=(e,t)=>({...Jc(e),options:[e,t]}),od=(e,t)=>({...Zc(e),options:[e,t]}),ad=(e,t)=>({...Xc(e),options:[e,t]}),sd=(e,t)=>({...td(e),options:[e,t]});function $r(e){return f.useMemo(()=>e.every(t=>t==null)?null:t=>{e.forEach(r=>{typeof r=="function"?r(t):r!=null&&(r.current=t)})},e)}const Vo={...Bs},id=Vo.useInsertionEffect,ld=id||(e=>e());function pe(e){const t=f.useRef(()=>{});return ld(()=>{t.current=e}),f.useCallback(function(){for(var r=arguments.length,n=new Array(r),o=0;o<r;o++)n[o]=arguments[o];return t.current==null?void 0:t.current(...n)},[])}const Wr="ArrowUp",Rt="ArrowDown",lt="ArrowLeft",gt="ArrowRight";function Dt(e,t,r){return Math.floor(e/t)!==r}function yt(e,t){return t<0||t>=e.current.length}function br(e,t){return he(e,{disabledIndices:t})}function Rn(e,t){return he(e,{decrement:!0,startingIndex:e.current.length,disabledIndices:t})}function he(e,t){let{startingIndex:r=-1,decrement:n=!1,disabledIndices:o,amount:a=1}=t===void 0?{}:t;const s=e.current;let i=r;do i+=n?-a:a;while(i>=0&&i<=s.length-1&&_t(s,i,o));return i}function cd(e,t){let{event:r,orientation:n,loop:o,cols:a,disabledIndices:s,minIndex:i,maxIndex:c,prevIndex:l,stopEvent:b=!1}=t,u=l;if(r.key===Wr){if(b&&be(r),l===-1)u=c;else if(u=he(e,{startingIndex:u,amount:a,decrement:!0,disabledIndices:s}),o&&(l-a<i||u<0)){const m=l%a,g=c%a,p=c-(g-m);g===m?u=c:u=g>m?p:p-a}yt(e,u)&&(u=l)}if(r.key===Rt&&(b&&be(r),l===-1?u=i:(u=he(e,{startingIndex:l,amount:a,disabledIndices:s}),o&&l+a>c&&(u=he(e,{startingIndex:l%a-a,amount:a,disabledIndices:s}))),yt(e,u)&&(u=l)),n==="both"){const m=et(l/a);r.key===gt&&(b&&be(r),l%a!==a-1?(u=he(e,{startingIndex:l,disabledIndices:s}),o&&Dt(u,a,m)&&(u=he(e,{startingIndex:l-l%a-1,disabledIndices:s}))):o&&(u=he(e,{startingIndex:l-l%a-1,disabledIndices:s})),Dt(u,a,m)&&(u=l)),r.key===lt&&(b&&be(r),l%a!==0?(u=he(e,{startingIndex:l,decrement:!0,disabledIndices:s}),o&&Dt(u,a,m)&&(u=he(e,{startingIndex:l+(a-l%a),decrement:!0,disabledIndices:s}))):o&&(u=he(e,{startingIndex:l+(a-l%a),decrement:!0,disabledIndices:s})),Dt(u,a,m)&&(u=l));const g=et(c/a)===m;yt(e,u)&&(o&&g?u=r.key===lt?c:he(e,{startingIndex:l-l%a-1,disabledIndices:s}):u=l)}return u}function dd(e,t,r){const n=[];let o=0;return e.forEach((a,s)=>{let{width:i,height:c}=a,l=!1;for(r&&(o=0);!l;){const b=[];for(let u=0;u<i;u++)for(let m=0;m<c;m++)b.push(o+u+m*t);o%t+i<=t&&b.every(u=>n[u]==null)?(b.forEach(u=>{n[u]=s}),l=!0):o++}}),[...n]}function ud(e,t,r,n,o){if(e===-1)return-1;const a=r.indexOf(e),s=t[e];switch(o){case"tl":return a;case"tr":return s?a+s.width-1:a;case"bl":return s?a+(s.height-1)*n:a;case"br":return r.lastIndexOf(e)}}function fd(e,t){return t.flatMap((r,n)=>e.includes(r)?[n]:[])}function _t(e,t,r){if(r)return r.includes(t);const n=e[t];return n==null||n.hasAttribute("disabled")||n.getAttribute("aria-disabled")==="true"}let jn=0;function He(e,t){t===void 0&&(t={});const{preventScroll:r=!1,cancelPrevious:n=!0,sync:o=!1}=t;n&&cancelAnimationFrame(jn);const a=()=>e==null?void 0:e.focus({preventScroll:r});o?a():jn=requestAnimationFrame(a)}var Q=typeof document<"u"?f.useLayoutEffect:f.useEffect;function gd(e,t){const r=e.compareDocumentPosition(t);return r&Node.DOCUMENT_POSITION_FOLLOWING||r&Node.DOCUMENT_POSITION_CONTAINED_BY?-1:r&Node.DOCUMENT_POSITION_PRECEDING||r&Node.DOCUMENT_POSITION_CONTAINS?1:0}function bd(e,t){if(e.size!==t.size)return!1;for(const[r,n]of e.entries())if(n!==t.get(r))return!1;return!0}const Go=f.createContext({register:()=>{},unregister:()=>{},map:new Map,elementsRef:{current:[]}});function pd(e){const{children:t,elementsRef:r,labelsRef:n}=e,[o,a]=f.useState(()=>new Map),s=f.useCallback(c=>{a(l=>new Map(l).set(c,null))},[]),i=f.useCallback(c=>{a(l=>{const b=new Map(l);return b.delete(c),b})},[]);return Q(()=>{const c=new Map(o);Array.from(c.keys()).sort(gd).forEach((b,u)=>{c.set(b,u)}),bd(o,c)||a(c)},[o]),f.createElement(Go.Provider,{value:f.useMemo(()=>({register:s,unregister:i,map:o,elementsRef:r,labelsRef:n}),[s,i,o,r,n])},t)}function md(e){e===void 0&&(e={});const{label:t}=e,{register:r,unregister:n,map:o,elementsRef:a,labelsRef:s}=f.useContext(Go),[i,c]=f.useState(null),l=f.useRef(null),b=f.useCallback(u=>{if(l.current=u,i!==null&&(a.current[i]=u,s)){var m;const g=t!==void 0;s.current[i]=g?t:(m=u==null?void 0:u.textContent)!=null?m:null}},[i,a,s,t]);return Q(()=>{const u=l.current;if(u)return r(u),()=>{n(u)}},[r,n]),Q(()=>{const u=l.current?o.get(l.current):null;u!=null&&c(u)},[o]),f.useMemo(()=>({ref:b,index:i??-1}),[i,b])}function Ct(){return Ct=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ct.apply(this,arguments)}let En=!1,hd=0;const In=()=>"floating-ui-"+Math.random().toString(36).slice(2,6)+hd++;function yd(){const[e,t]=f.useState(()=>En?In():void 0);return Q(()=>{e==null&&t(In())},[]),f.useEffect(()=>{En=!0},[]),e}const xd=Vo.useId,nr=xd||yd;function wd(){const e=new Map;return{emit(t,r){var n;(n=e.get(t))==null||n.forEach(o=>o(r))},on(t,r){e.set(t,[...e.get(t)||[],r])},off(t,r){var n;e.set(t,((n=e.get(t))==null?void 0:n.filter(o=>o!==r))||[])}}}const vd=f.createContext(null),kd=f.createContext(null),or=()=>{var e;return((e=f.useContext(vd))==null?void 0:e.id)||null},jt=()=>f.useContext(kd);function Ve(e){return"data-floating-ui-"+e}function ve(e){const t=f.useRef(e);return Q(()=>{t.current=e}),t}const Sn=Ve("safe-polygon");function pr(e,t,r){return r&&!vt(r)?0:typeof e=="number"?e:e==null?void 0:e[t]}function Cd(e,t){t===void 0&&(t={});const{open:r,onOpenChange:n,dataRef:o,events:a,elements:s}=e,{enabled:i=!0,delay:c=0,handleClose:l=null,mouseOnly:b=!1,restMs:u=0,move:m=!0}=t,g=jt(),p=or(),h=ve(l),w=ve(c),v=ve(r),C=f.useRef(),T=f.useRef(-1),y=f.useRef(),I=f.useRef(-1),A=f.useRef(!0),B=f.useRef(!1),_=f.useRef(()=>{}),W=f.useCallback(()=>{var M;const R=(M=o.current.openEvent)==null?void 0:M.type;return(R==null?void 0:R.includes("mouse"))&&R!=="mousedown"},[o]);f.useEffect(()=>{if(!i)return;function M(R){let{open:P}=R;P||(clearTimeout(T.current),clearTimeout(I.current),A.current=!0)}return a.on("openchange",M),()=>{a.off("openchange",M)}},[i,a]),f.useEffect(()=>{if(!i||!h.current||!r)return;function M(P){W()&&n(!1,P,"hover")}const R=ge(s.floating).documentElement;return R.addEventListener("mouseleave",M),()=>{R.removeEventListener("mouseleave",M)}},[s.floating,r,n,i,h,W]);const S=f.useCallback(function(M,R,P){R===void 0&&(R=!0),P===void 0&&(P="hover");const D=pr(w.current,"close",C.current);D&&!y.current?(clearTimeout(T.current),T.current=window.setTimeout(()=>n(!1,M,P),D)):R&&(clearTimeout(T.current),n(!1,M,P))},[w,n]),L=pe(()=>{_.current(),y.current=void 0}),N=pe(()=>{if(B.current){const M=ge(s.floating).body;M.style.pointerEvents="",M.removeAttribute(Sn),B.current=!1}});f.useEffect(()=>{if(!i)return;function M(){return o.current.openEvent?["click","mousedown"].includes(o.current.openEvent.type):!1}function R(E){if(clearTimeout(T.current),A.current=!1,b&&!vt(C.current)||u>0&&!pr(w.current,"open"))return;const Y=pr(w.current,"open",C.current);Y?T.current=window.setTimeout(()=>{v.current||n(!0,E,"hover")},Y):n(!0,E,"hover")}function P(E){if(M())return;_.current();const Y=ge(s.floating);if(clearTimeout(I.current),h.current&&o.current.floatingContext){r||clearTimeout(T.current),y.current=h.current({...o.current.floatingContext,tree:g,x:E.clientX,y:E.clientY,onClose(){N(),L(),S(E,!0,"safe-polygon")}});const me=y.current;Y.addEventListener("mousemove",me),_.current=()=>{Y.removeEventListener("mousemove",me)};return}(C.current==="touch"?!ce(s.floating,E.relatedTarget):!0)&&S(E)}function D(E){M()||o.current.floatingContext&&(h.current==null||h.current({...o.current.floatingContext,tree:g,x:E.clientX,y:E.clientY,onClose(){N(),L(),S(E)}})(E))}if(ee(s.domReference)){var H;const E=s.domReference;return r&&E.addEventListener("mouseleave",D),(H=s.floating)==null||H.addEventListener("mouseleave",D),m&&E.addEventListener("mousemove",R,{once:!0}),E.addEventListener("mouseenter",R),E.addEventListener("mouseleave",P),()=>{var Y;r&&E.removeEventListener("mouseleave",D),(Y=s.floating)==null||Y.removeEventListener("mouseleave",D),m&&E.removeEventListener("mousemove",R),E.removeEventListener("mouseenter",R),E.removeEventListener("mouseleave",P)}}},[s,i,e,b,u,m,S,L,N,n,r,v,g,w,h,o]),Q(()=>{var M;if(i&&r&&(M=h.current)!=null&&M.__options.blockPointerEvents&&W()){const P=ge(s.floating).body;P.setAttribute(Sn,""),P.style.pointerEvents="none",B.current=!0;const D=s.floating;if(ee(s.domReference)&&D){var R;const H=s.domReference,E=g==null||(R=g.nodesRef.current.find(Y=>Y.id===p))==null||(R=R.context)==null?void 0:R.elements.floating;return E&&(E.style.pointerEvents=""),H.style.pointerEvents="auto",D.style.pointerEvents="auto",()=>{H.style.pointerEvents="",D.style.pointerEvents=""}}}},[i,r,p,s,g,h,W]),Q(()=>{r||(C.current=void 0,L(),N())},[r,L,N]),f.useEffect(()=>()=>{L(),clearTimeout(T.current),clearTimeout(I.current),N()},[i,s.domReference,L,N]);const O=f.useMemo(()=>{function M(R){C.current=R.pointerType}return{onPointerDown:M,onPointerEnter:M,onMouseMove(R){const{nativeEvent:P}=R;function D(){!A.current&&!v.current&&n(!0,P,"hover")}b&&!vt(C.current)||r||u===0||(clearTimeout(I.current),C.current==="touch"?D():I.current=window.setTimeout(D,u))}}},[b,n,r,v,u]),k=f.useMemo(()=>({onMouseEnter(){clearTimeout(T.current)},onMouseLeave(M){S(M.nativeEvent,!1)}}),[S]);return f.useMemo(()=>i?{reference:O,floating:k}:{},[i,O,k])}function Td(e,t){var r;let n=[],o=(r=e.find(a=>a.id===t))==null?void 0:r.parentId;for(;o;){const a=e.find(s=>s.id===o);o=a==null?void 0:a.parentId,a&&(n=n.concat(a))}return n}function Xe(e,t){let r=e.filter(o=>{var a;return o.parentId===t&&((a=o.context)==null?void 0:a.open)}),n=r;for(;n.length;)n=e.filter(o=>{var a;return(a=n)==null?void 0:a.some(s=>{var i;return o.parentId===s.id&&((i=o.context)==null?void 0:i.open)})}),r=r.concat(n);return r}function Nd(e,t){let r,n=-1;function o(a,s){s>n&&(r=a,n=s),Xe(e,a).forEach(c=>{o(c.id,s+1)})}return o(t,0),e.find(a=>a.id===r)}let Qe=new WeakMap,Lt=new WeakSet,At={},mr=0;const Rd=()=>typeof HTMLElement<"u"&&"inert"in HTMLElement.prototype,Yo=e=>e&&(e.host||Yo(e.parentNode)),jd=(e,t)=>t.map(r=>{if(e.contains(r))return r;const n=Yo(r);return e.contains(n)?n:null}).filter(r=>r!=null);function Ed(e,t,r,n){const o="data-floating-ui-inert",a=n?"inert":r?"aria-hidden":null,s=jd(t,e),i=new Set,c=new Set(s),l=[];At[o]||(At[o]=new WeakMap);const b=At[o];s.forEach(u),m(t),i.clear();function u(g){!g||i.has(g)||(i.add(g),g.parentNode&&u(g.parentNode))}function m(g){!g||c.has(g)||[].forEach.call(g.children,p=>{if(Ge(p)!=="script")if(i.has(p))m(p);else{const h=a?p.getAttribute(a):null,w=h!==null&&h!=="false",v=(Qe.get(p)||0)+1,C=(b.get(p)||0)+1;Qe.set(p,v),b.set(p,C),l.push(p),v===1&&w&&Lt.add(p),C===1&&p.setAttribute(o,""),!w&&a&&p.setAttribute(a,"true")}})}return mr++,()=>{l.forEach(g=>{const p=(Qe.get(g)||0)-1,h=(b.get(g)||0)-1;Qe.set(g,p),b.set(g,h),p||(!Lt.has(g)&&a&&g.removeAttribute(a),Lt.delete(g)),h||g.removeAttribute(o)}),mr--,mr||(Qe=new WeakMap,Qe=new WeakMap,Lt=new WeakSet,At={})}}function On(e,t,r){t===void 0&&(t=!1),r===void 0&&(r=!1);const n=ge(e[0]).body;return Ed(e.concat(Array.from(n.querySelectorAll("[aria-live]"))),n,t,r)}const ct=()=>({getShadowRoot:!0,displayCheck:typeof ResizeObserver=="function"&&ResizeObserver.toString().includes("[native code]")?"full":"none"});function Ko(e,t){const r=kt(e,ct());t==="prev"&&r.reverse();const n=r.indexOf(Ne(ge(e)));return r.slice(n+1)[0]}function qo(){return Ko(document.body,"next")}function Uo(){return Ko(document.body,"prev")}function xt(e,t){const r=t||e.currentTarget,n=e.relatedTarget;return!n||!ce(r,n)}function Id(e){kt(e,ct()).forEach(r=>{r.dataset.tabindex=r.getAttribute("tabindex")||"",r.setAttribute("tabindex","-1")})}function Sd(e){e.querySelectorAll("[data-tabindex]").forEach(r=>{const n=r.dataset.tabindex;delete r.dataset.tabindex,n?r.setAttribute("tabindex",n):r.removeAttribute("tabindex")})}function Od(e,t,r){const n=e.indexOf(t);function o(s){const i=Ve("focus-guard");let c=n+(s?1:0),l=e[c];for(;l&&(!l.isConnected||l.hasAttribute(i)||ce(r,l));)s?c++:c--,l=e[c];return l}const a=o(!0);return a||o(!1)}const Vr={border:0,clip:"rect(0 0 0 0)",height:"1px",margin:"-1px",overflow:"hidden",padding:0,position:"fixed",whiteSpace:"nowrap",width:"1px",top:0,left:0};let Md;function Mn(e){e.key==="Tab"&&(e.target,clearTimeout(Md))}const Qt=f.forwardRef(function(t,r){const[n,o]=f.useState();Q(()=>(Lr()&&o("button"),document.addEventListener("keydown",Mn),()=>{document.removeEventListener("keydown",Mn)}),[]);const a={ref:r,tabIndex:0,role:n,"aria-hidden":n?void 0:!0,[Ve("focus-guard")]:"",style:Vr};return f.createElement("span",Ct({},t,a))}),Xo=f.createContext(null),Pn=Ve("portal");function Pd(e){e===void 0&&(e={});const{id:t,root:r}=e,n=nr(),o=Jo(),[a,s]=f.useState(null),i=f.useRef(null);return Q(()=>()=>{a==null||a.remove(),queueMicrotask(()=>{i.current=null})},[a]),Q(()=>{if(!n||i.current)return;const c=t?document.getElementById(t):null;if(!c)return;const l=document.createElement("div");l.id=n,l.setAttribute(Pn,""),c.appendChild(l),i.current=l,s(l)},[t,n]),Q(()=>{if(!n||i.current)return;let c=r||(o==null?void 0:o.portalNode);c&&!ee(c)&&(c=c.current),c=c||document.body;let l=null;t&&(l=document.createElement("div"),l.id=t,c.appendChild(l));const b=document.createElement("div");b.id=n,b.setAttribute(Pn,""),c=l||c,c.appendChild(b),i.current=b,s(b)},[t,r,n,o]),a}function Dd(e){const{children:t,id:r,root:n=null,preserveTabOrder:o=!0}=e,a=Pd({id:r,root:n}),[s,i]=f.useState(null),c=f.useRef(null),l=f.useRef(null),b=f.useRef(null),u=f.useRef(null),m=!!s&&!s.modal&&s.open&&o&&!!(n||a);return f.useEffect(()=>{if(!a||!o||s!=null&&s.modal)return;function g(p){a&&xt(p)&&(p.type==="focusin"?Sd:Id)(a)}return a.addEventListener("focusin",g,!0),a.addEventListener("focusout",g,!0),()=>{a.removeEventListener("focusin",g,!0),a.removeEventListener("focusout",g,!0)}},[a,o,s==null?void 0:s.modal]),f.createElement(Xo.Provider,{value:f.useMemo(()=>({preserveTabOrder:o,beforeOutsideRef:c,afterOutsideRef:l,beforeInsideRef:b,afterInsideRef:u,portalNode:a,setFocusManagerState:i}),[o,a])},m&&a&&f.createElement(Qt,{"data-type":"outside",ref:c,onFocus:g=>{if(xt(g,a)){var p;(p=b.current)==null||p.focus()}else{const h=Uo()||(s==null?void 0:s.refs.domReference.current);h==null||h.focus()}}}),m&&a&&f.createElement("span",{"aria-owns":a.id,style:Vr}),a&&Gn.createPortal(t,a),m&&a&&f.createElement(Qt,{"data-type":"outside",ref:l,onFocus:g=>{if(xt(g,a)){var p;(p=u.current)==null||p.focus()}else{const h=qo()||(s==null?void 0:s.refs.domReference.current);h==null||h.focus(),s!=null&&s.closeOnFocusOut&&(s==null||s.onOpenChange(!1,g.nativeEvent))}}}))}const Jo=()=>f.useContext(Xo),Ld=20;let Ke=[];function hr(e){Ke=Ke.filter(r=>r.isConnected);let t=e;if(!(!t||Ge(t)==="body")){if(!Nc(t,ct())){const r=kt(t,ct())[0];r&&(t=r)}Ke.push(t),Ke.length>Ld&&(Ke=Ke.slice(-20))}}function Dn(){return Ke.slice().reverse().find(e=>e.isConnected)}const Ad=f.forwardRef(function(t,r){return f.createElement("button",Ct({},t,{type:"button",ref:r,tabIndex:-1,style:Vr}))});function Gr(e){const{context:t,children:r,disabled:n=!1,order:o=["content"],guards:a=!0,initialFocus:s=0,returnFocus:i=!0,restoreFocus:c=!1,modal:l=!0,visuallyHiddenDismiss:b=!1,closeOnFocusOut:u=!0}=e,{open:m,refs:g,nodeId:p,onOpenChange:h,events:w,dataRef:v,floatingId:C,elements:{domReference:T,floating:y}}=t,I=typeof s=="number"&&s<0,A=Eo(T)&&I,B=Rd()?a:!0,_=ve(o),W=ve(s),S=ve(i),L=jt(),N=Jo(),O=f.useRef(null),k=f.useRef(null),M=f.useRef(!1),R=f.useRef(!1),P=f.useRef(-1),D=N!=null,H=y==null?void 0:y.firstElementChild,E=(H==null?void 0:H.id)===C?H:y,Y=pe(function(F){return F===void 0&&(F=E),F?kt(F,ct()):[]}),ne=pe(F=>{const V=Y(F);return _.current.map(G=>T&&G==="reference"?T:E&&G==="floating"?E:V).filter(Boolean).flat()});f.useEffect(()=>{if(n||!l)return;function F(G){if(G.key==="Tab"){ce(E,Ne(ge(E)))&&Y().length===0&&!A&&be(G);const $=ne(),U=Pe(G);_.current[0]==="reference"&&U===T&&(be(G),G.shiftKey?He($[$.length-1]):He($[1])),_.current[1]==="floating"&&U===E&&G.shiftKey&&(be(G),He($[0]))}}const V=ge(E);return V.addEventListener("keydown",F),()=>{V.removeEventListener("keydown",F)}},[n,T,E,l,_,A,Y,ne]),f.useEffect(()=>{if(n||!y)return;function F(V){const G=Pe(V),U=Y().indexOf(G);U!==-1&&(P.current=U)}return y.addEventListener("focusin",F),()=>{y.removeEventListener("focusin",F)}},[n,y,Y]),f.useEffect(()=>{if(n||!u)return;function F(){R.current=!0,setTimeout(()=>{R.current=!1})}function V(G){const $=G.relatedTarget;queueMicrotask(()=>{const U=!(ce(T,$)||ce(y,$)||ce($,y)||ce(N==null?void 0:N.portalNode,$)||$!=null&&$.hasAttribute(Ve("focus-guard"))||L&&(Xe(L.nodesRef.current,p).find(Z=>{var le,te;return ce((le=Z.context)==null?void 0:le.elements.floating,$)||ce((te=Z.context)==null?void 0:te.elements.domReference,$)})||Td(L.nodesRef.current,p).find(Z=>{var le,te;return((le=Z.context)==null?void 0:le.elements.floating)===$||((te=Z.context)==null?void 0:te.elements.domReference)===$})));if(c&&U&&Ne(ge(E))===ge(E).body){de(E)&&(E==null||E.focus());const Z=P.current,le=Y(),te=le[Z]||le[le.length-1]||E;de(te)&&te.focus()}(A||!l)&&$&&U&&!R.current&&$!==Dn()&&(M.current=!0,h(!1,G))})}if(y&&de(T))return T.addEventListener("focusout",V),T.addEventListener("pointerdown",F),y.addEventListener("focusout",V),()=>{T.removeEventListener("focusout",V),T.removeEventListener("pointerdown",F),y.removeEventListener("focusout",V)}},[n,T,y,E,l,p,L,N,h,u,c,Y,A]),f.useEffect(()=>{var F;if(n)return;const V=Array.from((N==null||(F=N.portalNode)==null?void 0:F.querySelectorAll("["+Ve("portal")+"]"))||[]);if(y){const G=[y,...V,O.current,k.current,_.current.includes("reference")||A?T:null].filter(U=>U!=null),$=l||A?On(G,B,!B):On(G);return()=>{$()}}},[n,T,y,l,_,N,A,B]),Q(()=>{if(n||!de(E))return;const F=ge(E),V=Ne(F);queueMicrotask(()=>{const G=ne(E),$=W.current,U=(typeof $=="number"?G[$]:$.current)||E,Z=ce(E,V);!I&&!Z&&m&&He(U,{preventScroll:U===E})})},[n,m,E,I,ne,W]),Q(()=>{if(n||!E)return;let F=!1;const V=ge(E),G=Ne(V);let U=v.current.openEvent;const Z=g.domReference.current;hr(G);function le(te){let{open:Re,reason:Oe,event:Ce,nested:Ze}=te;Re&&(U=Ce),Oe==="escape-key"&&g.domReference.current&&hr(g.domReference.current),Oe==="hover"&&Ce.type==="mouseleave"&&(M.current=!0),Oe==="outside-press"&&(Ze?(M.current=!1,F=!0):M.current=!(Ro(Ce)||Dr(Ce)))}return w.on("openchange",le),()=>{w.off("openchange",le);const te=Ne(V),Re=ce(y,te)||L&&Xe(L.nodesRef.current,p).some(q=>{var re;return ce((re=q.context)==null?void 0:re.elements.floating,te)});(Re||U&&["click","mousedown"].includes(U.type))&&g.domReference.current&&hr(g.domReference.current);const Ce=Z||G,Ze=kt(ge(Ce).body,ct());queueMicrotask(()=>{let q=Dn();!q&&de(Ce)&&y&&(q=Od(Ze,Ce,y)),S.current&&!M.current&&de(q)&&(!(q!==te&&te!==V.body)||Re)&&q.focus({preventScroll:F})})}},[n,y,E,S,v,g,w,L,p]),Q(()=>{if(!n&&N)return N.setFocusManagerState({modal:l,closeOnFocusOut:u,open:m,onOpenChange:h,refs:g}),()=>{N.setFocusManagerState(null)}},[n,N,l,m,h,g,u]),Q(()=>{if(n||!E||typeof MutationObserver!="function"||I)return;const F=()=>{const G=E.getAttribute("tabindex"),$=Y(),U=Ne(ge(y)),Z=$.indexOf(U);Z!==-1&&(P.current=Z),_.current.includes("floating")||U!==g.domReference.current&&$.length===0?G!=="0"&&E.setAttribute("tabindex","0"):G!=="-1"&&E.setAttribute("tabindex","-1")};F();const V=new MutationObserver(F);return V.observe(E,{childList:!0,subtree:!0,attributes:!0}),()=>{V.disconnect()}},[n,y,E,g,_,Y,I]);function me(F){return n||!b||!l?null:f.createElement(Ad,{ref:F==="start"?O:k,onClick:V=>h(!1,V.nativeEvent)},typeof b=="string"?b:"Dismiss")}const J=!n&&B&&(l?!A:!0)&&(D||l);return f.createElement(f.Fragment,null,J&&f.createElement(Qt,{"data-type":"inside",ref:N==null?void 0:N.beforeInsideRef,onFocus:F=>{if(l){const G=ne();He(o[0]==="reference"?G[0]:G[G.length-1])}else if(N!=null&&N.preserveTabOrder&&N.portalNode)if(M.current=!1,xt(F,N.portalNode)){const G=qo()||T;G==null||G.focus()}else{var V;(V=N.beforeOutsideRef.current)==null||V.focus()}}}),!A&&me("start"),r,me("end"),J&&f.createElement(Qt,{"data-type":"inside",ref:N==null?void 0:N.afterInsideRef,onFocus:F=>{if(l)He(ne()[0]);else if(N!=null&&N.preserveTabOrder&&N.portalNode)if(u&&(M.current=!0),xt(F,N.portalNode)){const G=Uo()||T;G==null||G.focus()}else{var V;(V=N.afterOutsideRef.current)==null||V.focus()}}}))}const yr=new Set,Fd=f.forwardRef(function(t,r){const{lockScroll:n=!1,...o}=t,a=nr();return Q(()=>{if(!n)return;yr.add(a);const s=/iP(hone|ad|od)|iOS/.test(Pr()),i=document.body.style,l=Math.round(document.documentElement.getBoundingClientRect().left)+document.documentElement.scrollLeft?"paddingLeft":"paddingRight",b=window.innerWidth-document.documentElement.clientWidth,u=i.left?parseFloat(i.left):window.scrollX,m=i.top?parseFloat(i.top):window.scrollY;if(i.overflow="hidden",b&&(i[l]=b+"px"),s){var g,p;const h=((g=window.visualViewport)==null?void 0:g.offsetLeft)||0,w=((p=window.visualViewport)==null?void 0:p.offsetTop)||0;Object.assign(i,{position:"fixed",top:-(m-Math.floor(w))+"px",left:-(u-Math.floor(h))+"px",right:"0"})}return()=>{yr.delete(a),yr.size===0&&(Object.assign(i,{overflow:"",[l]:""}),s&&(Object.assign(i,{position:"",top:"",left:"",right:""}),window.scrollTo(u,m)))}},[a,n]),f.createElement("div",Ct({ref:r},o,{style:{position:"fixed",overflow:"auto",top:0,right:0,bottom:0,left:0,...o.style}}))});function Ln(e){return de(e.target)&&e.target.tagName==="BUTTON"}function An(e){return Ar(e)}function Zo(e,t){t===void 0&&(t={});const{open:r,onOpenChange:n,dataRef:o,elements:{domReference:a}}=e,{enabled:s=!0,event:i="click",toggle:c=!0,ignoreMouse:l=!1,keyboardHandlers:b=!0}=t,u=f.useRef(),m=f.useRef(!1),g=f.useMemo(()=>({onPointerDown(p){u.current=p.pointerType},onMouseDown(p){const h=u.current;p.button===0&&i!=="click"&&(vt(h,!0)&&l||(r&&c&&(!o.current.openEvent||o.current.openEvent.type==="mousedown")?n(!1,p.nativeEvent,"click"):(p.preventDefault(),n(!0,p.nativeEvent,"click"))))},onClick(p){const h=u.current;if(i==="mousedown"&&u.current){u.current=void 0;return}vt(h,!0)&&l||(r&&c&&(!o.current.openEvent||o.current.openEvent.type==="click")?n(!1,p.nativeEvent,"click"):n(!0,p.nativeEvent,"click"))},onKeyDown(p){u.current=void 0,!(p.defaultPrevented||!b||Ln(p))&&(p.key===" "&&!An(a)&&(p.preventDefault(),m.current=!0),p.key==="Enter"&&n(!(r&&c),p.nativeEvent,"click"))},onKeyUp(p){p.defaultPrevented||!b||Ln(p)||An(a)||p.key===" "&&m.current&&(m.current=!1,n(!(r&&c),p.nativeEvent,"click"))}}),[o,a,i,l,b,n,r,c]);return f.useMemo(()=>s?{reference:g}:{},[s,g])}const zd={pointerdown:"onPointerDown",mousedown:"onMouseDown",click:"onClick"},Bd={pointerdown:"onPointerDownCapture",mousedown:"onMouseDownCapture",click:"onClickCapture"},Fn=e=>{var t,r;return{escapeKey:typeof e=="boolean"?e:(t=e==null?void 0:e.escapeKey)!=null?t:!1,outsidePress:typeof e=="boolean"?e:(r=e==null?void 0:e.outsidePress)!=null?r:!0}};function Qo(e,t){t===void 0&&(t={});const{open:r,onOpenChange:n,elements:o,dataRef:a}=e,{enabled:s=!0,escapeKey:i=!0,outsidePress:c=!0,outsidePressEvent:l="pointerdown",referencePress:b=!1,referencePressEvent:u="pointerdown",ancestorScroll:m=!1,bubbles:g,capture:p}=t,h=jt(),w=pe(typeof c=="function"?c:()=>!1),v=typeof c=="function"?w:c,C=f.useRef(!1),T=f.useRef(!1),{escapeKey:y,outsidePress:I}=Fn(g),{escapeKey:A,outsidePress:B}=Fn(p),_=pe(k=>{var M;if(!r||!s||!i||k.key!=="Escape")return;const R=(M=a.current.floatingContext)==null?void 0:M.nodeId,P=h?Xe(h.nodesRef.current,R):[];if(!y&&(k.stopPropagation(),P.length>0)){let D=!0;if(P.forEach(H=>{var E;if((E=H.context)!=null&&E.open&&!H.context.dataRef.current.__escapeKeyBubbles){D=!1;return}}),!D)return}n(!1,Jl(k)?k.nativeEvent:k,"escape-key")}),W=pe(k=>{var M;const R=()=>{var P;_(k),(P=Pe(k))==null||P.removeEventListener("keydown",R)};(M=Pe(k))==null||M.addEventListener("keydown",R)}),S=pe(k=>{var M;const R=C.current;C.current=!1;const P=T.current;if(T.current=!1,l==="click"&&P||R||typeof v=="function"&&!v(k))return;const D=Pe(k),H="["+Ve("inert")+"]",E=ge(o.floating).querySelectorAll(H);let Y=ee(D)?D:null;for(;Y&&!We(Y);){const F=De(Y);if(We(F)||!ee(F))break;Y=F}if(E.length&&ee(D)&&!Zl(D)&&!ce(D,o.floating)&&Array.from(E).every(F=>!ce(Y,F)))return;if(de(D)&&O){const F=D.clientWidth>0&&D.scrollWidth>D.clientWidth,V=D.clientHeight>0&&D.scrollHeight>D.clientHeight;let G=V&&k.offsetX>D.clientWidth;if(V&&ke(D).direction==="rtl"&&(G=k.offsetX<=D.offsetWidth-D.clientWidth),G||F&&k.offsetY>D.clientHeight)return}const ne=(M=a.current.floatingContext)==null?void 0:M.nodeId,me=h&&Xe(h.nodesRef.current,ne).some(F=>{var V;return ur(k,(V=F.context)==null?void 0:V.elements.floating)});if(ur(k,o.floating)||ur(k,o.domReference)||me)return;const J=h?Xe(h.nodesRef.current,ne):[];if(J.length>0){let F=!0;if(J.forEach(V=>{var G;if((G=V.context)!=null&&G.open&&!V.context.dataRef.current.__outsidePressBubbles){F=!1;return}}),!F)return}n(!1,k,"outside-press")}),L=pe(k=>{var M;const R=()=>{var P;S(k),(P=Pe(k))==null||P.removeEventListener(l,R)};(M=Pe(k))==null||M.addEventListener(l,R)});f.useEffect(()=>{if(!r||!s)return;a.current.__escapeKeyBubbles=y,a.current.__outsidePressBubbles=I;function k(P){n(!1,P,"ancestor-scroll")}const M=ge(o.floating);i&&M.addEventListener("keydown",A?W:_,A),v&&M.addEventListener(l,B?L:S,B);let R=[];return m&&(ee(o.domReference)&&(R=$e(o.domReference)),ee(o.floating)&&(R=R.concat($e(o.floating))),!ee(o.reference)&&o.reference&&o.reference.contextElement&&(R=R.concat($e(o.reference.contextElement)))),R=R.filter(P=>{var D;return P!==((D=M.defaultView)==null?void 0:D.visualViewport)}),R.forEach(P=>{P.addEventListener("scroll",k,{passive:!0})}),()=>{i&&M.removeEventListener("keydown",A?W:_,A),v&&M.removeEventListener(l,B?L:S,B),R.forEach(P=>{P.removeEventListener("scroll",k)})}},[a,o,i,v,l,r,n,m,s,y,I,_,A,W,S,B,L]),f.useEffect(()=>{C.current=!1},[v,l]);const N=f.useMemo(()=>({onKeyDown:_,[zd[u]]:k=>{b&&n(!1,k.nativeEvent,"reference-press")}}),[_,n,b,u]),O=f.useMemo(()=>({onKeyDown:_,onMouseDown(){T.current=!0},onMouseUp(){T.current=!0},[Bd[l]]:()=>{C.current=!0}}),[_,l]);return f.useMemo(()=>s?{reference:N,floating:O}:{},[s,N,O])}function _d(e){const{open:t=!1,onOpenChange:r,elements:n}=e,o=nr(),a=f.useRef({}),[s]=f.useState(()=>wd()),i=or()!=null,[c,l]=f.useState(n.reference),b=pe((g,p,h)=>{a.current.openEvent=g?p:void 0,s.emit("openchange",{open:g,event:p,reason:h,nested:i}),r==null||r(g,p,h)}),u=f.useMemo(()=>({setPositionReference:l}),[]),m=f.useMemo(()=>({reference:c||n.reference||null,floating:n.floating||null,domReference:n.reference}),[c,n.reference,n.floating]);return f.useMemo(()=>({dataRef:a,open:t,onOpenChange:b,elements:m,events:s,floatingId:o,refs:u}),[t,b,m,s,o,u])}function ea(e){e===void 0&&(e={});const{nodeId:t}=e,r=_d({...e,elements:{reference:null,floating:null,...e.elements}}),n=e.rootContext||r,o=n.elements,[a,s]=f.useState(null),[i,c]=f.useState(null),b=(o==null?void 0:o.reference)||a,u=f.useRef(null),m=jt();Q(()=>{b&&(u.current=b)},[b]);const g=ed({...e,elements:{...o,...i&&{reference:i}}}),p=f.useCallback(T=>{const y=ee(T)?{getBoundingClientRect:()=>T.getBoundingClientRect(),contextElement:T}:T;c(y),g.refs.setReference(y)},[g.refs]),h=f.useCallback(T=>{(ee(T)||T===null)&&(u.current=T,s(T)),(ee(g.refs.reference.current)||g.refs.reference.current===null||T!==null&&!ee(T))&&g.refs.setReference(T)},[g.refs]),w=f.useMemo(()=>({...g.refs,setReference:h,setPositionReference:p,domReference:u}),[g.refs,h,p]),v=f.useMemo(()=>({...g.elements,domReference:b}),[g.elements,b]),C=f.useMemo(()=>({...g,...n,refs:w,elements:v,nodeId:t}),[g,w,v,t,n]);return Q(()=>{n.dataRef.current.floatingContext=C;const T=m==null?void 0:m.nodesRef.current.find(y=>y.id===t);T&&(T.context=C)}),f.useMemo(()=>({...g,context:C,refs:w,elements:v}),[g,w,v,C])}function Hd(e,t){t===void 0&&(t={});const{open:r,onOpenChange:n,events:o,dataRef:a,elements:s}=e,{enabled:i=!0,visibleOnly:c=!0}=t,l=f.useRef(!1),b=f.useRef(),u=f.useRef(!0);f.useEffect(()=>{if(!i)return;const g=we(s.domReference);function p(){!r&&de(s.domReference)&&s.domReference===Ne(ge(s.domReference))&&(l.current=!0)}function h(){u.current=!0}return g.addEventListener("blur",p),g.addEventListener("keydown",h,!0),()=>{g.removeEventListener("blur",p),g.removeEventListener("keydown",h,!0)}},[s.domReference,r,i]),f.useEffect(()=>{if(!i)return;function g(p){let{reason:h}=p;(h==="reference-press"||h==="escape-key")&&(l.current=!0)}return o.on("openchange",g),()=>{o.off("openchange",g)}},[o,i]),f.useEffect(()=>()=>{clearTimeout(b.current)},[]);const m=f.useMemo(()=>({onPointerDown(g){Dr(g.nativeEvent)||(u.current=!1)},onMouseLeave(){l.current=!1},onFocus(g){if(l.current)return;const p=Pe(g.nativeEvent);if(c&&ee(p))try{if(Lr()&&jo())throw Error();if(!p.matches(":focus-visible"))return}catch{if(!u.current&&!Ar(p))return}n(!0,g.nativeEvent,"focus")},onBlur(g){l.current=!1;const p=g.relatedTarget,h=g.nativeEvent,w=ee(p)&&p.hasAttribute(Ve("focus-guard"))&&p.getAttribute("data-type")==="outside";b.current=window.setTimeout(()=>{var v;const C=Ne(s.domReference?s.domReference.ownerDocument:document);!p&&C===s.domReference||ce((v=a.current.floatingContext)==null?void 0:v.refs.floating.current,C)||ce(s.domReference,C)||w||n(!1,h,"focus")})}}),[a,s.domReference,n,c]);return f.useMemo(()=>i?{reference:m}:{},[i,m])}const zn="active",Bn="selected";function xr(e,t,r){const n=new Map,o=r==="item";let a=e;if(o&&e){const{[zn]:s,[Bn]:i,...c}=e;a=c}return{...r==="floating"&&{tabIndex:-1},...a,...t.map(s=>{const i=s?s[r]:null;return typeof i=="function"?e?i(e):null:i}).concat(e).reduce((s,i)=>(i&&Object.entries(i).forEach(c=>{let[l,b]=c;if(!(o&&[zn,Bn].includes(l)))if(l.indexOf("on")===0){if(n.has(l)||n.set(l,[]),typeof b=="function"){var u;(u=n.get(l))==null||u.push(b),s[l]=function(){for(var m,g=arguments.length,p=new Array(g),h=0;h<g;h++)p[h]=arguments[h];return(m=n.get(l))==null?void 0:m.map(w=>w(...p)).find(w=>w!==void 0)}}}else s[l]=b}),s),{})}}function ta(e){e===void 0&&(e=[]);const t=e.map(i=>i==null?void 0:i.reference),r=e.map(i=>i==null?void 0:i.floating),n=e.map(i=>i==null?void 0:i.item),o=f.useCallback(i=>xr(i,e,"reference"),t),a=f.useCallback(i=>xr(i,e,"floating"),r),s=f.useCallback(i=>xr(i,e,"item"),n);return f.useMemo(()=>({getReferenceProps:o,getFloatingProps:a,getItemProps:s}),[o,a,s])}let _n=!1;function ar(e,t,r){switch(e){case"vertical":return t;case"horizontal":return r;default:return t||r}}function Hn(e,t){return ar(t,e===Wr||e===Rt,e===lt||e===gt)}function wr(e,t,r){return ar(t,e===Rt,r?e===lt:e===gt)||e==="Enter"||e===" "||e===""}function $d(e,t,r){return ar(t,r?e===lt:e===gt,e===Rt)}function $n(e,t,r){return ar(t,r?e===gt:e===lt,e===Wr)}function Wd(e,t){const{open:r,onOpenChange:n,elements:o}=e,{listRef:a,activeIndex:s,onNavigate:i=()=>{},enabled:c=!0,selectedIndex:l=null,allowEscape:b=!1,loop:u=!1,nested:m=!1,rtl:g=!1,virtual:p=!1,focusItemOnOpen:h="auto",focusItemOnHover:w=!0,openOnArrowKeyDown:v=!0,disabledIndices:C=void 0,orientation:T="vertical",cols:y=1,scrollItemIntoView:I=!0,virtualItemRef:A,itemSizes:B,dense:_=!1}=t,W=or(),S=jt(),L=pe(i),N=f.useRef(h),O=f.useRef(l??-1),k=f.useRef(null),M=f.useRef(!0),R=f.useRef(L),P=f.useRef(!!o.floating),D=f.useRef(r),H=f.useRef(!1),E=f.useRef(!1),Y=ve(C),ne=ve(r),me=ve(I),J=ve(o.floating),F=ve(l),[V,G]=f.useState(),[$,U]=f.useState(),Z=pe(function(q,re,K){K===void 0&&(K=!1);function oe(se){p?(G(se.id),S==null||S.events.emit("virtualfocus",se),A&&(A.current=se)):He(se,{preventScroll:!0,sync:jo()&&Lr()?_n||H.current:!1})}const xe=q.current[re.current];xe&&oe(xe),requestAnimationFrame(()=>{const se=q.current[re.current]||xe;if(!se)return;xe||oe(se);const je=me.current;je&&te&&(K||!M.current)&&(se.scrollIntoView==null||se.scrollIntoView(typeof je=="boolean"?{block:"nearest",inline:"nearest"}:je))})});Q(()=>{document.createElement("div").focus({get preventScroll(){return _n=!0,!1}})},[]),Q(()=>{c&&(r&&o.floating?N.current&&l!=null&&(E.current=!0,O.current=l,L(l)):P.current&&(O.current=-1,R.current(null)))},[c,r,o.floating,l,L]),Q(()=>{if(c&&r&&o.floating)if(s==null){if(H.current=!1,F.current!=null)return;if(P.current&&(O.current=-1,Z(a,O)),(!D.current||!P.current)&&N.current&&(k.current!=null||N.current===!0&&k.current==null)){let q=0;const re=()=>{a.current[0]==null?(q<2&&(q?requestAnimationFrame:queueMicrotask)(re),q++):(O.current=k.current==null||wr(k.current,T,g)||m?br(a,Y.current):Rn(a,Y.current),k.current=null,L(O.current))};re()}}else yt(a,s)||(O.current=s,Z(a,O,E.current),E.current=!1)},[c,r,o.floating,s,F,m,a,T,g,L,Z,Y]),Q(()=>{var q;if(!c||o.floating||!S||p||!P.current)return;const re=S.nodesRef.current,K=(q=re.find(se=>se.id===W))==null||(q=q.context)==null?void 0:q.elements.floating,oe=Ne(ge(o.floating)),xe=re.some(se=>se.context&&ce(se.context.elements.floating,oe));K&&!xe&&M.current&&K.focus({preventScroll:!0})},[c,o.floating,S,W,p]),Q(()=>{if(!c||!S||!p||W)return;function q(re){U(re.id),A&&(A.current=re)}return S.events.on("virtualfocus",q),()=>{S.events.off("virtualfocus",q)}},[c,S,p,W,A]),Q(()=>{R.current=L,P.current=!!o.floating}),Q(()=>{r||(k.current=null)},[r]),Q(()=>{D.current=r},[r]);const le=s!=null,te=f.useMemo(()=>{function q(K){if(!r)return;const oe=a.current.indexOf(K);oe!==-1&&L(oe)}return{onFocus(K){let{currentTarget:oe}=K;q(oe)},onClick:K=>{let{currentTarget:oe}=K;return oe.focus({preventScroll:!0})},...w&&{onMouseMove(K){let{currentTarget:oe}=K;q(oe)},onPointerLeave(K){let{pointerType:oe}=K;!M.current||oe==="touch"||(O.current=-1,Z(a,O),L(null),p||He(J.current,{preventScroll:!0}))}}}},[r,J,Z,w,a,L,p]),Re=pe(q=>{if(M.current=!1,H.current=!0,!ne.current&&q.currentTarget===J.current)return;if(m&&$n(q.key,T,g)){be(q),n(!1,q.nativeEvent,"list-navigation"),de(o.domReference)&&!p&&o.domReference.focus();return}const re=O.current,K=br(a,C),oe=Rn(a,C);if(q.key==="Home"&&(be(q),O.current=K,L(O.current)),q.key==="End"&&(be(q),O.current=oe,L(O.current)),y>1){const xe=B||Array.from({length:a.current.length},()=>({width:1,height:1})),se=dd(xe,y,_),je=se.findIndex(Te=>Te!=null&&!_t(a.current,Te,C)),It=se.reduce((Te,Ae,St)=>Ae!=null&&!_t(a.current,Ae,C)?St:Te,-1);if(O.current=se[cd({current:se.map(Te=>Te!=null?a.current[Te]:null)},{event:q,orientation:T,loop:u,cols:y,disabledIndices:fd([...C||a.current.map((Te,Ae)=>_t(a.current,Ae)?Ae:void 0),void 0],se),minIndex:je,maxIndex:It,prevIndex:ud(O.current>oe?K:O.current,xe,se,y,q.key===Rt?"bl":q.key===gt?"tr":"tl"),stopEvent:!0})],L(O.current),T==="both")return}if(Hn(q.key,T)){if(be(q),r&&!p&&Ne(q.currentTarget.ownerDocument)===q.currentTarget){O.current=wr(q.key,T,g)?K:oe,L(O.current);return}wr(q.key,T,g)?u?O.current=re>=oe?b&&re!==a.current.length?-1:K:he(a,{startingIndex:re,disabledIndices:C}):O.current=Math.min(oe,he(a,{startingIndex:re,disabledIndices:C})):u?O.current=re<=K?b&&re!==-1?a.current.length:oe:he(a,{startingIndex:re,decrement:!0,disabledIndices:C}):O.current=Math.max(K,he(a,{startingIndex:re,decrement:!0,disabledIndices:C})),yt(a,O.current)?L(null):L(O.current)}}),Oe=f.useMemo(()=>p&&r&&le&&{"aria-activedescendant":$||V},[p,r,le,$,V]),Ce=f.useMemo(()=>({"aria-orientation":T==="both"?void 0:T,...!Eo(o.domReference)&&Oe,onKeyDown:Re,onPointerMove(){M.current=!0}}),[Oe,Re,o.domReference,T]),Ze=f.useMemo(()=>{function q(K){h==="auto"&&Ro(K.nativeEvent)&&(N.current=!0)}function re(K){N.current=h,h==="auto"&&Dr(K.nativeEvent)&&(N.current=!0)}return{...Oe,onKeyDown(K){M.current=!1;const oe=K.key.indexOf("Arrow")===0,xe=$d(K.key,T,g),se=$n(K.key,T,g),je=Hn(K.key,T),It=(m?xe:je)||K.key==="Enter"||K.key.trim()==="";if(p&&r){const on=S==null?void 0:S.nodesRef.current.find(Ot=>Ot.parentId==null),Fe=S&&on?Nd(S.nodesRef.current,on.id):null;if(oe&&Fe&&A){const Ot=new KeyboardEvent("keydown",{key:K.key,bubbles:!0});if(xe||se){var Te,Ae;const Fs=((Te=Fe.context)==null?void 0:Te.elements.domReference)===K.currentTarget,an=se&&!Fs?(Ae=Fe.context)==null?void 0:Ae.elements.domReference:xe?a.current.find(sn=>(sn==null?void 0:sn.id)===V):null;an&&(be(K),an.dispatchEvent(Ot),U(void 0))}if(je&&Fe.context&&Fe.context.open&&Fe.parentId&&K.currentTarget!==Fe.context.elements.domReference){var St;be(K),(St=Fe.context.elements.domReference)==null||St.dispatchEvent(Ot);return}}return Re(K)}if(!(!r&&!v&&oe)){if(It&&(k.current=m&&je?null:K.key),m){xe&&(be(K),r?(O.current=br(a,Y.current),L(O.current)):n(!0,K.nativeEvent,"list-navigation"));return}je&&(l!=null&&(O.current=l),be(K),!r&&v?n(!0,K.nativeEvent,"list-navigation"):Re(K),r&&L(O.current))}},onFocus(){r&&!p&&L(null)},onPointerDown:re,onMouseDown:q,onClick:q}},[V,Oe,Re,Y,h,a,m,L,n,r,v,T,g,l,S,p,A]);return f.useMemo(()=>c?{reference:Ze,floating:Ce,item:te}:{},[c,Ze,Ce,te])}const Vd=new Map([["select","listbox"],["combobox","listbox"],["label",!1]]);function ra(e,t){var r;t===void 0&&(t={});const{open:n,floatingId:o}=e,{enabled:a=!0,role:s="dialog"}=t,i=(r=Vd.get(s))!=null?r:s,c=nr(),b=or()!=null,u=f.useMemo(()=>i==="tooltip"||s==="label"?{["aria-"+(s==="label"?"labelledby":"describedby")]:n?o:void 0}:{"aria-expanded":n?"true":"false","aria-haspopup":i==="alertdialog"?"dialog":i,"aria-controls":n?o:void 0,...i==="listbox"&&{role:"combobox"},...i==="menu"&&{id:c},...i==="menu"&&b&&{role:"menuitem"},...s==="select"&&{"aria-autocomplete":"none"},...s==="combobox"&&{"aria-autocomplete":"list"}},[i,o,b,n,c,s]),m=f.useMemo(()=>{const p={id:o,...i&&{role:i}};return i==="tooltip"||s==="label"?p:{...p,...i==="menu"&&{"aria-labelledby":c}}},[i,o,c,s]),g=f.useCallback(p=>{let{active:h,selected:w}=p;const v={role:"option",...h&&{id:o+"-option"}};switch(s){case"select":return{...v,"aria-selected":h&&w};case"combobox":return{...v,...h&&{"aria-selected":!0}}}return{}},[o,s]);return f.useMemo(()=>a?{reference:u,floating:m,item:g}:{},[a,u,m,g])}function Gd(e,t){var r;const{open:n,dataRef:o}=e,{listRef:a,activeIndex:s,onMatch:i,onTypingChange:c,enabled:l=!0,findMatch:b=null,resetMs:u=750,ignoreKeys:m=[],selectedIndex:g=null}=t,p=f.useRef(),h=f.useRef(""),w=f.useRef((r=g??s)!=null?r:-1),v=f.useRef(null),C=pe(i),T=pe(c),y=ve(b),I=ve(m);Q(()=>{n&&(clearTimeout(p.current),v.current=null,h.current="")},[n]),Q(()=>{if(n&&h.current===""){var S;w.current=(S=g??s)!=null?S:-1}},[n,g,s]);const A=pe(S=>{S?o.current.typing||(o.current.typing=S,T(S)):o.current.typing&&(o.current.typing=S,T(S))}),B=pe(S=>{function L(R,P,D){const H=y.current?y.current(P,D):P.find(E=>(E==null?void 0:E.toLocaleLowerCase().indexOf(D.toLocaleLowerCase()))===0);return H?R.indexOf(H):-1}const N=a.current;if(h.current.length>0&&h.current[0]!==" "&&(L(N,N,h.current)===-1?A(!1):S.key===" "&&be(S)),N==null||I.current.includes(S.key)||S.key.length!==1||S.ctrlKey||S.metaKey||S.altKey)return;n&&S.key!==" "&&(be(S),A(!0)),N.every(R=>{var P,D;return R?((P=R[0])==null?void 0:P.toLocaleLowerCase())!==((D=R[1])==null?void 0:D.toLocaleLowerCase()):!0})&&h.current===S.key&&(h.current="",w.current=v.current),h.current+=S.key,clearTimeout(p.current),p.current=setTimeout(()=>{h.current="",w.current=v.current,A(!1)},u);const k=w.current,M=L(N,[...N.slice((k||0)+1),...N.slice(0,(k||0)+1)],h.current);M!==-1?(C(M),v.current=M):S.key!==" "&&(h.current="",A(!1))}),_=f.useMemo(()=>({onKeyDown:B}),[B]),W=f.useMemo(()=>({onKeyDown:B,onKeyUp(S){S.key===" "&&A(!1)}}),[B,A]);return f.useMemo(()=>l?{reference:_,floating:W}:{},[l,_,W])}function Wn(e,t){const[r,n]=e;let o=!1;const a=t.length;for(let s=0,i=a-1;s<a;i=s++){const[c,l]=t[s]||[0,0],[b,u]=t[i]||[0,0];l>=n!=u>=n&&r<=(b-c)*(n-l)/(u-l)+c&&(o=!o)}return o}function Yd(e,t){return e[0]>=t.x&&e[0]<=t.x+t.width&&e[1]>=t.y&&e[1]<=t.y+t.height}function Kd(e){e===void 0&&(e={});const{buffer:t=.5,blockPointerEvents:r=!1,requireIntent:n=!0}=e;let o,a=!1,s=null,i=null,c=performance.now();function l(u,m){const g=performance.now(),p=g-c;if(s===null||i===null||p===0)return s=u,i=m,c=g,null;const h=u-s,w=m-i,C=Math.sqrt(h*h+w*w)/p;return s=u,i=m,c=g,C}const b=u=>{let{x:m,y:g,placement:p,elements:h,onClose:w,nodeId:v,tree:C}=u;return function(y){function I(){clearTimeout(o),w()}if(clearTimeout(o),!h.domReference||!h.floating||p==null||m==null||g==null)return;const{clientX:A,clientY:B}=y,_=[A,B],W=Pe(y),S=y.type==="mouseleave",L=ce(h.floating,W),N=ce(h.domReference,W),O=h.domReference.getBoundingClientRect(),k=h.floating.getBoundingClientRect(),M=p.split("-")[0],R=m>k.right-k.width/2,P=g>k.bottom-k.height/2,D=Yd(_,O),H=k.width>O.width,E=k.height>O.height,Y=(H?O:k).left,ne=(H?O:k).right,me=(E?O:k).top,J=(E?O:k).bottom;if(L&&(a=!0,!S))return;if(N&&(a=!1),N&&!S){a=!0;return}if(S&&ee(y.relatedTarget)&&ce(h.floating,y.relatedTarget)||C&&Xe(C.nodesRef.current,v).some(G=>{let{context:$}=G;return $==null?void 0:$.open}))return;if(M==="top"&&g>=O.bottom-1||M==="bottom"&&g<=O.top+1||M==="left"&&m>=O.right-1||M==="right"&&m<=O.left+1)return I();let F=[];switch(M){case"top":F=[[Y,O.top+1],[Y,k.bottom-1],[ne,k.bottom-1],[ne,O.top+1]];break;case"bottom":F=[[Y,k.top+1],[Y,O.bottom-1],[ne,O.bottom-1],[ne,k.top+1]];break;case"left":F=[[k.right-1,J],[k.right-1,me],[O.left+1,me],[O.left+1,J]];break;case"right":F=[[O.right-1,J],[O.right-1,me],[k.left+1,me],[k.left+1,J]];break}function V(G){let[$,U]=G;switch(M){case"top":{const Z=[H?$+t/2:R?$+t*4:$-t*4,U+t+1],le=[H?$-t/2:R?$+t*4:$-t*4,U+t+1],te=[[k.left,R||H?k.bottom-t:k.top],[k.right,R?H?k.bottom-t:k.top:k.bottom-t]];return[Z,le,...te]}case"bottom":{const Z=[H?$+t/2:R?$+t*4:$-t*4,U-t],le=[H?$-t/2:R?$+t*4:$-t*4,U-t],te=[[k.left,R||H?k.top+t:k.bottom],[k.right,R?H?k.top+t:k.bottom:k.top+t]];return[Z,le,...te]}case"left":{const Z=[$+t+1,E?U+t/2:P?U+t*4:U-t*4],le=[$+t+1,E?U-t/2:P?U+t*4:U-t*4];return[...[[P||E?k.right-t:k.left,k.top],[P?E?k.right-t:k.left:k.right-t,k.bottom]],Z,le]}case"right":{const Z=[$-t,E?U+t/2:P?U+t*4:U-t*4],le=[$-t,E?U-t/2:P?U+t*4:U-t*4],te=[[P||E?k.left+t:k.right,k.top],[P?E?k.left+t:k.right:k.left+t,k.bottom]];return[Z,le,...te]}}}if(!Wn([A,B],F)){if(a&&!D)return I();if(!S&&n){const G=l(y.clientX,y.clientY);if(G!==null&&G<.1)return I()}Wn([A,B],V([m,g]))?!a&&n&&(o=window.setTimeout(I,40)):I()}}};return b.__options={blockPointerEvents:r},b}const qd=({arrowRef:e,placement:t})=>{const r=[];return r.push(rd(8)),r.push(t==="auto"?ad():od()),r.push(nd({padding:8})),e!=null&&e.current&&r.push(sd({element:e.current})),r},Ud=({placement:e})=>e==="auto"?void 0:e,na=({placement:e})=>({top:"bottom",right:"left",bottom:"top",left:"right"})[e.split("-")[0]],Yr=({open:e,arrowRef:t,placement:r="top",setOpen:n})=>ea({placement:Ud({placement:r}),open:e,onOpenChange:n,whileElementsMounted:$o,middleware:qd({placement:r,arrowRef:t})}),Kr=({context:e,trigger:t,role:r="tooltip",interactions:n=[]})=>ta([Zo(e,{enabled:t==="click"}),Cd(e,{enabled:t==="hover",handleClose:Kd()}),Qo(e),ra(e,{role:r}),...n]),Xd=({animation:e="duration-300",arrow:t=!0,children:r,className:n,content:o,placement:a="top",style:s="dark",theme:i,trigger:c="hover",minWidth:l,...b})=>{const u=f.useRef(null),[m,g]=f.useState(!1),p=Yr({open:m,placement:a,arrowRef:u,setOpen:g}),{context:h,middlewareData:{arrow:{x:w,y:v}={}},refs:C,strategy:T,update:y,x:I,y:A}=p,B=Hd(h),{getFloatingProps:_,getReferenceProps:W}=Kr({context:h,role:"tooltip",trigger:c,interactions:[B]});return f.useEffect(()=>{if(C.reference.current&&C.floating.current&&m)return $o(C.reference.current,C.floating.current,y)},[m,C.floating,C.reference,y]),d.jsxs(d.Fragment,{children:[d.jsx("div",{ref:C.setReference,className:i.target,"data-testid":"flowbite-tooltip-target",...W(),children:r}),d.jsxs("div",{ref:C.setFloating,"data-testid":"flowbite-tooltip",..._({className:x(i.base,e&&`${i.animation} ${e}`,!m&&i.hidden,i.style[s],n),style:{position:T,top:A??" ",left:I??" ",minWidth:l},...b}),children:[d.jsx("div",{className:i.content,children:o}),t&&d.jsx("div",{className:x(i.arrow.base,s==="dark"&&i.arrow.style.dark,s==="light"&&i.arrow.style.light,s==="auto"&&i.arrow.style.auto),"data-testid":"flowbite-tooltip-arrow",ref:u,style:{top:v??" ",left:w??" ",right:" ",bottom:" ",[na({placement:p.placement})]:i.arrow.placement},children:" "})]})]})},sr=({animation:e="duration-300",arrow:t=!0,children:r,className:n,content:o,placement:a="top",style:s="dark",theme:i={},trigger:c="hover",...l})=>{const b=j(z().tooltip,i);return d.jsx(Xd,{animation:e,arrow:t,content:o,placement:a,style:s,theme:b,trigger:c,className:n,...l,children:r})};sr.displayName="Tooltip";function oa(e){return fe({attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M438.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L160 338.7 393.4 105.4c12.5-12.5 32.8-12.5 45.3 0z"},child:[]}]})(e)}function aa(e){return fe({attr:{viewBox:"0 0 384 512"},child:[{tag:"path",attr:{d:"M192 0c-41.8 0-77.4 26.7-90.5 64H64C28.7 64 0 92.7 0 128V448c0 35.3 28.7 64 64 64H320c35.3 0 64-28.7 64-64V128c0-35.3-28.7-64-64-64H282.5C269.4 26.7 233.8 0 192 0zm0 64a32 32 0 1 1 0 64 32 32 0 1 1 0-64zM72 272a24 24 0 1 1 48 0 24 24 0 1 1 -48 0zm104-16H304c8.8 0 16 7.2 16 16s-7.2 16-16 16H176c-8.8 0-16-7.2-16-16s7.2-16 16-16zM72 368a24 24 0 1 1 48 0 24 24 0 1 1 -48 0zm88 0c0-8.8 7.2-16 16-16H304c8.8 0 16 7.2 16 16s-7.2 16-16 16H176c-8.8 0-16-7.2-16-16z"},child:[]}]})(e)}function Jd(e){return fe({attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M448 296c0 66.3-53.7 120-120 120h-8c-17.7 0-32-14.3-32-32s14.3-32 32-32h8c30.9 0 56-25.1 56-56v-8H320c-35.3 0-64-28.7-64-64V160c0-35.3 28.7-64 64-64h64c35.3 0 64 28.7 64 64v32 32 72zm-256 0c0 66.3-53.7 120-120 120H64c-17.7 0-32-14.3-32-32s14.3-32 32-32h8c30.9 0 56-25.1 56-56v-8H64c-35.3 0-64-28.7-64-64V160c0-35.3 28.7-64 64-64h64c35.3 0 64 28.7 64 64v32 32 72z"},child:[]}]})(e)}const qr=(e,t)=>{var r;t(!0),(r=navigator==null?void 0:navigator.clipboard)==null||r.writeText(e).then(()=>{console.log("Copy Successfull")}).catch(n=>{console.error("Failed to Copy text: ",n),t(!1)}),setTimeout(()=>t(!1),4e3)},sa=f.forwardRef(({valueToCopy:e,icon:t=aa,theme:r={},className:n,...o},a)=>{const[s,i]=f.useState(!1),c=j(z().clipboard.withIcon,r);return d.jsx("button",{className:x(c.base,n),onClick:()=>qr(e,i),...o,ref:a,children:s?d.jsx(oa,{"aria-hidden":!0,className:c.icon.successIcon}):d.jsx(t,{"aria-hidden":!0,className:c.icon.defaultIcon})})}),ia=f.forwardRef(({valueToCopy:e,icon:t=aa,label:r="Copy",theme:n={},className:o,...a},s)=>{const[i,c]=f.useState(!1),l=j(z().clipboard.withIconText,n);return d.jsx("button",{className:x(l.base,o),onClick:()=>qr(e,c),...a,ref:s,children:i?d.jsxs("span",{className:l.label.base,children:[d.jsx(oa,{"aria-hidden":!0,className:l.icon.successIcon}),d.jsx("span",{className:l.label.successText,children:"Copied"})]}):d.jsxs("span",{className:l.label.base,children:[d.jsx(t,{"aria-hidden":!0,className:l.icon.defaultIcon}),d.jsx("span",{className:l.label.defaultText,children:r})]})})}),la=f.forwardRef(({className:e,valueToCopy:t,label:r,theme:n={},...o},a)=>{const[s,i]=f.useState(!1),c=j(z().clipboard.button,n);return d.jsx(sr,{content:s?"Copied":"Copy to clipboard",className:"[&_*]:cursor-pointer",children:d.jsx("button",{className:x(c.base,e),onClick:()=>qr(t,i),...o,ref:a,children:d.jsx("span",{className:c.label,children:r})})})});la.displayName="Clipboard";sa.displayName="Clipboard.WithIcon";ia.displayName="Clipboard.WithIconText";Object.assign(la,{WithIcon:sa,WithIconText:ia});const Et=({children:e,className:t,color:r="default",theme:n={},value:o,...a})=>{const s=j(z().helperText,n);return d.jsx("p",{className:x(s.root.base,s.root.colors[r],t),...a,children:o??e??""})};Et.displayName="HelperText";const ca=f.forwardRef(({addon:e,className:t,color:r="gray",helperText:n,icon:o,rightIcon:a,shadow:s,sizing:i="md",theme:c={},type:l="text",...b},u)=>{const m=j(z().textInput,c);return d.jsxs(d.Fragment,{children:[d.jsxs("div",{className:x(m.base,t),children:[e&&d.jsx("span",{className:m.addon,children:e}),d.jsxs("div",{className:m.field.base,children:[o&&d.jsx("div",{className:m.field.icon.base,children:d.jsx(o,{className:m.field.icon.svg})}),a&&d.jsx("div",{"data-testid":"right-icon",className:m.field.rightIcon.base,children:d.jsx(a,{className:m.field.rightIcon.svg})}),d.jsx("input",{className:x(m.field.input.base,m.field.input.colors[r],m.field.input.sizes[i],m.field.input.withIcon[o?"on":"off"],m.field.input.withRightIcon[a?"on":"off"],m.field.input.withAddon[e?"on":"off"],m.field.input.withShadow[s?"on":"off"]),type:l,...b,ref:u})]})]}),n&&d.jsx(Et,{color:r,children:n})]})});ca.displayName="TextInput";const da=f.createContext(void 0);function ir(){const e=f.useContext(da);if(!e)throw new Error("useDatePickerContext should be used within the DatePickerContext provider!");return e}var ie=(e=>(e[e.Days=0]="Days",e[e.Months=1]="Months",e[e.Years=2]="Years",e[e.Decades=3]="Decades",e))(ie||{}),ua=(e=>(e[e.Sunday=0]="Sunday",e[e.Monday=1]="Monday",e[e.Tuesday=2]="Tuesday",e[e.Wednesday=3]="Wednesday",e[e.Thursday=4]="Thursday",e[e.Friday=5]="Friday",e[e.Saturday=6]="Saturday",e))(ua||{});const dt=(e,t,r)=>{const n=new Date(e.getFullYear(),e.getMonth(),e.getDate()).getTime();if(t&&r){const o=new Date(t.getFullYear(),t.getMonth(),t.getDate()).getTime(),a=new Date(r.getFullYear(),r.getMonth(),r.getDate()).getTime();return n>=o&&n<=a}if(t){const o=new Date(t.getFullYear(),t.getMonth(),t.getDate()).getTime();return n>=o}if(r){const o=new Date(r.getFullYear(),r.getMonth(),r.getDate()).getTime();return n<=o}return!0},Tt=(e,t)=>(e=new Date(e.getFullYear(),e.getMonth(),e.getDate()),t=new Date(t.getFullYear(),t.getMonth(),t.getDate()),e.getTime()===t.getTime()),Ft=(e,t,r)=>(dt(e,t,r)||(t&&e<t?e=t:r&&e>r&&(e=r)),e),Zd=(e,t)=>{const r=new Date(e.getFullYear(),e.getMonth(),1);let o=r.getDay()-t;return o<0&&(o+=7),Ur(r,-o)},Qd=(e,t)=>{const r=[],n=new Date(0);n.setDate(n.getDate()-n.getDay()+t);const o=new Intl.DateTimeFormat(e,{weekday:"short"});for(let a=0;a<7;a++)r.push(o.format(Ur(n,a)));return r},Ur=(e,t)=>{const r=new Date(e);return r.setDate(r.getDate()+t),r},eu=(e,t)=>{const r=new Date(e);return r.setMonth(r.getMonth()+t),r},tt=(e,t)=>{const r=new Date(e);return r.setFullYear(r.getFullYear()+t),r},rt=(e,t,r)=>{let n={day:"numeric",month:"long",year:"numeric"};return r&&(n=r),new Intl.DateTimeFormat(e,n).format(t)},nt=(e,t)=>{const r=e.getFullYear();return Math.floor(r/t)*t},tu=(e,t)=>{const r=e.getFullYear(),n=t+9;return r>=t&&r<=n},ru=({theme:e={}})=>{const{theme:t,weekStart:r,minDate:n,maxDate:o,viewDate:a,selectedDate:s,changeSelectedDate:i,language:c}=ir(),l=j(t.views.days,e),b=Qd(c,r),u=Zd(a,r);return d.jsxs(d.Fragment,{children:[d.jsx("div",{className:l.header.base,children:b.map((m,g)=>d.jsx("span",{className:l.header.title,children:m},g))}),d.jsx("div",{className:l.items.base,children:[...Array(42)].map((m,g)=>{const p=Ur(u,g),h=rt(c,p,{day:"numeric"}),w=s&&Tt(s,p),v=!dt(p,n,o);return d.jsx("button",{disabled:v,type:"button",className:x(l.items.item.base,w&&l.items.item.selected,v&&l.items.item.disabled),onClick:()=>{v||i(p,!0)},children:h},g)})})]})},nu=({theme:e={}})=>{const{theme:t,viewDate:r,selectedDate:n,minDate:o,maxDate:a,setViewDate:s,setView:i}=ir(),c=j(t.views.decades,e),l=nt(r,100);return d.jsx("div",{className:c.items.base,children:[...Array(12)].map((b,u)=>{const m=l-10+u*10;new Date(r.getTime()).setFullYear(m+r.getFullYear()%10);const p=new Date(m,0,1),h=tt(p,9),w=n&&tu(n,m),v=!dt(p,o,a)&&!dt(h,o,a);return d.jsx("button",{disabled:v,type:"button",className:x(c.items.item.base,w&&c.items.item.selected,v&&c.items.item.disabled),onClick:()=>{v||(n&&s(tt(r,m-n.getFullYear())),i(ie.Years))},children:m},u)})})},ou=({theme:e={}})=>{const{theme:t,minDate:r,maxDate:n,selectedDate:o,viewDate:a,language:s,setViewDate:i,setView:c}=ir(),l=j(t.views.months,e);return d.jsx("div",{className:l.items.base,children:[...Array(12)].map((b,u)=>{const m=new Date;m.setMonth(u,1),m.setFullYear(a.getFullYear());const g=rt(s,m,{month:"short"}),p=o&&Tt(o,m),h=!dt(m,r,n);return d.jsx("button",{disabled:h,type:"button",className:x(l.items.item.base,p&&l.items.item.selected,h&&l.items.item.disabled),onClick:()=>{h||(i(m),c(ie.Days))},children:g},u)})})},au=({theme:e={}})=>{const{theme:t,selectedDate:r,minDate:n,maxDate:o,viewDate:a,setViewDate:s,setView:i}=ir(),c=j(t.views.years,e);return d.jsx("div",{className:c.items.base,children:[...Array(12)].map((l,b)=>{const m=nt(a,10)+b,g=new Date(a.getTime());g.setFullYear(m);const p=r&&Tt(r,g),h=!dt(g,n,o);return d.jsx("button",{disabled:h,type:"button",className:x(c.items.item.base,p&&c.items.item.selected,h&&c.items.item.disabled),onClick:()=>{h||(s(g),i(ie.Months))},children:m},b)})})},su=({title:e,open:t,inline:r=!1,autoHide:n=!0,showClearButton:o=!0,labelClearButton:a="Clear",showTodayButton:s=!0,labelTodayButton:i="Today",defaultValue:c,minDate:l,maxDate:b,language:u="en",weekStart:m=ua.Sunday,className:g,theme:p={},onChange:h,label:w,value:v,...C},T)=>{const y=j(z().datepicker,p),I=c?Ft(c,l,b):null,A=f.useMemo(()=>c?Ft(c,l,b):new Date,[]),[B,_]=f.useState(t),[W,S]=f.useState(ie.Days),[L,N]=f.useState(v??I),[O,k]=f.useState(v??A),M=f.useRef(null),R=f.useRef(null),P=(J,F)=>{N(J),(J===null||J)&&h&&h(J),n&&W===ie.Days&&F==!0&&!r&&_(!1)},D=()=>{P(I,!0),c&&k(c)};f.useImperativeHandle(T,()=>({focus(){var J;(J=M.current)==null||J.focus()},clear(){D()}}));const H=J=>{switch(J){case ie.Decades:return d.jsx(nu,{theme:y.views.decades});case ie.Years:return d.jsx(au,{theme:y.views.years});case ie.Months:return d.jsx(ou,{theme:y.views.months});case ie.Days:default:return d.jsx(ru,{theme:y.views.days})}},E=()=>{switch(W){case ie.Days:return ie.Months;case ie.Months:return ie.Years;case ie.Years:return ie.Decades}return W},Y=()=>{switch(W){case ie.Decades:return`${nt(O,100)-10} - ${nt(O,100)+100}`;case ie.Years:return`${nt(O,10)} - ${nt(O,10)+11}`;case ie.Months:return rt(u,O,{year:"numeric"});case ie.Days:default:return rt(u,O,{month:"long",year:"numeric"})}},ne=(J,F,V)=>{switch(J){case ie.Days:return new Date(eu(F,V));case ie.Months:return new Date(tt(F,V));case ie.Years:return new Date(tt(F,V*10));case ie.Decades:return new Date(tt(F,V*100));default:return new Date(tt(F,V*10))}};f.useEffect(()=>{const J=F=>{var $,U;const V=($=R==null?void 0:R.current)==null?void 0:$.contains(F.target),G=(U=M==null?void 0:M.current)==null?void 0:U.contains(F.target);!V&&!G&&_(!1)};return document.addEventListener("mousedown",J),()=>{document.removeEventListener("mousedown",J)}},[M,R,_]),f.useEffect(()=>{const J=v&&Ft(new Date(v),l,b),F=L&&Ft(new Date(L),l,b);F&&J&&!Tt(J,F)&&N(J),L==null&&N(I)},[v,N,k,L]);const me=v===null?w:rt(u,L||new Date);return d.jsx(da.Provider,{value:{theme:y,language:u,minDate:l,maxDate:b,weekStart:m,isOpen:B,setIsOpen:_,view:W,setView:S,viewDate:O,setViewDate:k,selectedDate:L,setSelectedDate:N,changeSelectedDate:P},children:d.jsxs("div",{className:x(y.root.base,g),children:[!r&&d.jsx(ca,{theme:y.root.input,icon:oi,ref:M,onFocus:()=>{L&&!Tt(O,L)&&k(L),_(!0)},value:me,readOnly:!0,defaultValue:I?rt(u,I):w,...C}),(B||r)&&d.jsx("div",{ref:R,className:x(y.popup.root.base,r&&y.popup.root.inline),children:d.jsxs("div",{className:y.popup.root.inner,children:[d.jsxs("div",{className:y.popup.header.base,children:[e&&d.jsx("div",{className:y.popup.header.title,children:e}),d.jsxs("div",{className:y.popup.header.selectors.base,children:[d.jsx("button",{type:"button",className:x(y.popup.header.selectors.button.base,y.popup.header.selectors.button.prev),onClick:()=>k(ne(W,O,-1)),children:d.jsx(ri,{})}),d.jsx("button",{type:"button",className:x(y.popup.header.selectors.button.base,y.popup.header.selectors.button.view),onClick:()=>S(E()),children:Y()}),d.jsx("button",{type:"button",className:x(y.popup.header.selectors.button.base,y.popup.header.selectors.button.next),onClick:()=>k(ne(W,O,1)),children:d.jsx(ni,{})})]})]}),d.jsx("div",{className:y.popup.view.base,children:H(W)}),(o||s)&&d.jsxs("div",{className:y.popup.footer.base,children:[s&&d.jsx("button",{type:"button",className:x(y.popup.footer.button.base,y.popup.footer.button.today),onClick:()=>{const J=new Date;P(J,!0),k(J)},children:i}),o&&d.jsx("button",{type:"button",className:x(y.popup.footer.button.base,y.popup.footer.button.clear),onClick:()=>{P(null,!0)},children:a})]})]})})]})})},iu=f.forwardRef(su);iu.displayName="Datepicker";const fa=f.createContext(void 0);function ga(){const e=f.useContext(fa);if(!e)throw new Error("useDrawerContext should be used within the DrawerContext provider!");return e}function lu(e){return fe({attr:{viewBox:"0 0 24 24"},child:[{tag:"path",attr:{fill:"none",d:"M0 0h24v24H0z"},child:[]},{tag:"path",attr:{d:"M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"},child:[]}]})(e)}function cu(e){return fe({attr:{viewBox:"0 0 24 24"},child:[{tag:"path",attr:{fill:"none",d:"M0 0h24v24H0z"},child:[]},{tag:"path",attr:{d:"M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"},child:[]}]})(e)}const ba=({children:e,className:t,closeIcon:r=cu,theme:n={},title:o,titleIcon:a=lu,...s})=>{const i=f.useId(),{id:c,isOpen:l,onClose:b,theme:u}=ga(),m=j(u.header,n);return d.jsxs("div",{className:t,...s,children:[d.jsxs("h5",{className:m.inner.titleText,id:c,children:[d.jsx(a,{"aria-hidden":!0,className:m.inner.titleIcon}),o]}),d.jsxs("button",{onClick:b,"data-testid":"close-drawer",className:m.inner.closeButton,children:[d.jsx(r,{"aria-hidden":!0,className:m.inner.closeIcon}),d.jsx("span",{className:"sr-only",children:"Close menu"})]}),d.jsx("span",{className:m.collapsed[l?"on":"off"],id:`flowbite-drawer-header-${i}`,children:e})]})};ba.displayName="Drawer.Header";const pa=({children:e,className:t,theme:r={},...n})=>{const{theme:o}=ga(),a=j(o.items,r);return d.jsx("div",{"data-testid":"flowbite-drawer-items",className:x(a.base,t),...n,children:e})};pa.displayName="Drawer.Items";const ma=({backdrop:e=!0,children:t,className:r,edge:n=!1,position:o="left",onClose:a,open:s=!1,theme:i={},...c})=>{const l=f.useId(),b=j(z().drawer,i);return f.useEffect(()=>{const u=m=>{m.key==="Escape"&&s&&a&&a()};return document.addEventListener("keydown",u),()=>document.removeEventListener("keydown",u)},[a,s]),d.jsxs(fa.Provider,{value:{theme:b,onClose:a,isOpen:s,id:l},children:[d.jsx("div",{"aria-modal":!0,"aria-describedby":`drawer-dialog-${l}`,role:"dialog",tabIndex:-1,"data-testid":"flowbite-drawer",className:x(b.root.base,b.root.position[o][s?"on":"off"],n&&!s&&b.root.edge,r),...c,children:t}),s&&e&&d.jsx("div",{onClick:()=>a(),className:b.root.backdrop})]})};ma.displayName="Drawer";Object.assign(ma,{Header:ba,Items:pa});const ha=f.createContext(void 0);function Xr(){const e=f.useContext(ha);if(!e)throw new Error("useDropdownContext should be used within the DropdownContext provider!");return e}const Jr=({className:e,theme:t={},...r})=>{const{theme:n}=Xr(),o=t.divider??n.floating.divider;return d.jsx("div",{className:x(o,e),...r})},ya=({children:e,className:t,theme:r={},...n})=>{const{theme:o}=Xr(),a=r.header??o.floating.header;return d.jsxs(d.Fragment,{children:[d.jsx("div",{className:x(a,t),...n,children:e}),d.jsx(Jr,{})]})},xa=f.forwardRef(({children:e,className:t,icon:r,onClick:n,theme:o={},...a},s)=>{const{ref:i,index:c}=md({label:typeof e=="string"?e:void 0}),l=$r([s,i]),{theme:b,activeIndex:u,dismissOnClick:m,getItemProps:g,handleSelect:p}=Xr(),h=u===c,w=j(b.floating.item,o),v=a;return d.jsx("li",{role:"menuitem",className:w.container,children:d.jsxs(Ir,{ref:l,className:x(w.base,t),...v,...g({onClick:()=>{n==null||n(),m&&p(null)}}),tabIndex:h?0:-1,children:[r&&d.jsx(r,{className:w.icon}),e]})})});xa.displayName="DropdownItem";const du={top:di,right:Zn,bottom:Jn,left:ci},uu=({refs:e,children:t,inline:r,theme:n,disabled:o,setButtonWidth:a,getReferenceProps:s,renderTrigger:i,...c})=>{const l=e.reference,b=s();if(f.useEffect(()=>{l.current&&(a==null||a(l.current.clientWidth))},[l,a]),i){const u=i(n);return f.cloneElement(u,{ref:e.setReference,disabled:o,...b,...u.props})}return r?d.jsx("button",{type:"button",ref:e.setReference,className:n==null?void 0:n.inlineWrapper,disabled:o,...b,children:t}):d.jsx(Sr,{...c,disabled:o,type:"button",ref:e.setReference,...b,children:t})},wa=({children:e,className:t,dismissOnClick:r=!0,theme:n={},enableTypeAhead:o=!0,renderTrigger:a,...s})=>{const[i,c]=f.useState(!1),[l,b]=f.useState(null),[u,m]=f.useState(null),[g,p]=f.useState(void 0),h=f.useRef([]),w=f.useRef([]),v=j(z().dropdown,n),C=s,T=s["data-testid"]||"flowbite-dropdown-target",{placement:y=s.inline?"bottom-start":"bottom",trigger:I="click",label:A,inline:B,arrowIcon:_=!0,...W}=C,S=f.useCallback(Y=>{m(Y),c(!1)},[]),L=f.useCallback(Y=>{i?b(Y):S(Y)},[i,S]),{context:N,floatingStyles:O,refs:k}=Yr({open:i,setOpen:c,placement:y}),M=Wd(N,{listRef:h,activeIndex:l,selectedIndex:u,onNavigate:b}),R=Gd(N,{listRef:w,activeIndex:l,selectedIndex:u,onMatch:L,enabled:o}),{getReferenceProps:P,getFloatingProps:D,getItemProps:H}=Kr({context:N,role:"menu",trigger:I,interactions:[M,R]}),E=f.useMemo(()=>{const[Y]=y.split("-");return du[Y]??Jn},[y]);return d.jsxs(ha.Provider,{value:{theme:v,activeIndex:l,dismissOnClick:r,getItemProps:H,handleSelect:S},children:[d.jsxs(uu,{...W,refs:k,inline:B,theme:v,"data-testid":T,className:x(v.floating.target,W.className),setButtonWidth:p,getReferenceProps:P,renderTrigger:a,children:[A,_&&d.jsx(E,{className:v.arrowIcon})]}),i&&d.jsx(Gr,{context:N,modal:!1,children:d.jsx("div",{ref:k.setFloating,style:{...O,minWidth:g},"data-testid":"flowbite-dropdown","aria-expanded":i,...D({className:x(v.floating.base,v.floating.animation,"duration-100",!i&&v.floating.hidden,v.floating.style.auto,t)}),children:d.jsx(pd,{elementsRef:h,labelsRef:w,children:d.jsx("ul",{className:v.content,tabIndex:-1,children:e})})})})]})};wa.displayName="Dropdown";ya.displayName="Dropdown.Header";Jr.displayName="Dropdown.Divider";const fu=Object.assign(wa,{Item:xa,Header:ya,Divider:Jr}),gu=f.forwardRef(({className:e,color:t="gray",helperText:r,sizing:n="md",theme:o={},...a},s)=>{const i=j(z().fileInput,o);return d.jsxs(d.Fragment,{children:[d.jsx("div",{className:x(i.root.base,e),children:d.jsx("div",{className:i.field.base,children:d.jsx("input",{className:x(i.field.input.base,i.field.input.colors[t],i.field.input.sizes[n]),...a,type:"file",ref:s})})}),r&&d.jsx(Et,{color:t,children:r})]})});gu.displayName="FileInput";const bu=f.forwardRef(({label:e,helperText:t,color:r="default",sizing:n="md",variant:o,disabled:a=!1,theme:s={},className:i,...c},l)=>{const b=f.useId(),u=j(z().floatingLabel,s);return d.jsxs("div",{children:[d.jsxs("div",{className:x("relative",o==="standard"?"z-0":""),children:[d.jsx("input",{type:"text",id:c.id?c.id:"floatingLabel"+b,"aria-describedby":"outlined_success_help",className:x(u.input[r][o][n],i),placeholder:" ","data-testid":"floating-label",disabled:a,...c,ref:l}),d.jsx("label",{htmlFor:c.id?c.id:"floatingLabel"+b,className:x(u.label[r][o][n],i),children:e})]}),d.jsx("p",{id:"outlined_helper_text"+b,className:x(u.helperText[r],i),children:t})]})});bu.displayName="FloatingLabel";const va=({alt:e,className:t,children:r,href:n,name:o,src:a,theme:s={},...i})=>{const c=j(z().footer.brand,s);return d.jsx("div",{children:n?d.jsxs("a",{"data-testid":"flowbite-footer-brand",href:n,className:x(c.base,t),...i,children:[d.jsx("img",{alt:e,src:a,className:c.img}),d.jsx("span",{"data-testid":"flowbite-footer-brand-span",className:c.span,children:o}),r]}):d.jsx("img",{alt:e,"data-testid":"flowbite-footer-brand",src:a,className:x(c.img,t),...i})})},ka=({by:e,className:t,href:r,theme:n={},year:o,...a})=>{const s=j(z().footer.copyright,n);return d.jsxs("div",{"data-testid":"flowbite-footer-copyright",className:x(s.base,t),...a,children:["© ",o,r?d.jsx("a",{href:r,className:s.href,children:e}):d.jsx("span",{"data-testid":"flowbite-footer-copyright-span",className:s.span,children:e})]})},Ca=({className:e,theme:t={},...r})=>{const n=j(z().footer.divider,t);return d.jsx("hr",{"data-testid":"footer-divider",className:x(n.base,e),...r})},Ta=({ariaLabel:e,className:t,href:r,icon:n,theme:o={},...a})=>{const s=j(z().footer.icon,o);return d.jsx("div",{children:r?d.jsx("a",{"aria-label":e,"data-testid":"flowbite-footer-icon",href:r,className:x(s.base,t),...a,children:d.jsx(n,{className:s.size})}):d.jsx(n,{"data-testid":"flowbite-footer-icon",className:s.size,...a})})},Na=({as:e="a",children:t,className:r,href:n,theme:o={},...a})=>{const s=j(z().footer.groupLink.link,o);return d.jsx("li",{className:x(s.base,r),children:d.jsx(e,{href:n,className:s.href,...a,children:t})})},Ra=({children:e,className:t,col:r=!1,theme:n={},...o})=>{const a=j(z().footer.groupLink,n);return d.jsx("ul",{"data-testid":"footer-groupLink",className:x(a.base,r&&a.col,t),...o,children:e})},ja=({as:e="h2",className:t,theme:r={},title:n,...o})=>{const a=j(z().footer.title,r);return d.jsx(e,{"data-testid":"flowbite-footer-title",className:x(a.base,t),...o,children:n})},Ea=({bgDark:e=!1,children:t,className:r,container:n=!1,theme:o={},...a})=>{const s=j(z().footer,o);return d.jsx("footer",{"data-testid":"flowbite-footer",className:x(s.root.base,e&&s.root.bgDark,n&&s.root.container,r),...a,children:t})};Ea.displayName="Footer";ka.displayName="Footer.Copyright";Na.displayName="Footer.Link";va.displayName="Footer.Brand";Ra.displayName="Footer.LinkGroup";Ta.displayName="Footer.Icon";ja.displayName="Footer.Title";Ca.displayName="Footer.Divider";Object.assign(Ea,{Copyright:ka,Link:Na,LinkGroup:Ra,Brand:va,Icon:Ta,Title:ja,Divider:Ca});const Ia=f.forwardRef(({theme:e={},icon:t=Jd,className:r,...n},o)=>{const a=j(z().hr.icon,e);return d.jsxs("div",{className:a.base,children:[d.jsx("hr",{className:x(a.hrLine,r),role:"separator","data-testid":"flowbite-hr-icon",ref:o,...n}),d.jsx("div",{className:a.icon.base,children:d.jsx(t,{"aria-hidden":!0,className:a.icon.icon})})]})}),Sa=f.forwardRef(({theme:e={},className:t,...r},n)=>{const o=j(z().hr.square,e);return d.jsx("hr",{className:x(o.base,t),role:"separator","data-testid":"flowbite-hr-square",ref:n,...r})}),Oa=f.forwardRef(({theme:e={},text:t,className:r,...n},o)=>{const a=j(z().hr.text,e);return d.jsxs("div",{className:a.base,children:[d.jsx("hr",{className:x(a.hrLine,r),"data-testid":"flowbite-hr-text",role:"separator",ref:o,...n}),d.jsx("span",{className:a.text,children:t})]})}),Ma=f.forwardRef(({theme:e={},className:t,...r},n)=>{const o=j(z().hr.trimmed,e);return d.jsx("hr",{className:x(o.base,t),role:"separator","data-testid":"flowbite-hr-trimmed",ref:n,...r})}),Pa=f.forwardRef(({theme:e={},className:t,...r},n)=>{const o=j(z().hr.root,e);return d.jsx("hr",{className:x(o.base,t),role:"separator","data-testid":"flowbite-hr",ref:n,...r})});Pa.displayName="HR";Ma.displayName="HR.Trimmed";Ia.displayName="HR.Icon";Oa.displayName="HR.Text";Sa.displayName="HR.Square";Object.assign(Pa,{Trimmed:Ma,Icon:Ia,Text:Oa,Square:Sa});const pu=({children:e,className:t,color:r="default",disabled:n=!1,theme:o={},value:a,...s})=>{const i=j(z().label,o);return d.jsx("label",{className:x(i.root.base,i.root.colors[r],n&&i.root.disabled,t),"data-testid":"flowbite-label",...s,children:a??e??""})};pu.displayName="Label";const Da=({children:e,className:t,icon:r,theme:n={},...o})=>{const a=j(z().list.item,n);return d.jsxs("li",{className:x(a.withIcon[r?"on":"off"],t),...o,children:[r&&d.jsx(r,{className:x(a.icon)}),e]})},La=({children:e,className:t,unstyled:r,nested:n,ordered:o,horizontal:a,theme:s={},...i})=>{const c=j(z().list,s),l=o?"ol":"ul";return d.jsx(l,{className:x(c.root.base,c.root.ordered[o?"on":"off"],r&&c.root.unstyled,n&&c.root.nested,a&&c.root.horizontal,t),...i,children:e})};La.displayName="List";Da.displayName="List.Item";const Du=Object.assign(La,{Item:Da}),Aa=({active:e,children:t,className:r,href:n,icon:o,onClick:a,theme:s={},disabled:i,...c})=>{const l=j(z().listGroup.item,s),b=typeof n<"u",u=b?"a":"button";return d.jsx("li",{className:x(l.base,r),children:d.jsxs(u,{href:n,onClick:a,type:b?void 0:"button",disabled:i,className:x(l.link.active[e?"on":"off"],l.link.disabled[i?"on":"off"],l.link.base,l.link.href[b?"on":"off"]),...c,children:[o&&d.jsx(o,{"aria-hidden":!0,"data-testid":"flowbite-list-group-item-icon",className:l.link.icon}),t]})})},Fa=({children:e,className:t,theme:r={},...n})=>{const o=j(z().listGroup,r);return d.jsx("ul",{className:x(o.root.base,t),...n,children:e})};Fa.displayName="ListGroup";Aa.displayName="ListGroup.Item";Object.assign(Fa,{Item:Aa});const za=f.createContext(void 0);function lr(){const e=f.useContext(za);if(!e)throw new Error("useNavBarContext should be used within the NavbarContext provider!");return e}const Ba=({as:e="a",children:t,className:r,theme:n={},...o})=>{const{theme:a}=lr(),s=j(a.brand,n);return d.jsx(e,{className:x(s.base,r),...o,children:t})},_a=({children:e,className:t,theme:r={},...n})=>{const{theme:o,isOpen:a}=lr(),s=j(o.collapse,r);return d.jsx("div",{"data-testid":"flowbite-navbar-collapse",className:x(s.base,s.hidden[a?"off":"on"],t),...n,children:d.jsx("ul",{className:s.list,children:e})})},Ha=({active:e,as:t="a",disabled:r,children:n,className:o,theme:a={},onClick:s,...i})=>{const{theme:c,setIsOpen:l}=lr(),b=j(c.link,a),u=m=>{l(!1),s==null||s(m)};return d.jsx("li",{children:d.jsx(t,{className:x(b.base,e&&b.active.on,!e&&!r&&b.active.off,b.disabled[r?"on":"off"],o),onClick:u,...i,children:n})})};function mu(e){return fe({attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M16 132h416c8.837 0 16-7.163 16-16V76c0-8.837-7.163-16-16-16H16C7.163 60 0 67.163 0 76v40c0 8.837 7.163 16 16 16zm0 160h416c8.837 0 16-7.163 16-16v-40c0-8.837-7.163-16-16-16H16c-8.837 0-16 7.163-16 16v40c0 8.837 7.163 16 16 16zm0 160h416c8.837 0 16-7.163 16-16v-40c0-8.837-7.163-16-16-16H16c-8.837 0-16 7.163-16 16v40c0 8.837 7.163 16 16 16z"},child:[]}]})(e)}const $a=({barIcon:e=mu,className:t,theme:r={},...n})=>{const{theme:o,isOpen:a,setIsOpen:s}=lr(),i=j(o.toggle,r),c=()=>{s(!a)};return d.jsxs("button",{"data-testid":"flowbite-navbar-toggle",onClick:c,className:x(i.base,t),...n,children:[d.jsx("span",{className:"sr-only",children:"Open main menu"}),d.jsx(e,{"aria-hidden":!0,className:i.icon})]})},Wa=({border:e,children:t,className:r,fluid:n=!1,menuOpen:o,rounded:a,theme:s={},...i})=>{const[c,l]=f.useState(o),b=j(z().navbar,s);return d.jsx(za.Provider,{value:{theme:b,isOpen:c,setIsOpen:l},children:d.jsx("nav",{className:x(b.root.base,b.root.bordered[e?"on":"off"],b.root.rounded[a?"on":"off"],r),...i,children:d.jsx("div",{className:x(b.root.inner.base,b.root.inner.fluid[n?"on":"off"]),children:t})})})};Wa.displayName="Navbar";Ba.displayName="Navbar.Brand";_a.displayName="Navbar.Collapse";Ha.displayName="Navbar.Link";$a.displayName="Navbar.Toggle";const hu=Object.assign(Wa,{Brand:Ba,Collapse:_a,Link:Ha,Toggle:$a}),Va=({children:e,className:t,theme:r={},toggle:n,...o})=>{const[a,s]=f.useState(void 0),i=j(z().megaMenu.dropdown,r);if(n)return d.jsx(fu,{inline:!0,label:n,placement:"bottom",theme:i.toggle,className:x(i.base,t),children:e});const c=f.useId(),l=f.useRef(null);return f.useEffect(()=>{var u;s((u=function(){var g;const m=(g=l.current)==null?void 0:g.closest("nav");return m==null?void 0:m.querySelector('[aria-haspopup="menu"]')}())==null?void 0:u.id)},[]),d.jsx("div",{"aria-labelledby":a,id:c,ref:l,role:"menu",className:x(i.base,t),...o,children:e})};Va.displayName="MegaMenu.Dropdown";const Ga=({children:e,className:t,theme:r={},...n})=>{const o=f.useId(),a=f.useRef(null),[s,i]=f.useState(void 0),[c,l]=f.useState(void 0),b=j(z().megaMenu.dropdownToggle,r),u=function(){var p;const g=(p=a.current)==null?void 0:p.closest("nav");return g==null?void 0:g.querySelector('[role="menu"]')},m=function(){var g;(g=u())==null||g.classList.toggle("hidden"),l(!c)};return f.useEffect(()=>{const g=u(),p=g==null?void 0:g.classList.contains("hidden");i(g==null?void 0:g.id),l(!p)},[]),d.jsx("button",{"aria-controls":s,"aria-expanded":c,"aria-haspopup":"menu",id:o,onClick:m,ref:a,className:x(b.base,t),...n,children:e})};Ga.displayName="MegaMenu.DropdownToggle";const Ya=({children:e,theme:t={},...r})=>{const n=j(z().megaMenu,t);return d.jsx(hu,{fluid:!0,theme:n,...r,children:e})};Object.assign(Ya,{Dropdown:Va,DropdownToggle:Ga});Ya.displayName="MegaMenu";const Ka=f.createContext(void 0);function Zr(){const e=f.useContext(Ka);if(!e)throw new Error("useModalContext should be used within the ModalContext provider!");return e}const qa=({children:e,className:t,theme:r={},...n})=>{const{theme:o,popup:a}=Zr(),s=j(o.body,r);return d.jsx("div",{className:x(s.base,a&&[s.popup],t),...n,children:e})},Ua=({children:e,className:t,theme:r={},...n})=>{const{theme:o,popup:a}=Zr(),s=j(o.footer,r);return d.jsx("div",{className:x(s.base,!a&&s.popup,t),...n,children:e})},Xa=({as:e="h3",children:t,className:r,theme:n={},id:o,...a})=>{const s=f.useId(),i=o||s,{theme:c,popup:l,onClose:b,setHeaderId:u}=Zr(),m=j(c.header,n);return f.useLayoutEffect(()=>(u(i),()=>u(void 0)),[i,u]),d.jsxs("div",{className:x(m.base,l&&m.popup,r),...a,children:[d.jsx(e,{id:i,className:m.title,children:t}),d.jsx("button",{"aria-label":"Close",className:m.close.base,type:"button",onClick:b,children:d.jsx(ui,{"aria-hidden":!0,className:m.close.icon})})]})},Ja=f.forwardRef(({children:e,className:t,dismissible:r=!1,onClose:n,popup:o,position:a="center",root:s,show:i,size:c="2xl",theme:l={},initialFocus:b,...u},m)=>{const[g,p]=f.useState(void 0),h=j(z().modal,l),{context:w}=ea({open:i,onOpenChange:()=>n&&n()}),v=$r([w.refs.setFloating,m]),C=Zo(w),T=Qo(w,{outsidePressEvent:"mousedown",enabled:r}),y=ra(w),{getFloatingProps:I}=ta([C,T,y]);return i?d.jsx(Ka.Provider,{value:{theme:h,popup:o,onClose:n,setHeaderId:p},children:d.jsx(Dd,{root:s,children:d.jsx(Fd,{lockScroll:!0,"data-testid":"modal-overlay",className:x(h.root.base,h.root.positions[a],i?h.root.show.on:h.root.show.off,t),...u,children:d.jsx(Gr,{context:w,initialFocus:b,children:d.jsx("div",{ref:v,...I(u),"aria-labelledby":g,className:x(h.content.base,h.root.sizes[c]),children:d.jsx("div",{className:h.content.inner,children:e})})})})})}):null});Ja.displayName="Modal";Xa.displayName="Modal.Header";qa.displayName="Modal.Body";Ua.displayName="Modal.Footer";const Lu=Object.assign(Ja,{Header:Xa,Body:qa,Footer:Ua}),yu=(e,t)=>e>=t?[]:[...Array(t-e+1).keys()].map(r=>r+e),Qr=({active:e,children:t,className:r,onClick:n,theme:o={},...a})=>{const s=j(z().pagination,o);return d.jsx("button",{type:"button",className:x(e&&s.pages.selector.active,r),onClick:n,...a,children:t})};Qr.displayName="Pagination.Button";const jr=({children:e,className:t,onClick:r,theme:n={},disabled:o=!1,...a})=>{const s=j(z().pagination,n);return d.jsx("button",{type:"button",className:x(o&&s.pages.selector.disabled,t),disabled:o,onClick:r,...a,children:e})};jr.displayName="Pagination.Navigation";const Za=({className:e,currentPage:t,layout:r="pagination",nextLabel:n="Next",onPageChange:o,previousLabel:a="Previous",renderPaginationButton:s=u=>d.jsx(Qr,{...u}),showIcons:i=!1,theme:c={},totalPages:l,...b})=>{const u=j(z().pagination,c),m=Math.min(Math.max(r==="pagination"?t+2:t+4,5),l),g=Math.max(1,m-4),p=()=>{o(Math.min(t+1,l))},h=()=>{o(Math.max(t-1,1))};return d.jsxs("nav",{className:x(u.base,e),...b,children:[r==="table"&&d.jsxs("div",{className:u.layout.table.base,children:["Showing ",d.jsx("span",{className:u.layout.table.span,children:g})," to ",d.jsx("span",{className:u.layout.table.span,children:m})," of ",d.jsx("span",{className:u.layout.table.span,children:l})," Entries"]}),d.jsxs("ul",{className:u.pages.base,children:[d.jsx("li",{children:d.jsxs(jr,{className:x(u.pages.previous.base,i&&u.pages.showIcon),onClick:h,disabled:t===1,children:[i&&d.jsx(ai,{"aria-hidden":!0,className:u.pages.previous.icon}),a]})}),r==="pagination"&&yu(g,m).map(w=>d.jsx("li",{"aria-current":w===t?"page":void 0,children:s({className:x(u.pages.selector.base,t===w&&u.pages.selector.active),active:w===t,onClick:()=>o(w),children:w})},w)),d.jsx("li",{children:d.jsxs(jr,{className:x(u.pages.next.base,i&&u.pages.showIcon),onClick:p,disabled:t===l,children:[n,i&&d.jsx(si,{"aria-hidden":!0,className:u.pages.next.icon})]})})]})]})};Za.displayName="Pagination";Object.assign(Za,{Button:Qr});function Au({children:e,content:t,theme:r={},arrow:n=!0,trigger:o="click",initialOpen:a,open:s,onOpenChange:i,placement:c="bottom",...l}){const[b,u]=f.useState(!!a),m=f.useRef(null),g=j(z().popover,r),p=s??b,w=Yr({open:p,placement:c,arrowRef:m,setOpen:i??u}),{floatingStyles:v,context:C,placement:T,middlewareData:{arrow:{x:y,y:I}={}},refs:A}=w,{getFloatingProps:B,getReferenceProps:_}=Kr({context:C,role:"dialog",trigger:o}),W=e.ref,S=$r([C.refs.setReference,W]);if(!f.isValidElement(e))throw Error("Invalid target element");const L=f.useMemo(()=>f.cloneElement(e,_({ref:S,"data-testid":"flowbite-popover-target",...e==null?void 0:e.props})),[e,S,_]);return d.jsxs(d.Fragment,{children:[L,p&&d.jsx(Gr,{context:C,modal:!0,children:d.jsx("div",{className:g.base,ref:A.setFloating,"data-testid":"flowbite-popover",...l,style:v,...B(),children:d.jsxs("div",{className:"relative",children:[n&&d.jsx("div",{className:g.arrow.base,"data-testid":"flowbite-popover-arrow",ref:m,style:{top:I??" ",left:y??" ",right:" ",bottom:" ",[na({placement:T})]:g.arrow.placement},children:" "}),d.jsx("div",{className:g.content,children:t})]})})})]})}const xu=({className:e,color:t="cyan",labelProgress:r=!1,labelText:n=!1,progress:o,progressLabelPosition:a="inside",size:s="md",textLabel:i="progressbar",textLabelPosition:c="inside",theme:l={},...b})=>{const u=f.useId(),m=j(z().progress,l);return d.jsx(d.Fragment,{children:d.jsxs("div",{id:u,"aria-label":i,"aria-valuenow":o,role:"progressbar",...b,children:[(i&&n&&c==="outside"||o>0&&r&&a==="outside")&&d.jsxs("div",{className:m.label,"data-testid":"flowbite-progress-outer-label-container",children:[i&&n&&c==="outside"&&d.jsx("span",{"data-testid":"flowbite-progress-outer-text-label",children:i}),r&&a==="outside"&&d.jsxs("span",{"data-testid":"flowbite-progress-outer-progress-label",children:[o,"%"]})]}),d.jsx("div",{className:x(m.base,m.size[s],e),children:d.jsxs("div",{style:{width:`${o}%`},className:x(m.bar,m.color[t],m.size[s]),children:[i&&n&&c==="inside"&&d.jsx("span",{"data-testid":"flowbite-progress-inner-text-label",children:i}),o>0&&r&&a==="inside"&&d.jsxs("span",{"data-testid":"flowbite-progress-inner-progress-label",children:[o,"%"]})]})})]})})};xu.displayName="Progress";const wu=f.forwardRef(({className:e,theme:t={},...r},n)=>{const o=j(z().radio,t);return d.jsx("input",{ref:n,type:"radio",className:x(o.root.base,e),...r})});wu.displayName="Radio";const vu=f.forwardRef(({className:e,sizing:t="md",theme:r={},...n},o)=>{const a=j(z().rangeSlider,r);return d.jsx(d.Fragment,{children:d.jsx("div",{"data-testid":"flowbite-range-slider",className:x(a.root.base,e),children:d.jsx("div",{className:a.field.base,children:d.jsx("input",{ref:o,type:"range",className:x(a.field.input.base,a.field.input.sizes[t]),...n})})})})});vu.displayName="RangeSlider";const Qa=({children:e,className:t,percentFilled:r=0,theme:n={},...o})=>{const a=j(z().ratingAdvanced,n);return d.jsxs("div",{className:x(a.base,t),...o,children:[d.jsx("span",{className:a.label,children:e}),d.jsx("div",{className:a.progress.base,children:d.jsx("div",{className:a.progress.fill,"data-testid":"flowbite-rating-fill",style:{width:`${r}%`}})}),d.jsx("span",{className:a.progress.label,children:`${r}%`})]})},es=f.createContext(void 0);function ku(){const e=f.useContext(es);if(!e)throw new Error("useRatingContext should be used within the RatingContext provider!");return e}const ts=({className:e,filled:t=!0,starIcon:r=ii,theme:n={},...o})=>{const{theme:a,size:s="sm"}=ku(),i=j(a.star,n);return d.jsx(r,{"data-testid":"flowbite-rating-star",className:x(i.sizes[s],i[t?"filled":"empty"],e),...o})},rs=({children:e,className:t,size:r="sm",theme:n={},...o})=>{const a=j(z().rating,n);return d.jsx(es.Provider,{value:{theme:a,size:r},children:d.jsx("div",{className:x(a.root.base,t),...o,children:e})})};rs.displayName="Rating";ts.displayName="Rating.Star";Qa.displayName="Rating.Advanced";Object.assign(rs,{Star:ts,Advanced:Qa});const Cu=f.forwardRef(({addon:e,children:t,className:r,color:n="gray",helperText:o,icon:a,shadow:s,sizing:i="md",theme:c={},...l},b)=>{const u=j(z().select,c);return d.jsxs("div",{className:x(u.base,r),children:[e&&d.jsx("span",{className:u.addon,children:e}),d.jsxs("div",{className:u.field.base,children:[a&&d.jsx("div",{className:u.field.icon.base,children:d.jsx(a,{className:u.field.icon.svg})}),d.jsx("select",{className:x(u.field.select.base,u.field.select.colors[n],u.field.select.sizes[i],u.field.select.withIcon[a?"on":"off"],u.field.select.withAddon[e?"on":"off"],u.field.select.withShadow[s?"on":"off"]),...l,ref:b,children:t}),o&&d.jsx(Et,{color:n,children:o})]})]})});Cu.displayName="Select";const ns=f.createContext(void 0);function bt(){const e=f.useContext(ns);if(!e)throw new Error("useSidebarContext should be used within the SidebarContext provider!");return e}const en=f.createContext(void 0);function Tu(){const e=f.useContext(en);if(!e)throw new Error("useSidebarItemContext should be used within the SidebarItemContext provider!");return e}const os=({children:e,className:t,icon:r,label:n,chevronIcon:o=Xn,renderChevronIcon:a,open:s=!1,theme:i={},...c})=>{const l=f.useId(),[b,u]=f.useState(s),{theme:m,isCollapsed:g}=bt(),p=j(m.collapse,i);f.useEffect(()=>u(s),[s]);const h=({children:w})=>d.jsx("li",{children:g&&!b?d.jsx(sr,{content:n,placement:"right",children:w}):w});return d.jsxs(h,{children:[d.jsxs("button",{id:`flowbite-sidebar-collapse-${l}`,onClick:()=>u(!b),title:n,type:"button",className:x(p.button,t),...c,children:[r&&d.jsx(r,{"aria-hidden":!0,"data-testid":"flowbite-sidebar-collapse-icon",className:x(p.icon.base,p.icon.open[b?"on":"off"])}),g?d.jsx("span",{className:"sr-only",children:n}):d.jsxs(d.Fragment,{children:[d.jsx("span",{"data-testid":"flowbite-sidebar-collapse-label",className:p.label.base,children:n}),a?a(p,b):d.jsx(o,{"aria-hidden":!0,className:x(p.label.icon.base,p.label.icon.open[b?"on":"off"])})]})]}),d.jsx("ul",{"aria-labelledby":`flowbite-sidebar-collapse-${l}`,hidden:!b,className:p.list,children:d.jsx(en.Provider,{value:{isInsideCollapse:!0},children:e})})]})};os.displayName="Sidebar.Collapse";const as=({children:e,color:t="info",className:r,theme:n={},...o})=>{const{theme:a,isCollapsed:s}=bt(),i=j(a.cta,n);return d.jsx("div",{"data-testid":"sidebar-cta",hidden:s,className:x(i.base,i.color[t],r),...o,children:e})};as.displayName="Sidebar.CTA";const Nu=({id:e,theme:t,isCollapsed:r,tooltipChildren:n,children:o,...a})=>d.jsx("li",{...a,children:r?d.jsx(sr,{content:d.jsx(ss,{id:e,theme:t,children:n}),placement:"right",children:o}):o}),ss=({id:e,theme:t,children:r})=>d.jsx("span",{"data-testid":"flowbite-sidebar-item-content",id:`flowbite-sidebar-item-${e}`,className:x(t.content.base),children:r}),is=f.forwardRef(({active:e,as:t="a",children:r,className:n,icon:o,label:a,labelColor:s="info",theme:i={},...c},l)=>{var h,w,v,C;const b=f.useId(),{theme:u,isCollapsed:m}=bt(),{isInsideCollapse:g}=Tu(),p=j(u.item,i);return d.jsx(Nu,{theme:p,className:p.listItem,id:b,isCollapsed:m,tooltipChildren:r,children:d.jsxs(t,{"aria-labelledby":`flowbite-sidebar-item-${b}`,ref:l,className:x(p.base,e&&p.active,!m&&g&&((h=p.collapsed)==null?void 0:h.insideCollapse),n),...c,children:[o&&d.jsx(o,{"aria-hidden":!0,"data-testid":"flowbite-sidebar-item-icon",className:x((w=p.icon)==null?void 0:w.base,e&&((v=p.icon)==null?void 0:v.active))}),m&&!o&&d.jsx("span",{className:(C=p.collapsed)==null?void 0:C.noIcon,children:r.charAt(0).toLocaleUpperCase()??"?"}),!m&&d.jsx(ss,{id:b,theme:p,children:r}),!m&&a&&d.jsx(bo,{color:s,"data-testid":"flowbite-sidebar-label",hidden:m,className:p.label,children:a})]})})});is.displayName="Sidebar.Item";const ls=({children:e,className:t,theme:r={},...n})=>{const{theme:o}=bt(),a=j(o.itemGroup,r);return d.jsx("ul",{"data-testid":"flowbite-sidebar-item-group",className:x(a.base,t),...n,children:d.jsx(en.Provider,{value:{isInsideCollapse:!1},children:e})})};ls.displayName="Sidebar.ItemGroup";const cs=({children:e,className:t,theme:r={},...n})=>{const{theme:o}=bt(),a=j(o.items,r);return d.jsx("div",{className:x(a.base,t),"data-testid":"flowbite-sidebar-items",...n,children:e})};cs.displayName="Sidebar.Items";const ds=({children:e,className:t,href:r,img:n,imgAlt:o="",theme:a={},...s})=>{const i=f.useId(),{theme:c,isCollapsed:l}=bt(),b=j(c.logo,a);return d.jsxs("a",{"aria-labelledby":`flowbite-sidebar-logo-${i}`,href:r,className:x(b.base,t),...s,children:[d.jsx("img",{alt:o,src:n,className:b.img}),d.jsx("span",{className:b.collapsed[l?"on":"off"],id:`flowbite-sidebar-logo-${i}`,children:e})]})};ds.displayName="Sidebar.Logo";const us=({children:e,as:t="nav",collapseBehavior:r="collapse",collapsed:n=!1,theme:o={},className:a,...s})=>{const i=j(z().sidebar,o);return d.jsx(ns.Provider,{value:{theme:i,isCollapsed:n},children:d.jsx(t,{"aria-label":"Sidebar",hidden:n&&r==="hide",className:x(i.root.base,i.root.collapsed[n?"on":"off"],a),...s,children:d.jsx("div",{className:i.root.inner,children:e})})})};us.displayName="Sidebar";Object.assign(us,{Collapse:os,CTA:as,Item:is,Items:cs,ItemGroup:ls,Logo:ds});const fs=f.createContext(void 0);function Ru(){const e=f.useContext(fs);if(!e)throw new Error("useTableBodyContext should be used within the TableBodyContext provider!");return e}const gs=f.createContext(void 0);function tn(){const e=f.useContext(gs);if(!e)throw new Error("useTableContext should be used within the TableContext provider!");return e}const bs=f.forwardRef(({children:e,className:t,theme:r={},...n},o)=>{const{theme:a}=tn(),s=j(a.body,r);return d.jsx(fs.Provider,{value:{theme:s},children:d.jsx("tbody",{className:x(s.base,t),ref:o,...n,children:e})})});bs.displayName="Table.Body";const ps=f.forwardRef(({children:e,className:t,theme:r={},...n},o)=>{const{theme:a}=Ru(),s=j(a.cell,r);return d.jsx("td",{className:x(s.base,t),ref:o,...n,children:e})});ps.displayName="Table.Cell";const ms=f.createContext(void 0);function ju(){const e=f.useContext(ms);if(!e)throw new Error("useTableHeadContext should be used within the TableHeadContext provider!");return e}const hs=f.forwardRef(({children:e,className:t,theme:r={},...n},o)=>{const{theme:a}=tn(),s=j(a.head,r);return d.jsx(ms.Provider,{value:{theme:s},children:d.jsx("thead",{className:x(s.base,t),ref:o,...n,children:d.jsx("tr",{children:e})})})});hs.displayName="Table.Head";const ys=f.forwardRef(({children:e,className:t,theme:r={},...n},o)=>{const{theme:a}=ju(),s=j(a.cell,r);return d.jsx("th",{className:x(s.base,t),ref:o,...n,children:e})});ys.displayName="Table.HeadCell";const xs=f.forwardRef(({children:e,className:t,theme:r={},...n},o)=>{const{theme:a,hoverable:s,striped:i}=tn(),c=j(a.row,r);return d.jsx("tr",{ref:o,"data-testid":"table-row-element",className:x(c.base,i&&c.striped,s&&c.hovered,t),...n,children:e})});xs.displayName="Table.Row";const ws=f.forwardRef(({children:e,className:t,striped:r,hoverable:n,theme:o={},...a},s)=>{const i=j(z().table,o);return d.jsx("div",{"data-testid":"table-element",className:x(i.root.wrapper),children:d.jsxs(gs.Provider,{value:{theme:i,striped:r,hoverable:n},children:[d.jsx("div",{className:x(i.root.shadow,t)}),d.jsx("table",{className:x(i.root.base,t),...a,ref:s,children:e})]})})});ws.displayName="Table";Object.assign(ws,{Head:hs,Body:bs,Row:xs,Cell:ps,HeadCell:ys});const vs=({children:e,className:t})=>d.jsx("div",{className:t,children:e});vs.displayName="Tabs.Item";const ks=f.forwardRef(({children:e,className:t,onActiveTabChange:r,variant:n="default",theme:o={},...a},s)=>{const i=j(z().tabs,o),c=f.useId(),l=f.useMemo(()=>f.Children.map(f.Children.toArray(e),({props:y})=>y),[e]),b=f.useRef([]),[u,m]=f.useState(Math.max(0,l.findIndex(y=>y.active))),[g,p]=f.useState(-1),h=y=>{m(y),r&&r(y)},w=({target:y})=>{h(y),p(y)},v=({event:y,target:I})=>{y.key==="ArrowLeft"&&p(Math.max(0,g-1)),y.key==="ArrowRight"&&p(Math.min(l.length-1,g+1)),y.key==="Enter"&&(h(I),p(I))},C=i.tablist.tabitem.variant[n],T=i.tabitemcontainer.variant[n];return f.useEffect(()=>{var y;(y=b.current[g])==null||y.focus()},[g]),f.useImperativeHandle(s,()=>({setActiveTab:h})),d.jsxs("div",{className:x(i.base,t),children:[d.jsx("div",{"aria-label":"Tabs",role:"tablist",className:x(i.tablist.base,i.tablist.variant[n],t),...a,children:l.map((y,I)=>d.jsxs("button",{type:"button","aria-controls":`${c}-tabpanel-${I}`,"aria-selected":I===u,className:x(i.tablist.tabitem.base,C.base,I===u&&C.active.on,I!==u&&!y.disabled&&C.active.off),disabled:y.disabled,id:`${c}-tab-${I}`,onClick:()=>w({target:I}),onKeyDown:A=>v({event:A,target:I}),ref:A=>b.current[I]=A,role:"tab",tabIndex:I===g?0:-1,style:{zIndex:I===g?2:1},children:[y.icon&&d.jsx(y.icon,{className:i.tablist.tabitem.icon}),y.title]},I))}),d.jsx("div",{className:x(i.tabitemcontainer.base,T),children:l.map((y,I)=>d.jsx("div",{"aria-labelledby":`${c}-tab-${I}`,className:i.tabpanel,hidden:I!==u,id:`${c}-tabpanel-${I}`,role:"tabpanel",tabIndex:0,children:y.children},I))})]})});ks.displayName="Tabs";Object.assign(ks,{Item:vs});const Eu=f.forwardRef(({className:e,color:t="gray",helperText:r,shadow:n,theme:o={},...a},s)=>{const i=j(z().textarea,o);return d.jsxs(d.Fragment,{children:[d.jsx("textarea",{ref:s,className:x(i.base,i.colors[t],i.withShadow[n?"on":"off"],e),...a}),r&&d.jsx(Et,{color:t,children:r})]})});Eu.displayName="Textarea";const Cs=f.createContext(void 0);function rn(){const e=f.useContext(Cs);if(!e)throw new Error("useTimelineContentContext should be used within the TimelineContentContext provider!");return e}const Ts=({children:e,className:t,theme:r={},...n})=>{const{theme:o}=rn(),a=j(o.body,r);return d.jsx("div",{className:x(a.base,t),...n,children:e})},Ns=f.createContext(void 0);function nn(){const e=f.useContext(Ns);if(!e)throw new Error("useTimelineContext should be used within the TimelineContext provider!");return e}const Rs=f.createContext(void 0);function js(){const e=f.useContext(Rs);if(!e)throw new Error("useTimelineItemContext should be used within the TimelineItemContext provider!");return e}const Es=({children:e,className:t,theme:r={},...n})=>{const{horizontal:o}=nn(),{theme:a}=js(),s=j(a.content,r);return d.jsx(Cs.Provider,{value:{theme:s},children:d.jsx("div",{"data-testid":"timeline-content",className:x(s.root.base,o?s.root.horizontal:s.root.vertical,t),...n,children:e})})},Is=({children:e,className:t,theme:r={},...n})=>{const{theme:o,horizontal:a}=nn(),s=j(o.item,r);return d.jsx(Rs.Provider,{value:{theme:s},children:d.jsx("li",{"data-testid":"timeline-item",className:x(a&&s.root.horizontal,!a&&s.root.vertical,t),...n,children:e})})},Ss=({children:e,className:t,icon:r,theme:n={},...o})=>{const{horizontal:a}=nn(),{theme:s}=js(),i=j(s.point,n);return d.jsxs("div",{"data-testid":"timeline-point",className:x(a&&i.horizontal,!a&&i.vertical,t),...o,children:[e,r?d.jsx("span",{className:x(i.marker.icon.wrapper),children:d.jsx(r,{"aria-hidden":!0,className:x(i.marker.icon.base)})}):d.jsx("div",{className:x(a&&i.marker.base.horizontal,!a&&i.marker.base.vertical)}),a&&d.jsx("div",{className:x(i.line)})]})},Os=({children:e,className:t,theme:r={},...n})=>{const{theme:o}=rn(),a=j(o.time,r);return d.jsx("time",{className:x(a.base,t),...n,children:e})},Ms=({as:e="h3",children:t,className:r,theme:n={},...o})=>{const{theme:a}=rn(),s=j(a.title,n);return d.jsx(e,{className:x(s.base,r),...o,children:t})},Ps=({children:e,className:t,horizontal:r,theme:n={},...o})=>{const a=j(z().timeline,n);return d.jsx(Ns.Provider,{value:{theme:a,horizontal:r},children:d.jsx("ol",{"data-testid":"timeline-component",className:x(r&&a.root.direction.horizontal,!r&&a.root.direction.vertical,t),...o,children:e})})};Ps.displayName="Timeline";Is.displayName="Timeline.Item";Ss.displayName="Timeline.Point";Es.displayName="Timeline.Content";Os.displayName="Timeline.Time";Ms.displayName="Timeline.Title";Ts.displayName="Timeline.Body";Object.assign(Ps,{Item:Is,Point:Ss,Content:Es,Time:Os,Title:Ms,Body:Ts});const Ds=f.createContext(void 0);function Iu(){const e=f.useContext(Ds);if(!e)throw new Error("useToastContext should be used within the ToastContext provider!");return e}const Ls=({className:e,onClick:t,theme:r={},xIcon:n=li,onDismiss:o,...a})=>{const{theme:s,duration:i,isClosed:c,isRemoved:l,setIsClosed:b,setIsRemoved:u}=Iu(),m=j(s.toggle,r),g=p=>{if(t&&t(p),o){o();return}b(!c),setTimeout(()=>u(!l),i)};return d.jsx("button",{"aria-label":"Close",onClick:g,type:"button",className:x(m.base,e),...a,children:d.jsx(n,{"aria-hidden":!0,className:m.icon})})},Su={75:"duration-75",100:"duration-100",150:"duration-150",200:"duration-200",300:"duration-300",500:"duration-500",700:"duration-700",1e3:"duration-1000"},As=({children:e,className:t,duration:r=300,theme:n={},...o})=>{const[a,s]=f.useState(!1),[i,c]=f.useState(!1),l=j(z().toast,n);return i?null:d.jsx(Ds.Provider,{value:{theme:l,duration:r,isClosed:a,isRemoved:i,setIsClosed:s,setIsRemoved:c},children:d.jsx("div",{"data-testid":"flowbite-toast",role:"alert",className:x(l.root.base,Su[r],a&&l.root.closed,t),...o,children:e})})};As.displayName="Toast";Ls.displayName="Toast.Toggle";const Fu=Object.assign(As,{Toggle:Ls}),Ou=f.forwardRef(({checked:e,className:t,color:r="blue",sizing:n="md",disabled:o,label:a,name:s,onChange:i,theme:c={},...l},b)=>{const u=f.useId(),m=j(z().toggleSwitch,c),g=()=>i(!e),p=()=>{g()},h=w=>{w.code=="Enter"&&w.preventDefault()};return d.jsxs(d.Fragment,{children:[s&&e?d.jsx("input",{ref:b,checked:e,hidden:!0,name:s,readOnly:!0,type:"checkbox",className:"sr-only"}):null,d.jsxs("button",{"aria-checked":e,"aria-labelledby":`${u}-flowbite-toggleswitch-label`,disabled:o,id:`${u}-flowbite-toggleswitch`,onClick:p,onKeyDown:h,role:"switch",tabIndex:0,type:"button",className:x(m.root.base,m.root.active[o?"off":"on"],t),...l,children:[d.jsx("div",{"data-testid":"flowbite-toggleswitch-toggle",className:x(m.toggle.base,m.toggle.checked[e?"on":"off"],e&&m.toggle.checked.color[r],m.toggle.sizes[n])}),a!=null&&a.length?d.jsx("span",{"data-testid":"flowbite-toggleswitch-label",id:`${u}-flowbite-toggleswitch-label`,className:m.root.label,children:a}):null]})]})});Ou.displayName="ToggleSwitch";export{Sr as B,Kl as C,fu as D,Pu as G,Du as L,Lu as M,Au as P,Cu as S,sr as T,Fu as a,ca as b,pu as c,xu as d,po as e,zl as f,d as j};
