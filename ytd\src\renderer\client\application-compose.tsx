import { Tooltip } from 'flowbite-react';
import {
  cloneElement,
  CSSProperties,
  ReactElement,
  ReactNode,
  useMemo,
} from 'react';

const ACTIVE_COLOR = '#3F83F8';
const NORMAL_COLOR = '#6B7280';
const DEFAULT_SIZE = 40;

export function ApplicationSideItem(props: {
  icon: ReactElement;
  iconSize?: number;
  active?: boolean;
  text: string;
  onClick?: () => void;
}) {
  const { icon, iconSize, active, text, onClick } = props;
  const size = iconSize ? iconSize : DEFAULT_SIZE;
  const color = active ? ACTIVE_COLOR : NORMAL_COLOR;

  const modifiedIcon = useMemo(
    () =>
      cloneElement(icon, {
        className: 'fill-[var(--icon-color)] stroke-[var(--icon-color)]',
        width: `${size}px`,
        height: `${size}px`,
        style: {
          fontSize: size,
        },
      }),
    [icon, size],
  );

  return (
    <section
      className="flex flex-col items-center justify-center cursor-pointer p-4 gap-1"
      onClick={onClick}
      style={{ '--icon-color': color } as CSSProperties}
    >
      {modifiedIcon}

      <span className="text-center text-[var(--icon-color)]">{text}</span>
    </section>
  );
}

export function ApplicationRequireSpan(props: {
  required?: boolean;
  children: ReactNode;
}) {
  return (
    <span
      className={`after:content-["*"] after:text-[#E02424] after:ml-1 ${props.required ? '' : 'after:hidden'}`}
    >
      {props.children}
    </span>
  );
}

export function ApplicationIconWrap(props: {
  children: ReactNode;
  content: ReactNode;
}) {
  return (
    <Tooltip content={props.content}>
      <div className="p-1 hover:bg-[#f3f4f6] rounded cursor-pointer text-xl text-gray-500">
        {props.children}
      </div>
    </Tooltip>
  );
}
