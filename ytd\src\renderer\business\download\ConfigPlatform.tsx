import { Dropdown } from 'flowbite-react';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import {
  DownloadConfig,
  DownloadConfigShowCurrent,
} from '@/client/download-compose';
import { AUDIO_FORMATS, PLATFORMS, VIDEO_FORMATS } from '@/constants/media';
import { useSnapany } from '@/hooks/snapany';
import { useDownloadStore } from '@/store/download';

function DownloadConfigPlatform() {
  const { t } = useTranslation();

  const {
    platform,
    mediaType,
    videoFormat,
    changePlatform,
    changeVideoFormat,
    audioFormat,
    changeAudioFormat,
  } = useDownloadStore();

  const { updateDownloadConfig } = useSnapany();

  const isVideo = useMemo(() => mediaType === 'video', [mediaType]);

  const bindAfter = useCallback(
    (fn: () => void) => async () => {
      await fn();
      const latestDownloadConfig = useDownloadStore.getState();
      await updateDownloadConfig(latestDownloadConfig);
    },
    [updateDownloadConfig],
  );

  const renderVideoFormatList = () =>
    VIDEO_FORMATS.map(({ label, value }) => (
      <DownloadConfig
        key={value}
        label={label}
        showCheck={value === videoFormat}
        onClick={bindAfter(() => changeVideoFormat(value))}
      />
    ));

  const renderAudioFormatList = () =>
    AUDIO_FORMATS.map(({ label, value }) => (
      <DownloadConfig
        key={value}
        label={label}
        showCheck={value === audioFormat}
        onClick={bindAfter(() => changeAudioFormat(value))}
      />
    ));

  return (
    <Dropdown
      inline
      label={
        <DownloadConfigShowCurrent
          content={
            platform !== 'none'
              ? // 如果选择了系统，显示系统名称
                PLATFORMS.find((p) => p.value === platform)?.label
              : // 否则显示格式名称
                isVideo
                ? VIDEO_FORMATS.find((f) => f.value === videoFormat)?.label
                : AUDIO_FORMATS.find((f) => f.value === audioFormat)?.label
          }
          label={platform === 'none' ? t('download.format') : t('download.for')}
        />
      }
      theme={{
        arrowIcon: 'ml-1 text-gray-500',
      }}
    >
      {isVideo ? renderVideoFormatList() : renderAudioFormatList()}

      <Dropdown.Divider />

      {PLATFORMS.map(({ label, value }) => (
        <DownloadConfig
          label={label}
          key={value}
          showCheck={value === platform}
          onClick={bindAfter(() => changePlatform(value))}
        />
      ))}
    </Dropdown>
  );
}

export default DownloadConfigPlatform;
