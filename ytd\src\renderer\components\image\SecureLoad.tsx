import { Spinner } from 'flowbite-react';
import { useMemo } from 'react';
import useSWR from 'swr';

import SkeletonImageSvgIcon from '@/assets/skeleton-image.svg?react';
import { fetchImage } from '@/service/render';

interface ImageSecureLoadProps {
  url?: string;
  headers?: HeadersInit;
  loading?: boolean;
}

function ImageSecureLoad({ url, headers, loading }: ImageSecureLoadProps) {
  const { data: secureImage, isLoading } = useSWR(
    url && headers ? `fetchImage/${url}` : null,
    () => fetchImage(url!, headers),
    {},
  );

  const content = useMemo(() => {
    // 一个是图片加载，一个是外部受控加载
    if (isLoading || loading) {
      return (
        <Spinner
          size="xl"
          theme={{
            color: { info: 'fill-[#1C64F2]' },
            light: { off: { base: 'text-[#374151]' } },
          }}
        />
      );
    }

    if (secureImage?.dataBase64) {
      // data-url 暂不考虑加载过程
      return (
        <div
          className="w-full h-full bg-cover bg-no-repeat bg-center"
          style={{
            backgroundImage: `url(data:image/jpeg;base64,${secureImage.dataBase64})`,
          }}
        />
      );
    }

    return <SkeletonImageSvgIcon className="w-12 h-12 fill-[#bfbfbf]" />;
  }, [isLoading, loading, secureImage?.dataBase64]);

  return (
    <div className="shrink-0 w-[143px] h-[88px] bg-[#D9D9D9] flex items-center justify-center">
      {content}
    </div>
  );
}

export default ImageSecureLoad;
