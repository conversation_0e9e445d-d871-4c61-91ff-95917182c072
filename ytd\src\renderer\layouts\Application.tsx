import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { LuSettings } from 'react-icons/lu';
import { useRoutes } from 'react-router-dom';

import { ApplicationIconWrap } from '@/client/application-compose';
import SimpleModal from '@/components/modal/SimpleModal';
import { CONTACT_SOCIAL } from '@/constants/contact';
import LayoutItem from '@/layouts/LayoutItem';
import SettingLayout from '@/layouts/SettingLayout';
import routers, { applicationMenuRouter } from '@/routes/application';
import { openExternal } from '@/service/render';

function ApplicationLayout() {
  const { t } = useTranslation();

  const [showSettings, setShowSettings] = useState(false);

  const routerElements = useRoutes(routers);

  if (!routerElements) {
    return null;
  }

  return (
    <div className="flex h-screen w-full overflow-hidden">
      <aside className="flex flex-col shrink-0 bg-white gap-1 py-4">
        <nav>
          {applicationMenuRouter.map(
            (router) =>
              router.path &&
              router.key && (
                <LayoutItem
                  icon={router.icon}
                  to={router.path}
                  key={router.key}
                >
                  {t(router.key)}
                </LayoutItem>
              ),
          )}
        </nav>
        <footer className="gap-2 grow justify-center items-end flex flex-wrap px-4 content-end">
          {CONTACT_SOCIAL.map(({ key, icon: Icon, link }) => (
            <ApplicationIconWrap key={key} content={t(key)}>
              <Icon onClick={() => openExternal(link)} />
            </ApplicationIconWrap>
          ))}
          <ApplicationIconWrap content={t('menu.settings')}>
            <LuSettings onClick={() => setShowSettings(true)} />
          </ApplicationIconWrap>
        </footer>
      </aside>
      <main className="bg-[#F3F4F6] grow">{routerElements}</main>

      <SimpleModal open={showSettings} onClose={() => setShowSettings(false)}>
        <SettingLayout />
      </SimpleModal>
    </div>
  );
}

export default ApplicationLayout;
