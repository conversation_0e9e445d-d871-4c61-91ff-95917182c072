/* eslint-disable @typescript-eslint/no-explicit-any */
import { StoredDownloadTask } from '@common/types/download';
import * as Sentry from '@sentry/electron/renderer';
import { Button } from 'flowbite-react';
import React, { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { AiOutlineLink } from 'react-icons/ai';
import { VirtuosoHandle } from 'react-virtuoso';

import DownloadConfigDropdownGroup from '@/business/download/ConfigDropdownGroup';
import MultipleLinksModal from '@/business/download/MultipleLinksModal';
import TaskItem from '@/business/download/TaskItem';
import { DownloadTaskList } from '@/client/download-compose';
import { Notice } from '@/components/Notice';
import { useAuthSite } from '@/hooks/auth-site';
import { useSnapany } from '@/hooks/snapany';
import { useDownloadStore } from '@/store/download';
import { useTaskStore } from '@/store/task';
import debounce from '@/utils/debounce';
import { extractFirstValidUrl } from '@/utils/download';

// 主页组件
export const DownloadPage: React.FC = () => {
  const { t } = useTranslation();

  const [multipleLinks, setMultipleLinks] = useState<
    ReturnType<typeof extractFirstValidUrl>
  >([]);

  const {
    tasks,
    fetchTask,
    registerSync,
    deleteTask,
    openTaskFolder,
    retryTask,
    addTask,
  } = useTaskStore();

  const { loadSetting } = useDownloadStore();

  const { authSites, addCustomSite, loginSite } = useAuthSite();

  const { mutateSettings, settings } = useSnapany();

  const loadSettingSignalRef = useRef(false);
  const taskListContainerRef = useRef<VirtuosoHandle>(null);

  useEffect(() => {
    fetchTask();
    registerSync();
  }, [fetchTask, registerSync]);

  useEffect(() => {
    if (loadSettingSignalRef.current || !settings) {
      return;
    }

    loadSetting(settings);
    loadSettingSignalRef.current = true;
  }, [loadSetting, settings]);

  const handleDownload = async (url: string) => {
    if (!settings) {
      return;
    }

    await addTask(url);
    await mutateSettings();
  };

  // 添加批量下载处理函数
  const handleMultipleDownload = debounce(async (selectedLinks: string[]) => {
    setMultipleLinks([]);

    try {
      // 使用 for...of 循环按顺序处理链接
      for (const link of selectedLinks) {
        try {
          await handleDownload(link);
        } catch (error: any) {
          console.error(`处理链接失败: ${link}`, error);
          Notice.error(error.message);
        }
      }
    } catch (error: any) {
      Sentry.captureException('批量下载处理失败:' + error);
      Notice.error(error.message);
    }
  }, 300);

  const handleAuth = async (task: StoredDownloadTask) => {
    try {
      const url = new URL(task.url);

      const currentSite = authSites.find((x) => x.authUrl === url.origin);
      let site = currentSite;

      if (!currentSite) {
        const latestAuthSites = await addCustomSite(task.url);

        if (!latestAuthSites) {
          throw new Error();
        }

        site = latestAuthSites[latestAuthSites.length - 1];
      }

      await loginSite(site);

      Notice.success(t('messages.authSuccess'));

      retryTask(task);
    } catch {
      Notice.error(t('messages.authFailed'));
    }
  };

  // 粘贴处理函数
  const handlePaste = debounce(async () => {
    try {
      const text = await navigator.clipboard.readText();
      const links = extractFirstValidUrl(text);
      if (links.length > 0) {
        // 识别到剪切板里是否有多个链接，单个链接直接下载。多个链接，弹窗提示，用户选择确认后开始批量提取-下载
        if (links.length === 1) {
          await handleDownload(links[0].href);
        } else {
          // 取前10个链接
          const selectedLinks = links.slice(0, 10);
          setMultipleLinks(selectedLinks);
        }
        taskListContainerRef.current?.scrollToIndex({
          index: 0,
          align: 'start',
          behavior: 'smooth',
        });
      } else {
        Notice.error(t('errors.clipboardNotContainsValidUrl'));
      }
    } catch (err: any) {
      Sentry.captureException('读取剪贴板失败:' + err);
      Notice.error(err.message);
    }
  }, 300);

  return (
    <div className="flex flex-col w-full h-full ">
      <div className="h-[80px] bg-white flex items-center shrink-0 px-4">
        <section className="flex gap-2 items-center mr-4 ">
          <Button
            onClick={handlePaste}
            size="sm"
            color="blue"
            theme={{
              inner: {
                base: 'flex items-center justify-center',
              },
            }}
          >
            <AiOutlineLink className="mr-2" />
            <span className="text-[16px]"> {t('download.pasteLink')}</span>
          </Button>
        </section>

        <DownloadConfigDropdownGroup />
      </div>

      <article className="grow flex flex-col ">
        <DownloadTaskList
          ref={taskListContainerRef}
          data={tasks}
          render={(task) => (
            <TaskItem
              key={task.id}
              task={task}
              onDelete={deleteTask}
              onOpen={openTaskFolder}
              onRetry={retryTask}
              onAuth={handleAuth}
            />
          )}
        />
      </article>

      <MultipleLinksModal
        links={multipleLinks}
        open={multipleLinks.length > 0}
        onCancel={() => setMultipleLinks([])}
        onOk={handleMultipleDownload}
      />
    </div>
  );
};
export default DownloadPage;
