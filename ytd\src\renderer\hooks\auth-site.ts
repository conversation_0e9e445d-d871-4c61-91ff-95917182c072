import { AuthSite } from '@common/types/setting';
import { useLockFn } from 'ahooks';
import { useTranslation } from 'react-i18next';
import useSWR from 'swr';

import { Notice } from '@/components/Notice';
import {
  getSavedSites,
  openAuthWindow,
  removeAuth,
  saveSites,
} from '@/service/render';
import { validateCustomSite } from '@/utils/download';

export function useAuthSite() {
  const { t } = useTranslation();
  const { data: authSites = [], mutate: mutateAuthSites } = useSWR(
    'getSavedSites',
    getSavedSites,
  );

  const loginSite = useLockFn(async (authSite: AuthSite) => {
    const { authUrl, url } = authSite;
    const { success } = await openAuthWindow(authUrl, url);

    if (!success) {
      return;
    }

    const newSites = authSites.map((s) =>
      s.authUrl === authUrl ? { ...s, isAuthorized: true } : s,
    );
    saveSites(newSites);
    mutateAuthSites();
  });

  const logoutSite = useLockFn(async (authSite: AuthSite) => {
    const { authUrl } = authSite;
    try {
      await removeAuth(authUrl);
      const newSites = authSites.map((s) =>
        s.authUrl === authUrl ? { ...s, isAuthorized: true } : s,
      );
      saveSites(newSites);
      await mutateAuthSites();
      Notice.success(t('messages.removeAuthSuccess'));
    } catch {
      Notice.error(t('messages.removeAuthFailed'));
    }
  });

  const addCustomSite = useLockFn(async (url: string) => {
    try {
      const newAuthSites = await validateCustomSite(url, authSites);
      await saveSites(newAuthSites);
      return await mutateAuthSites();

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (e: any) {
      Notice.error(t(e.message ?? 'messages.defaultError'));
    }
  });

  const deleteSite = useLockFn(async (authSite: AuthSite) => {
    const newSites = authSites.filter((s) => s.authUrl !== authSite.authUrl);
    saveSites(newSites);
    mutateAuthSites();
  });

  return {
    authSites,
    mutateAuthSites,
    loginSite,
    logoutSite,
    addCustomSite,
    deleteSite,
  };
}
