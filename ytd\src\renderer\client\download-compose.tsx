import { Dropdown } from 'flowbite-react';
import { forwardRef, ReactNode, Ref } from 'react';
import { useTranslation } from 'react-i18next';
import { HiCheck } from 'react-icons/hi';
import { Virtuoso, VirtuosoHandle } from 'react-virtuoso';

import DownloadTaskEmptySvgIcon from '@/assets/download_empty.svg?react';

export function DownloadConfig(props: {
  showCheck: boolean;
  label: ReactNode;
  onClick?: () => void;
}) {
  return (
    <Dropdown.Item
      className="flex justify-between gap-4 text-nowrap"
      onClick={props.onClick}
    >
      <span>{props.label}</span>
      <HiCheck className={props.showCheck ? 'visible' : 'invisible'} />
    </Dropdown.Item>
  );
}

export function DownloadSubDropdown(props: {
  label?: string;
  children: ReactNode;
}) {
  const { label, children } = props;
  return (
    <Dropdown.Item as="div">
      <Dropdown
        dismissOnClick={false}
        inline
        trigger="hover"
        placement="right"
        label={label}
        theme={{
          inlineWrapper: 'grow flex justify-between items-center -mr-4 pr-4',
          content: 'max-h-[90vh] overflow-auto',
        }}
      >
        {children}
      </Dropdown>
    </Dropdown.Item>
  );
}

export function DownloadConfigShowCurrent(props: {
  label: ReactNode;
  content: ReactNode;
}) {
  return (
    <div className="flex gap-1 cursor-pointer text-sm">
      <span className="text-gray-500">{props.label}</span>
      <span>{props.content}</span>
    </div>
  );
}

export const DownloadTaskList = forwardRef(function DownloadTaskListWrap<T>(
  props: {
    data: T[];
    render: (data: T) => ReactNode;
  },
  ref?: Ref<VirtuosoHandle>,
) {
  const { t } = useTranslation();
  const { data, render } = props;

  if (data.length === 0) {
    return (
      <div className="flex flex-col justify-center items-center h-full">
        <DownloadTaskEmptySvgIcon className="scale-75" />
        <section className="text-[#6B7280]">
          <p>{t('download.emptyState.step1')}</p>
          <p>{t('download.emptyState.step2')}</p>
        </section>
      </div>
    );
  }

  return (
    <Virtuoso
      ref={ref}
      className="grow"
      totalCount={data.length}
      data={data}
      itemContent={(_, item) => render(item)}
      followOutput={'smooth'}
    />
  );
}) as <T>(
  props: { data: T[]; render: (data: T) => ReactNode } & {
    ref?: Ref<VirtuosoHandle>;
  },
) => JSX.Element;
