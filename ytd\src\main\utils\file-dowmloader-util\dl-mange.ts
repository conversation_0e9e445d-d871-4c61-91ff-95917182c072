// import { Downloader } from '../downloader';
import { DownloadProgress } from '@common/types/download';

import FileDownloader from './index';
import M3u8Downloader from './m3u8';

const m3u8Downloader = new M3u8Downloader();
const fileDownloader = new FileDownloader();

// 将 downloadStats 改造成一个工厂函数
const createDownloadStats = () => ({
  totalBytes: 0,
  normalDownloaded: new Map<string, number>(),
  m3u8Downloaded: new Map<string, number>(),
  normalSpeeds: new Map<string, number>(), // 记录普通文件的下载速度
  m3u8Speeds: new Map<string, number>(), // 记录m3u8文件的下载速度

  updateNormalProgress(url: string, downloadedBytes: number, speed: number) {
    this.normalDownloaded.set(url, downloadedBytes);
    this.normalSpeeds.set(url, speed); // 更新普通文件的速度
    this.printProgress();
  },

  updateM3u8Progress(url: string, downloadedBytes: number, speed: number) {
    this.m3u8Downloaded.set(url, downloadedBytes);
    this.m3u8Speeds.set(url, speed); // 更新m3u8文件的速度
    this.printProgress();
  },

  getTotalSpeed(): number {
    const normalSpeedsSum = Array.from(
      this.normalSpeeds.values(),
    ).reduce<number>((a: number, b: number) => a + b, 0);
    const m3u8SpeedsSum = Array.from(this.m3u8Speeds.values()).reduce<number>(
      (a: number, b: number) => a + b,
      0,
    );
    return normalSpeedsSum + m3u8SpeedsSum;
  },

  printProgress() {
    Array.from(this.normalDownloaded.values()).reduce<number>(
      (a: number, b: number) => a + b,
      0,
    );
    Array.from(this.m3u8Downloaded.values()).reduce<number>(
      (a: number, b: number) => a + b,
      0,
    );
  },
});

interface DownloadResult {
  url: string;
  filePath: string;
}

export async function downloadFile(
  urls: {
    url: string;
    headers?: Record<string, string>;
  }[],
  downloadPath: string,
  callback: {
    onProgress: (progress: DownloadProgress) => void;
    onComplete?: () => void;
    onError?: (error: Error) => void;
  },
  taskId?: string,
  controller?: AbortController,
): Promise<DownloadResult[]> {
  // 每次调用时创建新的 downloadStats 实例
  const downloadStats = createDownloadStats();

  // 获取总大小
  const sizePromises = urls.map((url) =>
    isM3u8Url(url.url)
      ? m3u8Downloader.getTotalM3u8Size(url.url, url.headers)
      : fileDownloader.getFileSize(url.url, url.headers),
  );

  try {
    const sizes = await Promise.all(sizePromises);
    downloadStats.totalBytes = sizes.reduce((acc, size) => acc + size, 0);
    console.log('文件总大小：', downloadStats.totalBytes / 1024 / 1024, 'MB');

    const normalUrls = urls.filter((url) => !isM3u8Url(url.url));
    const m3u8Urls = urls.filter((url) => isM3u8Url(url.url));

    // 如果没有提供controller，则创建一个新的
    if (!controller) {
      controller = new AbortController();
    }

    // 创建Promise来跟踪下载完成状态
    const downloadPromises: Promise<DownloadResult[]>[] = [];

    if (normalUrls.length > 0) {
      const normalDownloadPromise = new Promise<DownloadResult[]>(
        (resolve, reject) => {
          fileDownloader.downloadMultiple(
            normalUrls.map((url) => ({
              url: url.url,
              headers: url.headers,
            })),
            downloadPath,
            controller,
            {
              onProgress: async (progresses) => {
                progresses.forEach((progress) => {
                  downloadStats.updateNormalProgress(
                    progress.url,
                    progress.downloadedBytes,
                    progress.speedBytes,
                  );
                });
              },
              onComplete: async (results) => {
                console.log('\n[普通文件] 下载完成的文件:');
                results.forEach((result) => {
                  console.log(`${result.url} -> ${result.filePath}`);
                });
                resolve(results);
              },
              onError: async (errors) => {
                console.log('\n[普通文件] 下载失败的文件:');
                errors.forEach((error) => {
                  console.log(`${error.url}: ${error.error.message}`);
                });

                // 当普通文件下载失败时，取消所有其他下载
                if (controller) {
                  controller.abort();
                }

                const error = new Error('普通文件下载失败');
                if (callback.onError) {
                  callback.onError(error);
                }
                reject(error);
              },
            },
          );
        },
      );
      downloadPromises.push(normalDownloadPromise);
    }

    if (m3u8Urls.length > 0) {
      const m3u8DownloadPromise = new Promise<DownloadResult[]>(
        (resolve, reject) => {
          m3u8Downloader.downloadMultipleM3u8(
            m3u8Urls.map((url) => ({
              url: url.url,
              headers: url.headers,
            })),
            downloadPath,
            {
              onProgress: (progresses) => {
                progresses.forEach(({ url, progress }) => {
                  downloadStats.updateM3u8Progress(
                    url,
                    progress.downloadedBytes,
                    progress.speedBytes,
                  );
                });
              },
              onComplete: (results) => {
                console.log('\n[M3U8] 下载完成的文件:');
                results.forEach(({ url, filePath }) => {
                  console.log(`${url} -> ${filePath}`);
                });
                resolve(results);
              },
              onError: (url, error) => {
                console.error(`\n[M3U8] 下载失败 (${url}):`, error.message);

                // 当M3U8文件下载失败时，取消所有其他下载
                if (controller) {
                  controller.abort();
                }

                const errorObj = new Error(
                  `M3U8文件下载失败: ${error.message}`,
                );
                if (callback.onError) {
                  callback.onError(errorObj);
                }
                reject(errorObj);
              },
            },
          );
        },
      );
      downloadPromises.push(m3u8DownloadPromise);
    }

    // 重写 printProgress 方法，添加回调
    const originalPrintProgress = downloadStats.printProgress;
    downloadStats.printProgress = function () {
      originalPrintProgress.call(this);

      const normalTotal = Array.from(
        this.normalDownloaded.values(),
      ).reduce<number>((a: number, b: number) => a + b, 0);
      const m3u8Total = Array.from(this.m3u8Downloaded.values()).reduce<number>(
        (a: number, b: number) => a + b,
        0,
      );
      const totalDownloaded = normalTotal + m3u8Total;
      const totalSpeed = this.getTotalSpeed();

      // 调用回调函数，传递进度信息
      callback.onProgress({
        totalPercent: (totalDownloaded / this.totalBytes) * 100,
        downloadedBytes: totalDownloaded,
        totalBytes: this.totalBytes,
        speed: totalSpeed,
        normalDownloaded: normalTotal,
        m3u8Downloaded: m3u8Total,
      });
    };

    try {
      // 使用 Promise.allSettled 替代 Promise.all，确保能捕获所有错误
      const results = await Promise.all(downloadPromises);
      const allDownloadResults = results.flat();
      console.log('\n=== 所有文件下载完成 ===');

      // 调用完成回调
      if (callback.onComplete) {
        callback.onComplete();
      }

      return allDownloadResults;
    } catch (error) {
      // 如果有任何下载失败，取消所有下载
      if (controller && !controller.signal.aborted) {
        controller.abort();
      }

      // console.error('下载过程中发生错误:', error);

      // 调用错误回调
      if (callback.onError) {
        callback.onError(
          error instanceof Error ? error : new Error(String(error)),
        );
      }

      throw error;
    }
  } catch (error) {
    // console.error('下载过程中发生错误:', error);

    // 调用错误回调
    if (callback.onError) {
      callback.onError(
        error instanceof Error ? error : new Error(String(error)),
      );
    }

    throw error;
  }
}

function isM3u8Url(url: string): boolean {
  return url.toLowerCase().includes('.m3u8');
}
