import { useMount } from 'ahooks';
import { Toast } from 'flowbite-react';
import { ReactNode, useCallback, useEffect, useMemo, useState } from 'react';
import { createRoot } from 'react-dom/client';
import { HiBadgeCheck, HiBan } from 'react-icons/hi';

interface InnerProps {
  type: string;
  duration: number;
  message: ReactNode;
  isDark?: boolean;
  onClose: () => void;
}

const DEFAULT_ANIMATION_DURATION = 300;

function NoticeInner(props: InnerProps) {
  const { message, type, onClose } = props;
  const [visible, setVisible] = useState(false);

  const iconElement = useMemo(() => {
    if (type === 'error') {
      return (
        <div className="mr-3 inline-flex h-8 w-8 shrink-0 items-center justify-center rounded-lg bg-red-100 text-red-500 dark:bg-red-800 dark:text-red-200">
          <HiBan className="h-5 w-5" />
        </div>
      );
    }

    if (type === 'success') {
      return (
        <div className="mr-3 inline-flex h-8 w-8 shrink-0 items-center justify-center rounded-lg bg-green-100 text-green-500 dark:bg-green-800 dark:text-green-200">
          <HiBadgeCheck className="h-5 w-5" />
        </div>
      );
    }

    return null;
  }, [type]);

  const handleExit = useCallback(() => {
    setVisible(false);
    setTimeout(onClose, DEFAULT_ANIMATION_DURATION);
  }, [onClose]);

  useEffect(() => {
    const timer = setTimeout(handleExit, props.duration);
    return () => clearTimeout(timer);
  }, [handleExit, props.duration]);

  useMount(() => {
    requestAnimationFrame(() => {
      setVisible(true);
    });
  });

  return (
    <div
      className={`mt-4 mr-4 transition-all duration-${DEFAULT_ANIMATION_DURATION} ease-in-out
        ${visible ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-full'}`}
    >
      <Toast>
        {iconElement}
        <div className="text-sm font-normal">{message}</div>
        <Toast.Toggle onDismiss={handleExit} />
      </Toast>
    </div>
  );
}

interface NoticeInstance {
  (props: Omit<InnerProps, 'onClose'>): void;

  info(message: ReactNode, duration?: number, isDark?: boolean): void;
  error(message: ReactNode, duration?: number, isDark?: boolean): void;
  success(message: ReactNode, duration?: number, isDark?: boolean): void;
}

let parent: HTMLDivElement = null!;

export const Notice: NoticeInstance = (props) => {
  const { type, message, duration } = props;

  // 验证必要的参数
  if (!message) {
    return;
  }

  if (!parent) {
    parent = document.createElement('div');
    parent.setAttribute('id', 'notice-container'); // 添加 id 便于调试
    parent.className = 'flex flex-col fixed right-0 top-0 z-2001'; // z-index 2001 是因为模态框默认给了 2000
    document.body.appendChild(parent);
  }

  const container = document.createElement('div');
  parent.appendChild(container);

  const root = createRoot(container);

  const onUnmount = () => {
    root.unmount();

    if (parent && container.parentNode === parent) {
      setTimeout(() => {
        parent.removeChild(container);
      }, 500);
    }
  };

  root.render(
    <NoticeInner
      type={type}
      message={message}
      duration={duration ?? 1500}
      onClose={onUnmount}
    />,
  );
};

const createNoticeTypeFactory =
  (type: keyof NoticeInstance) => (message: ReactNode, duration?: number) => {
    // 确保消息不为空
    if (!message) {
      return;
    }
    Notice({
      type,
      message,
      // 错误类型通知显示 8 秒，其他类型默认 1.5 秒
      duration: type === 'error' ? 8000 : (duration ?? 1500),
    });
  };

Notice.info = createNoticeTypeFactory('info');
Notice.error = createNoticeTypeFactory('error');
Notice.success = createNoticeTypeFactory('success');
