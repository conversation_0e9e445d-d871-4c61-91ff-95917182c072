{"download": {"pasteLink": "<PERSON><PERSON><PERSON>", "pasteLinkFromClipboard": "<PERSON><PERSON><PERSON> da<PERSON>", "playlistChannel": "Daftar Putar/Saluran", "pastePlaylistChannelLink": "Tempel Tautan Daftar Putar/Saluran", "download": "<PERSON><PERSON><PERSON>", "resourceType": {"video": "Video", "audio": "Audio"}, "quality": "<PERSON><PERSON><PERSON>", "videoQuality": {"best": "Terbaik"}, "audioQuality": {"highest": "Tertinggi"}, "format": "Format", "for": "Untuk", "thumbnail": "Gambar Mini", "subtitles": "Subtitle", "audioTracks": "Trek Audio", "allTracks": "<PERSON><PERSON><PERSON>", "default": "<PERSON>ar", "none": "Tidak Ada", "modal": {"selectAll": "<PERSON><PERSON><PERSON>", "cancelAll": "Batalkan Semua", "cancel": "<PERSON><PERSON>", "download": "<PERSON><PERSON><PERSON>", "needDownloadToSelect": "<PERSON><PERSON>an pilih setidaknya satu item untuk diunduh"}, "emptyState": {"step1": "Langkah 1: <PERSON><PERSON> video", "step2": "Langkah 2: <PERSON><PERSON> untuk menempelkan tautan dan mengunduh"}, "popconfirm": {"downloadingDeleteTitle": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menghapus tugas ini?", "deleteText": "Hapus", "openFileFailed": "Gagal membuka file, apakah Anda ingin menghap<PERSON>?"}}, "taskStatus": {"retrievingInformation": "Mengambil informasi", "downloading": "Sedang mengunduh", "audioDownloading": "Sedang mengunduh audio", "videoDownloading": "Sedang mengunduh video", "subtitlesDownloading": "Sedang mengunduh subtitle", "converting": "Sedang <PERSON>versi", "merging": "Sedang menggabungkan", "downloadFailed": "<PERSON><PERSON><PERSON><PERSON> gagal", "parseFailed": "<PERSON><PERSON><PERSON><PERSON> gagal", "cancelled": "Di<PERSON><PERSON><PERSON>", "preparingToDownload": "Mempersiapkan pengunduhan"}, "taskActions": {"retry": "<PERSON><PERSON>", "delete": "Hapus", "showInFinder": "<PERSON><PERSON><PERSON><PERSON>", "showInFolder": "Tampilkan di Folder", "more": "<PERSON><PERSON><PERSON>", "logIn": "<PERSON><PERSON><PERSON>", "timeLeft": "<PERSON><PERSON><PERSON> ters<PERSON>", "speed": "Kecepatan", "fileSize": "Ukuran berkas"}, "auth": {"logIn": "<PERSON><PERSON><PERSON>", "logOut": "<PERSON><PERSON><PERSON>", "logInToX": "<PERSON><PERSON><PERSON> ke https://x.com", "done": "Se<PERSON><PERSON>", "cancel": "<PERSON><PERSON>", "cancelLogin": "Batalkan Masuk", "loginTo": "<PERSON><PERSON><PERSON> ke"}, "contextMenu": {"copyCaption": "<PERSON><PERSON>", "copyLinkAddress": "<PERSON><PERSON>", "openInBrowser": "<PERSON><PERSON> <PERSON>", "remove": "Hapus", "removeAll": "<PERSON><PERSON>"}, "errors": {"connectionTimeout": "<PERSON><PERSON><PERSON> koneksi habis, silakan periksa koneksi jaringan", "unsupportedUrl": "URL belum didukung. Segera tersedia", "needLoginToDownload": "<PERSON><PERSON> masuk untuk mengunduh", "fileNotFound": "<PERSON><PERSON><PERSON> tidak <PERSON>", "folderNotFound": "Folder tidak ditemukan", "openFileLocationFailed": "Gagal membuka lokasi berkas", "clipboardNotContainsValidUrl": "Papan klip tidak berisi URL yang valid", "retryFailed": "<PERSON><PERSON><PERSON><PERSON> ulang gagal", "checkVersionFailed": "<PERSON><PERSON><PERSON><PERSON><PERSON> versi gagal", "notImplemented": "Belum diimplementasikan", "parseError": "<PERSON><PERSON><PERSON>", "downloadError": "<PERSON><PERSON><PERSON>, silakan periksa koneksi jar<PERSON>n", "mergeError": "<PERSON><PERSON><PERSON>"}, "messages": {"saveSuccess": "<PERSON><PERSON><PERSON><PERSON><PERSON> be<PERSON>", "saveFailed": "<PERSON><PERSON><PERSON><PERSON><PERSON> gagal", "authSuccess": "<PERSON><PERSON><PERSON><PERSON> be<PERSON>", "authFailed": "<PERSON><PERSON><PERSON><PERSON> gagal", "removeAuthSuccess": "Penghapusan o<PERSON><PERSON><PERSON> ber<PERSON>", "removeAuthFailed": "Penghapusan otor<PERSON><PERSON> gagal", "validUrlPrompt": "<PERSON>lakan masukkan URL yang valid", "websiteAlreadyInList": "Situs web ini sudah ada dalam daftar", "defaultError": "<PERSON><PERSON> gagal"}, "dialogs": {"removeAll": {"removeAllItemsFromTheList": "Hapus semua item dari daftar?", "deleteDownloadedFiles": "<PERSON><PERSON> berkas yang telah di<PERSON>uh", "remove": "Hapus", "cancel": "<PERSON><PERSON>"}, "fileDeleted": {"fileHasBeenDeletedOrMoved": "<PERSON><PERSON>s telah dihapus atau dipindahkan. Hapus item ini?", "remove": "Hapus", "cancel": "<PERSON><PERSON>"}, "deleteDownloading": {"fileIsDownloading": "Berkas sedang diunduh. <PERSON><PERSON>?", "delete": "Hapus", "cancel": "<PERSON><PERSON>"}}, "menu": {"website": "Situs Web", "settings": "<PERSON><PERSON><PERSON><PERSON>"}, "settings": {"general": "<PERSON><PERSON>", "saveTo": "Simpan ke", "changeFolderBrowser": "Ubah Folder", "language": "Bahasa", "system": "Sistem", "createSubdirectoriesForDownloadedPlaylistsAndChannels": "Buat subfolder untuk daftar putar dan saluran yang diunduh", "numerateFilesInPlaylistsAndChannels": "Beri nomor pada berkas dalam daftar putar dan saluran", "embedSubtitlesInVideoFile": "Sematkan subtitle dalam berkas video", "authorization": "<PERSON><PERSON><PERSON><PERSON>", "logOut": "<PERSON><PERSON><PERSON>", "logIn": "<PERSON><PERSON><PERSON>", "delete": "Hapus", "addUrl": "Tambah", "enterTheWebsiteUrl": "Masukkan URL situs web", "authorizationPanelTips": "Masuk ke situs web memungkinkan pengunduhan konten dengan batasan usia, konten keanggotaan yang telah <PERSON> be<PERSON>, dan konten pribadi la<PERSON>ya.", "proxy": "<PERSON>ks<PERSON>", "proxyType": "<PERSON><PERSON>", "httpProxy": "Proksi HTTP", "socks5Proxy": "Proksi SOCKS5", "usingSystemProxy": "Menggunakan Proksi Sistem", "notUsingProxy": "Tidak Menggunakan Proksi", "host": "Host", "port": "Port", "proxyInfoMessage": {"pleaseEnterProxyHost": "<PERSON><PERSON><PERSON> ma<PERSON>kkan al<PERSON> host proksi", "pleaseEnterValidProxyHost": "<PERSON><PERSON><PERSON> masukkan alamat host yang valid", "pleaseEnterProxyPort": "Silakan masukkan port proksi", "pleaseEnterValidProxyPort": "Silakan masukkan nomor port yang valid (1-65535)", "optional": "Opsional"}, "login": "<PERSON><PERSON>", "password": "<PERSON><PERSON>", "save": "Simpan", "about": "Tentang", "version": "<PERSON><PERSON><PERSON>", "latestVersion": "<PERSON><PERSON><PERSON>", "upgrade": "<PERSON><PERSON><PERSON>", "message": {"loadSettingsFailed": "Gagal memuat pengaturan"}, "checkVersion": "<PERSON><PERSON><PERSON>", "latestVersionAvailable": "Versi terbaru ditemukan", "latestVersionNotAvailable": "Sudah menggunakan versi terbaru"}, "update": {"newVersionAvailable": "Versi baru tersedia", "whatsNew": "<PERSON><PERSON> yang <PERSON>", "upgradeNow": "<PERSON><PERSON><PERSON>", "downloading": "Sedang mengunduh...", "remindAfterDownload": "<PERSON><PERSON><PERSON> set<PERSON>", "newVersionReady": "Versi baru telah siap", "installNow": "<PERSON><PERSON>", "remindLater": "<PERSON><PERSON><PERSON>"}, "mainMenu": {"download": "<PERSON><PERSON><PERSON>", "online": "Daring", "convert": "Kon<PERSON><PERSON>", "audioVideoMerger": "Penggabungan Audio Video", "joinTelegramGroup": "Gabung ke Grup Telegram", "joinDiscordCommunity": "Gabung ke Komunitas Discord"}, "application": {"menu": {"download": "<PERSON><PERSON><PERSON>", "network": "Peman<PERSON><PERSON>", "format": "Konversi Format", "merge": "Penggabungan Audio dan Video"}, "loading": "Memuat..."}, "common": {"cancel": "<PERSON><PERSON>", "ok": "OK"}}