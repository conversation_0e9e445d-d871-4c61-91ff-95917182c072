export type ProxyType = 'NONE' | 'SYSTEM' | 'HTTP' | 'SOCKS5';

export interface ProxySettings {
    type: ProxyType;
    host: string;
    port: string;
    username: string;
    password: string;
}

// 添加字幕类型
export type SubtitleType = 'embedded' | 'external';

export interface Settings {
    defaultDownloadPath: string;
    downloadType: 'video' | 'audio';
    downloadPlatform: 'windows' | 'macos' | 'linux' | 'ios' | 'android' | 'none';
    downloadTypeVideo: {
        quality: string;
        format: string;
        subtitle: string[];
        audioChange: string[];
    };
    downloadTypeAudio: {
        quality: string;
        format: string;
    };
    thumbnail: boolean;
    subtitleType?: SubtitleType;
    proxy: {
        type: 'SYSTEM' | 'HTTP' | 'SOCKS5' | 'NONE';
        host: string;
        port: string;
        username: string;
        password: string;
    };
    language: string;
} 