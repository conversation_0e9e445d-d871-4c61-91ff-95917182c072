import { Button, Checkbox, Label, Modal } from 'flowbite-react';
import { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { addOrRemoveUniqueItemOfArray } from '@/utils/array';
import { extractFirstValidUrl } from '@/utils/download';

interface MultipleLinksModalProps {
  open: boolean;
  onCancel: () => void;
  links: ReturnType<typeof extractFirstValidUrl>;
  onOk: (selectedLinks: string[]) => void;
}

function MultipleLinksModal(props: MultipleLinksModalProps) {
  const { open, onCancel, links, onOk } = props;
  const { t } = useTranslation();

  const [selectedUrls, setSelectedUrls] = useState<string[]>([]);

  // 默认全选所有链接
  useEffect(() => {
    if (open && links.length > 0) {
      setSelectedUrls(links.map((link) => link.href));
    }
  }, [open, links]);

  const currentHasAll = useMemo(
    () => selectedUrls.length === links.length,
    [selectedUrls.length, links.length],
  );

  return (
    <Modal
      show={open}
      onClose={onCancel}
      // ! HACK 因为 flowbite-react bug
      className="bg-gray-900/50 dar:bg-gray-900/80"
    >
      <Modal.Header className="border-b-0">
        <b className="text-2xl">{t('download.modal.needDownloadToSelect')}</b>
      </Modal.Header>
      <Modal.Body>
        {links.map((link) => (
          <section key={link.href} className="gap-3 flex items-center mb-2">
            <Checkbox
              // ! HACK 因为 flowbite-react bug
              className="checked:bg-[#1C64F2] checked:border-[#1C64F2] flex-shrink-0"
              color="blue"
              id={`multipleLink/${link.href}`}
              checked={selectedUrls.includes(link.href)}
              onChange={() => {
                const newSelectedUrls = addOrRemoveUniqueItemOfArray(
                  link.href,
                  selectedUrls,
                );
                setSelectedUrls(newSelectedUrls);
              }}
            />
            <Label 
              htmlFor={`multipleLink/${link.href}`}
              className="truncate overflow-hidden"
              title={link.href}
            >
              {link.href}
            </Label>
          </section>
        ))}
        <div className="pt-8 flex items-center gap-3">
          <Checkbox
            color="blue"
            // ! HACK 因为 flowbite-react bug
            className="checked:bg-[#1C64F2] checked:border-[#1C64F2]"
            id="selectAll"
            checked={currentHasAll}
            onClick={() => {
              if (currentHasAll) {
                setSelectedUrls([]);
              } else {
                setSelectedUrls(links.map((link) => link.href));
              }
            }}
          />
          <Label htmlFor="selectAll">
            {currentHasAll
              ? t('download.modal.cancelAll')
              : t('download.modal.selectAll')}
          </Label>
          <Button
            className="ml-auto"
            color="blue"
            onClick={() => onOk(selectedUrls)}
          >
            {t('download.modal.download')}
          </Button>
        </div>
      </Modal.Body>
    </Modal>
  );
}

export default MultipleLinksModal;
