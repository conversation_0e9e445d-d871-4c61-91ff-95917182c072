import { DEFAULT_AUTH_SITES } from '@main/constants/settings';
import { showError } from '@main/utils/error-handler';
import * as Sentry from '@sentry/electron/main';
import { app, session } from 'electron';
import * as fs from 'fs';
import * as path from 'path';

export type BrowserType = 'none';

interface AuthSite {
  name: string;
  url: string;
  isAuthorized: boolean;
}

export class CookieManager {
  private static instance: CookieManager;
  private cookiesFilePath: string;
  private sitesFilePath: string;

  /*************  ✨ Codeium Command ⭐  *************/
  /**
   * Constructor for CookieManager.
   *
   * This constructor is private. The class should be instantiated using the
   * getInstance() method.
   *
   * @private
   */
  /******  3d5220c9-465b-4e2d-ac34-c7ffd5a83575  *******/ private constructor() {
    this.cookiesFilePath = path.join(app.getPath('userData'), 'cookies.txt');
    this.sitesFilePath = path.join(app.getPath('userData'), 'auth_sites.json');
  }

  public static getInstance(): CookieManager {
    if (!CookieManager.instance) {
      CookieManager.instance = new CookieManager();
    }
    return CookieManager.instance;
  }

  public async saveSiteCookies(
    siteUrl: string,
    cookies: Electron.Cookie[],
  ): Promise<void> {
    const cookieStr = this.formatCookies(cookies);

    // 读取现有的 cookies 文件
    let existingContent = '';
    try {
      await fs.promises.access(this.cookiesFilePath);
      existingContent = await fs.promises.readFile(
        this.cookiesFilePath,
        'utf8',
      );
    } catch (error) {
      // 文件不存在，继续使用空字符串
      console.error('读取 cookies 文件失败:', error);
    }

    // 更新或添加新的 cookies
    const fileContent = this.updateCookiesContent(
      existingContent,
      siteUrl,
      cookieStr,
    );
    await fs.promises.writeFile(this.cookiesFilePath, fileContent, 'utf8');
  }

  public async removeSiteCookies(url: string): Promise<void> {
    try {
      const cookies = await session.defaultSession.cookies.get({});
      // 从 electron 中清除所有相关的数据
      const domain = new URL(url).hostname.replace(/^www\./, ''); // "youtube.com"

      // 只移除指定域名的 cookie
      for (const cookie of cookies) {
        // 对x.com和twitter.com进行特殊处理，将其视为同一域名
        if (
          (domain.includes('x.com') || domain.includes('twitter.com')) &&
          (cookie.domain?.includes('x.com') ||
            cookie.domain?.includes('twitter.com'))
        ) {
          // 移除所有x.com和twitter.com的cookie
          const cookieUrl = `http${cookie.secure ? 's' : ''}://${cookie.domain.startsWith('.') ? cookie.domain.slice(1) : cookie.domain}${cookie.path}`;
          await session.defaultSession.cookies.remove(cookieUrl, cookie.name);
        } else if (cookie.domain && cookie.domain.includes(domain)) {
          const cookieUrl = `http${cookie.secure ? 's' : ''}://${cookie.domain.startsWith('.') ? cookie.domain.slice(1) : cookie.domain}${cookie.path}`;
          await session.defaultSession.cookies.remove(cookieUrl, cookie.name);
        }
      }

      // 获取所有剩余的 cookies 并重新保存到文件
      const remainingCookies = await session.defaultSession.cookies.get({});
      if (remainingCookies.length > 0) {
        const cookieStr = this.formatCookies(remainingCookies);
        const header =
          '# Netscape HTTP Cookie File\n# This file is generated by yt-dlp.  Do not edit.\n\n';
        await fs.promises.writeFile(
          this.cookiesFilePath,
          header + cookieStr,
          'utf8',
        );
      } else {
        // 如果没有剩余的 cookies，创建一个只有头部的空文件
        const header =
          '# Netscape HTTP Cookie File\n# This file is generated by yt-dlp.  Do not edit.\n\n';
        await fs.promises.writeFile(this.cookiesFilePath, header, 'utf8');
      }
    } catch (error) {
      Sentry.captureException('移除 Cookie 失败:' + error);
      throw new Error('移除 Cookie 失败');
    }
  }

  public async createTempCookieFile(): Promise<string> {
    const tempId =
      Date.now().toString(36) + Math.random().toString(36).substr(2);
    const tempPath = path.join(app.getPath('temp'), `${tempId}_cookies.txt`);

    try {
      await fs.promises.access(this.cookiesFilePath);
      await fs.promises.copyFile(this.cookiesFilePath, tempPath);
    } catch (error) {
      console.error('创建临时 cookie 文件失败:', error);
      // 创建空的 cookie 文件
      const header =
        '# Netscape HTTP Cookie File\n# This file is generated by yt-dlp.  Do not edit.\n\n';
      await fs.promises.writeFile(tempPath, header, 'utf8');
    }

    return tempPath;
  }

  private updateCookiesContent(
    existingContent: string,
    siteUrl: string,
    newCookies: string,
  ): string {
    const domain = new URL(siteUrl).hostname;
    const header =
      '# Netscape HTTP Cookie File\n# This file is generated by yt-dlp.  Do not edit.\n';

    // 如果文件为空，直接返回头部信息和新的cookies
    if (!existingContent) {
      return header + newCookies;
    }

    // 从现有内容中过滤掉旧的domain的cookies
    const cookies = existingContent
      .split('\n')
      .filter((line) => !line.startsWith('#') && !line.includes(domain));

    return [header, ...cookies, newCookies].join('\n');
  }

  private removeDomainCookies(content: string, domain: string): string {
    const lines = content.split('\n');
    const header = lines.slice(0, 3).join('\n');
    const cookies = lines.slice(3).filter((line) => !line.includes(domain));

    return [header, ...cookies].join('\n');
  }

  private formatCookies(cookies: Electron.Cookie[]): string {
    return cookies
      .map((cookie) => {
        const domain = cookie.domain
          ? cookie.domain.startsWith('.')
            ? cookie.domain
            : `.${cookie.domain}`
          : '.localhost';
        // 将时间戳转换为整数
        const expirationDate = cookie.expirationDate
          ? Math.floor(cookie.expirationDate)
          : Math.floor(Date.now() / 1000 + 365 * 24 * 60 * 60); // 默认一年后过期

        return `${domain}\tTRUE\t${cookie.path || '/'}\t${cookie.secure}\t${expirationDate}\t${cookie.name}\t${cookie.value}`;
      })
      .join('\n');
  }

  public async readBrowserCookies(browser: BrowserType): Promise<boolean> {
    try {
      if (browser === 'none') {
        // 清除 cookies.txt 文件
        try {
          await fs.promises.access(this.cookiesFilePath);
          await fs.promises.writeFile(this.cookiesFilePath, '', 'utf8');
        } catch (error) {
          console.error('清除 cookies 文件失败:', error);
          // 文件不存在，不需要清除
        }
      }
      return true;
    } catch (error) {
      showError('读取Cookie失败: ' + error);
      Sentry.captureException('读取Cookie失败:' + error);
      return false;
    }
  }

  public async getSavedSites(): Promise<AuthSite[]> {
    try {
      try {
        await fs.promises.access(this.sitesFilePath);
        const content = await fs.promises.readFile(this.sitesFilePath, 'utf8');
        return JSON.parse(content);
      } catch (error) {
        console.error('读取网站列表失败:', error);
        // 文件不存在，创建默认网站列表
        await fs.promises.writeFile(
          this.sitesFilePath,
          JSON.stringify(DEFAULT_AUTH_SITES),
          'utf8',
        );
        return DEFAULT_AUTH_SITES;
      }
    } catch (error) {
      showError(
        error instanceof Error ? error.message : '读取网站列表失败' + error,
      );
      Sentry.captureException('读取网站列表失败:' + error);
      return [];
    }
  }

  public async saveSites(sites: AuthSite[]): Promise<void> {
    try {
      await fs.promises.writeFile(
        this.sitesFilePath,
        JSON.stringify(sites, null, 2),
        'utf8',
      );
    } catch (error) {
      showError(
        error instanceof Error ? error.message : '保存网站列表失败' + error,
      );
      Sentry.captureException('保存网站列表失败:' + error);
      throw new Error('保存网站列表失败');
    }
  }
}
