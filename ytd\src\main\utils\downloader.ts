import { trackEvent } from '@aptabase/electron/main';
import { DOWNLOAD_STATUS_ENUM } from '@common/constants/download';
import { VideoInfo } from '@common/types/download';
import { Settings } from '@main/types/settings';
import { <PERSON>ieManager } from '@main/utils/cookie-manager';
import { getOrCreateDeviceId } from '@main/utils/device-id';
import { DownloadStore } from '@main/utils/download-store';
import { showError } from '@main/utils/error-handler';
import { downloadFile } from '@main/utils/file-dowmloader-util/dl-mange';
import i18n from '@main/utils/i18n';
import { parseJsonToUrlList } from '@main/utils/json-to-url';
import { setupGlobalProxy } from '@main/utils/proxy-setting';
import * as Sentry from '@sentry/electron/main';
import { ChildProcess, exec, spawn } from 'child_process';
import { app, BrowserWindow, dialog, ipcMain } from 'electron';
import * as fs from 'fs';
import * as path from 'path';

import { FFmpegHandler } from './ffmpegHandler';

// 异步初始化设备ID
let deviceId: string;
(async () => {
  deviceId = await getOrCreateDeviceId();
})();

// 添加一个简单的翻译函数

// 格式化剩余时间为 HH:MM:SS 或 MM:SS 格式
function formatRemainingTime(seconds: number): string {
  // 处理无效输入
  if (isNaN(seconds) || !isFinite(seconds) || seconds < 0) {
    return '00:00';
  }

  // 如果速度太慢（导致时间过长）或无限大，显示为未知
  if (seconds > 86400 * 365) {
    // 超过1年的时间
    return '--:--';
  }

  // 向下取整总秒数
  seconds = Math.floor(seconds);

  // 计算小时、分钟和剩余秒数
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = seconds % 60;

  // 如果有小时，返回 HH:MM:SS 格式
  if (hours > 0) {
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  }

  // 否则返回 MM:SS 格式
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
}
export class Downloader {
  private static instance: Downloader | null = null;
  private window: BrowserWindow;
  private ytDlpPath: string;
  private ffmpegPath: string;
  private cookieFilePath: string;
  private lastValidEta: Map<string, string> = new Map();
  private downloadTasks: Map<
    string,
    {
      process: ChildProcess;
      status: DOWNLOAD_STATUS_ENUM;
      resumeData?: {
        url: string;
        tempFilename: string;
        finalFilename: string;
        title: string;
        format: string;
        quality: string;
        downloadedBytes: number;
      };
      videoInfo?: VideoInfo;
      downloadController?: AbortController;
    }
  > = new Map();

  // 添加静态语言映射表
  private static readonly LANGUAGE_MAP = {
    'en-US': 'eng',
    'zh-CN': 'zho',
    'es-US': 'spa',
    'fr-FR': 'fra',
    'de-DE': 'deu',
    'ja-JP': 'jpn',
    'ko-KR': 'kor',
    'ru-RU': 'rus',
    'pt-PT': 'por',
    'it-IT': 'ita',
    'nl-NL': 'nld',
    'tr-TR': 'tur',
    'pl-PL': 'pol',
    'sv-SE': 'swe',
    'fi-FI': 'fin',
    'da-DK': 'dan',
    'no-NO': 'nor',
    'el-GR': 'ell',
    'cs-CZ': 'ces',
    'hu-HU': 'hun',
    'uk-UA': 'ukr',
    'ro-RO': 'ron',
    'ar-AE': 'ara',
    'hi-IN': 'hin',
    'th-TH': 'tha',
    'vi-VN': 'vie',
    'id-ID': 'ind',
    'ms-MY': 'msa',
    'fil-PH': 'tgl',
    'he-IL': 'heb',
    'fa-IR': 'fas',
    'bn-BD': 'ben',
    'ta-IN': 'tam',
    'te-IN': 'tel',
    'ur-PK': 'urd',
  } as const;

  // 出现错误时，保存的错误信息
  private lastErrorMessage: { [key: string]: string } = {};

  public static getInstance(window: BrowserWindow): Downloader {
    if (!Downloader.instance) {
      Downloader.instance = new Downloader(window);
    }
    return Downloader.instance;
  }

  private constructor(window: BrowserWindow) {
    this.window = window;
    console.log('调用下载器构造函数');

    try {
      // 根据平台选择正确的可执行文件名
      const ytdlpExecutable =
        process.platform === 'win32' ? 'yt-dlp.exe' : 'yt-dlp';
      const ffmpegExecutable =
        process.platform === 'win32' ? 'ffmpeg.exe' : 'ffmpeg';

      this.ytDlpPath = this.getResourcePath(ytdlpExecutable);
      this.ffmpegPath = this.getResourcePath(ffmpegExecutable);
      this.cookieFilePath = path.join(app.getPath('userData'), 'cookies.txt');

      // 在 Mac 平台上设置执行权限
      if (process.platform === 'darwin') {
        try {
          // 使用八进制数设置权限
          const mode = 0o755; // rwxr-xr-x
          fs.chmodSync(this.ytDlpPath, mode);
          fs.chmodSync(this.ffmpegPath, mode);
          console.log('已设置执行权限');
        } catch (error) {
          console.error('设置执行权限失败:', error);
          Sentry.captureException('设置执行权限失败:' + error);
        }
      }

      // 检查文件是否存在
      this.checkFilesExist()
        .then((filesExist) => {
          if (!filesExist) {
            throw new Error('必要的执行文件不存在，请重新安装应用');
          }

          console.log('Platform:', process.platform);
          console.log('yt-dlp path:', this.ytDlpPath);
          console.log('ffmpeg path:', this.ffmpegPath);
          console.log('Paths exist?', {
            ytdlp: filesExist,
            ffmpeg: filesExist,
          });

          this.registerHandlers();
        })
        .catch((error) => {
          showError('初始化下载器失败:' + error);
          Sentry.captureException('初始化下载器失败:' + error);
          throw error;
        });
    } catch (error) {
      showError('初始化下载器失败:' + error);
      Sentry.captureException('初始化下载器失败:' + error);
      throw error;
    }
  }

  // 异步检查文件是否存在
  private async checkFilesExist(): Promise<boolean> {
    try {
      await Promise.all([
        fs.promises.access(this.ytDlpPath, fs.constants.F_OK),
        fs.promises.access(this.ffmpegPath, fs.constants.F_OK),
      ]);
      return true;
    } catch {
      // 错误处理已在调用处执行
      return false;
    }
  }

  private registerHandlers() {
    // 先移除所有现有的处理程序
    ipcMain.removeHandler('get-download-video-info');
    ipcMain.removeHandler('start-download');
    ipcMain.removeHandler('resume-download');
    ipcMain.removeHandler('cancel-download');
    ipcMain.removeHandler('check-file-exists');

    // 然后重新注册处理程序
    ipcMain.handle(
      'get-download-video-info',
      async (_, url: string, taskId: string) => {
        try {
          await this.getDownloadVideoInfo(url, taskId);
          return { success: true };
        } catch (error) {
          // 检查是否是授权相关错误
          if (
            error instanceof Error &&
            (error.message.includes('Sign') || error.message.includes('cookie'))
          ) {
            console.log('需要使用浏览器cookie，请先登录');
            return {
              success: false,
              error: error.message,
              status: 'parse_sign_error',
            };
          } else if (
            error instanceof Error &&
            error.message.includes('ENOENT')
          ) {
            console.log('视频文件不存在，获取视频信息失败');
            Sentry.captureException('视频文件不存在，获取视频信息失败');
            return {
              success: false,
              error: error.message,
              status: 'parse_sign_error',
            };
          } else {
            console.log('获取视频信息失败:' + error);
            Sentry.captureException('获取视频信息失败:' + error);
            return {
              success: false,
              error:
                error instanceof Error ? error.message : '获取视频信息失败',
              status: 'parse_sign_error',
            };
          }
        }
      },
    );

    ipcMain.handle('cancel-download', async (_, taskId: string) => {
      try {
        console.log('点击删除按钮2');
        await this.handleCancelDownload(taskId);
        return { success: true };
      } catch (error) {
        // console.error('Cancel download error:', error);
        // return { success: false, error: error instanceof Error ? error.message : '取消失败' };
        if (error instanceof Error && error.message.includes('ENOENT')) {
          Sentry.captureException('任务不存在，取消失败');
          return {
            success: false,
            error:
              error instanceof Error ? error.message : '任务不存在，取消失败',
          };
        } else {
          Sentry.captureException('取消失败' + error);
          return {
            success: false,
            error: error instanceof Error ? error.message : '取消失败' + error,
          };
        }
      }
    });

    ipcMain.handle('get-settings', async () => {
      try {
        const settings = await Downloader.loadSettings();
        return settings;
      } catch (error) {
        Sentry.captureException('获取设置失败' + error);
        showError(
          error instanceof Error ? error.message : '获取设置失败' + error,
        );
        throw error;
      }
    });

    ipcMain.handle('save-settings', async (_, settings: Settings) => {
      try {
        await this.saveSettings(settings);
        return { success: true };
      } catch (error) {
        Sentry.captureException('保存设置失败' + error);
        showError(
          error instanceof Error ? error.message : '保存设置失败' + error,
        );
        return {
          success: false,
          error: error instanceof Error ? error.message : '保存设置失败',
        };
      }
    });

    ipcMain.handle('select-directory', async () => {
      try {
        const result = await dialog.showOpenDialog(this.window, {
          properties: ['openDirectory'],
        });

        if (!result.canceled && result.filePaths.length > 0) {
          return { success: true, path: result.filePaths[0] };
        }
        return { success: false, error: '未选择目录' };
      } catch (error) {
        // return { success: false, error: '选择目录失败' };
        if (error instanceof Error && error.message.includes('ENOENT')) {
          Sentry.captureException('任务不存在，选择目录失败');
          showError('任务不存在，选择目录失败');
          return {
            success: false,
            error:
              error instanceof Error
                ? error.message
                : '任务不存在，选择目录失败',
          };
        } else {
          Sentry.captureException('选择目录失败' + error);
          showError(
            error instanceof Error ? error.message : '选择目录失败' + error,
          );
          return {
            success: false,
            error:
              error instanceof Error ? error.message : '选择目录失败' + error,
          };
        }
      }
    });

    ipcMain.handle('clear-json-tasks', (_, taskId: string) => {
      DownloadStore.getInstance().clearJsonTasks(taskId);
      return { success: true };
    });

    ipcMain.handle('check-file-exists', async (_, filename: string) => {
      try {
        // 获取设置中的下载目录，返回文件名
        const settings = await Downloader.loadSettings();
        const finalFilePath = path.join(settings.defaultDownloadPath, filename);

        let fileExists = false;
        try {
          await fs.promises.access(finalFilePath, fs.constants.F_OK);
          fileExists = true;
        } catch {
          // 文件不存在，保持fileExists为false
        }

        if (fileExists) {
          for (let i = 1; ; i++) {
            const newPath = `${finalFilePath.replace(path.extname(finalFilePath), '')}(${i})${path.extname(finalFilePath)}`;
            try {
              await fs.promises.access(newPath, fs.constants.F_OK);
              // 文件存在，继续循环
            } catch {
              // 文件不存在，使用这个名称
              filename = `${filename.replace(path.extname(filename), '')}(${i})${path.extname(filename)}`;
              break;
            }
          }
        }
        return filename;
      } catch (error) {
        console.error('检查文件是否存在失败:', error);
        return filename;
      }
    });
  }

  public async getDownloadVideoInfo(url: string, taskId: string) {
    try {
      // 将下载信息添加到列表
      DownloadStore.getInstance().addTask(
        taskId,
        url,
        '',
        '',
        '',
        '',
        '',
        DOWNLOAD_STATUS_ENUM.Parsing,
        {
          id: '',
          title: '',
          thumbnail: '',
          duration: 0,
          formats: [],
          description: '',
          thumbnailHeaders: {},
          uploader: '',
          isPlaylist: false,
          audioLanguages: [],
        },
      );
      const settings = await Downloader.loadSettings();
      // 创建临时目录，目录名称为appName
      const appName = app.getName();
      console.log('appName:', appName);
      const tempDir = path.join(
        settings.defaultDownloadPath,
        '.' + appName,
        taskId,
      );
      await fs.promises.mkdir(tempDir, { recursive: true });

      // 设置.temp目录为隐藏
      const tempBaseDir = path.join(
        settings.defaultDownloadPath,
        '.' + appName,
      );
      if (process.platform === 'win32') {
        try {
          await new Promise<void>((resolve, reject) => {
            // 调用子进程以用来提供隐藏能力
            exec(`attrib +h "${tempBaseDir}"`, (err) => {
              if (err) {
                console.error('无法隐藏文件夹:', err);
                return reject(err);
              }
              console.log('文件夹已隐藏');
              resolve();
            });
          });
        } catch (error) {
          console.error('设置隐藏属性失败:', error);
          Sentry.captureException('设置隐藏属性失败:' + error);
        }
      }
      console.log('Downloader: 开始获取视频信息，URL:', url);
      this.getVideoInfo(url, taskId);
    } catch (error) {
      const host = url ? new URL(url).hostname : '未知网站：' + url;
      Sentry.captureException('获取视频信息失败:' + error);
      trackEvent('视频信息提取失败', {
        host: host,
        error: error instanceof Error ? error.message : String(error),
        deviceId: deviceId,
        timestamp: Date.now(),
      });
      throw error;
    }
  }

  private async getVideoInfo(url: string, taskId: string) {
    // 获取视频信息的Promise
    new Promise<VideoInfo>((resolve, reject) => {
      // 提取host信息用于跟踪
      const host = url ? new URL(url).hostname : '未知网站：' + url;

      const args = [
        url,
        '--dump-json',
        '--no-check-certificates',
        '--no-warnings',
        '--no-playlist',
        '--ignore-errors',
        '--ignore-config',
        '--no-cache-dir',
        '--prefer-insecure',
        '--extractor-args',
        'generic:extract_flat=true',
        '--add-header',
        'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        '--add-header',
        'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        '--add-header',
        'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8',
        '--retries',
        '10',
        '--fragment-retries',
        '10',
        '--socket-timeout',
        '10',
        '--format-sort',
        'res,ext:mp4:m4a',
      ];

      // 使用立即执行的异步函数来处理内部的异步操作
      (async () => {
        try {
          // 获取当前代理设置
          const proxySettings = await Downloader.loadSettings();
          switch (proxySettings.proxy.type) {
            case 'NONE':
              args.push('--proxy', '');
              break;
            case 'HTTP':
              args.push(
                '--proxy',
                'http://' +
                  proxySettings.proxy.host +
                  ':' +
                  proxySettings.proxy.port,
              );
              break;
            case 'SOCKS5':
              args.push(
                '--proxy',
                'socks5://' +
                  proxySettings.proxy.host +
                  ':' +
                  proxySettings.proxy.port,
              );
              break;
            default:
              break;
          }

          // 创建临时 cookie 文件
          const cookieFilePath =
            await CookieManager.getInstance().createTempCookieFile();
          if (cookieFilePath) {
            args.push('--cookies', cookieFilePath);
          }

          console.log('Downloader: 执行命令，参数:', args);
          const ytDlp = spawn(this.ytDlpPath, args);
          let output = '';
          let errorOutput = '';

          ytDlp.stdout.on('data', (data) => {
            console.log('Downloader: ytDlp.stdout.on data:', data);
            output += data.toString();
          });

          ytDlp.stderr.on('data', (data) => {
            console.log('Downloader: ytDlp.stderr.on data:', data);
            const message = data.toString();
            if (!message.includes('WARNING:')) {
              errorOutput += message;
            }
          });

          ytDlp.on('close', async (code) => {
            console.log('Downloader: ytDlp.on close, code:', code);
            if (code === 0 && output) {
              try {
                const rawInfo = JSON.parse(output);
                // 记录提取成功
                trackEvent('视频信息提取成功', {
                  host: host,
                  deviceId: deviceId,
                  timestamp: Date.now(),
                });
                console.log('Downloader: 解析成功，rawInfo:');

                // 更新downloads.json中的封面图信息
                const downloadStore = DownloadStore.getInstance();
                const existingTaskVideoInfo =
                  downloadStore.getTask(taskId)?.videoInfo;

                downloadStore.updateTask(taskId, {
                  thumbnail: rawInfo.thumbnail || '',
                  thumbnailHeaders: rawInfo.thumbnailHeaders || {},
                  title: rawInfo.title || '',
                  status: DOWNLOAD_STATUS_ENUM.DownloadingVideo,
                  videoInfo: {
                    ...existingTaskVideoInfo, // 保持原有的 videoInfo
                    id: taskId,
                    title: rawInfo.title || '',
                    thumbnail: rawInfo.thumbnail || '',
                    thumbnailHeaders: rawInfo.thumbnailHeaders || {},
                  },
                });

                this.sendProgress(
                  taskId,
                  DOWNLOAD_STATUS_ENUM.DownloadingVideo,
                  {
                    percent: 0,
                  },
                );

                // 在后台开始下载过程
                this.startDownloadInBackground(
                  rawInfo,
                  output,
                  url,
                  taskId,
                  cookieFilePath,
                  host,
                );
              } catch (error) {
                Sentry.captureException('获取视频信息失败:' + error);
                // 记录提取失败
                trackEvent('视频信息提取失败', {
                  host: host,
                  deviceId: deviceId,
                  error: error instanceof Error ? error.message : String(error),
                  timestamp: Date.now(),
                });
                this.sendProgress(taskId, DOWNLOAD_STATUS_ENUM.ParseError, {
                  error: error instanceof Error ? error.message : String(error),
                });
              }
            } else {
              console.log(
                'Downloader: ytDlp.on close, errorOutput:',
                errorOutput,
              );
              // 记录提取失败
              trackEvent('视频信息提取失败', {
                host: host,
                deviceId: deviceId,
                error: errorOutput || '未知错误',
                timestamp: Date.now(),
              });

              // 更新本地存储的失败信息
              const downloadStore = DownloadStore.getInstance();

              console.log('Downloader: 更新本地存储的失败信息', errorOutput);

              // 判断错误类型
              if (
                errorOutput.includes('Sign') ||
                errorOutput.includes('cookie')
              ) {
                downloadStore.updateTask(taskId, {
                  status: DOWNLOAD_STATUS_ENUM.ParseSignError,
                  error: i18n.t('errors.needLoginToDownload'),
                });
                this.sendProgress(taskId, DOWNLOAD_STATUS_ENUM.ParseSignError, {
                  error: i18n.t('errors.needLoginToDownload'),
                });
              } else if (errorOutput.includes('Unsupported URL')) {
                downloadStore.updateTask(taskId, {
                  status: DOWNLOAD_STATUS_ENUM.UnsupportedUrl,
                  error: i18n.t('errors.unsupportedUrl'),
                });
                this.sendProgress(taskId, DOWNLOAD_STATUS_ENUM.ParseError, {
                  error: i18n.t('errors.unsupportedUrl'),
                });
              } else {
                downloadStore.updateTask(taskId, {
                  status: DOWNLOAD_STATUS_ENUM.ParseError,
                  error: i18n.t('errors.parseError'),
                });
                this.sendProgress(taskId, DOWNLOAD_STATUS_ENUM.ParseError, {
                  error: i18n.t('errors.parseError'),
                });
                Sentry.captureException('获取视频信息失败:' + errorOutput);
              }
            }

            // 如果获取视频信息失败，清理临时文件
            if (code !== 0 || !output) {
              fs.unlink(cookieFilePath, () => {});
            }
          });

          ytDlp.on('error', (error) => {
            console.log('Downloader: ytDlp.on error, error:', error);
            // 修改为只记录信息提取失败
            trackEvent('视频信息提取失败', {
              host: host,
              error: error instanceof Error ? error.message : String(error),
              deviceId: deviceId,
              timestamp: Date.now(),
            });
            reject(error);

            // 清理临时文件
            fs.unlink(cookieFilePath, () => {});
          });
        } catch (error) {
          reject(error);
        }
      })();
    });
  }

  // 新增方法：在后台执行下载操作
  private async startDownloadInBackground(
    rawInfo: VideoInfo,
    output: string,
    url: string,
    taskId: string,
    cookieFilePath: string,
    host: string,
  ): Promise<void> {
    try {
      const settings = await Downloader.loadSettings();

      // 初始化这个任务的最后有效时间
      this.lastValidEta.set(taskId, '00:00');

      // 解析出需要下载的url
      const urlList = await parseJsonToUrlList(output);
      const downloadTempPath = path.join(
        settings.defaultDownloadPath,
        '.snapany',
        taskId,
      );

      console.log('Downloader: 开始下载，urlList:', urlList);

      // 创建AbortController用于取消下载
      const controller = new AbortController();
      // 将下载任务和controller存储到downloadTasks中
      const task = this.downloadTasks.get(taskId);
      if (task) {
        task.downloadController = controller;
      } else {
        // 如果任务不存在，创建一个新的任务
        this.downloadTasks.set(taskId, {
          process: null,
          status: DOWNLOAD_STATUS_ENUM.DownloadingVideo,
          downloadController: controller,
          videoInfo: rawInfo,
        });
      }

      // 更新任务状态为下载中
      const downloadStore = DownloadStore.getInstance();
      downloadStore.updateTask(taskId, {
        status: DOWNLOAD_STATUS_ENUM.DownloadingVideo,
      });

      // 下载文件
      const results = await downloadFile(
        urlList,
        downloadTempPath,
        {
          onProgress: (progress) => {
            console.log(
              'progress---:',
              '总进度:',
              progress.totalPercent,
              '普已下:',
              progress.normalDownloaded,
              'm3u8已下:',
              progress.m3u8Downloaded,
            );

            // 计算新的eta
            const remainingSeconds =
              progress.speed > 0
                ? (progress.totalBytes - progress.downloadedBytes) /
                  progress.speed
                : Infinity;

            let etaDisplay = this.lastValidEta.get(taskId) || '00:00';

            // 只在时间合理时更新显示
            if (
              !isNaN(remainingSeconds) &&
              isFinite(remainingSeconds) &&
              remainingSeconds >= 0 &&
              remainingSeconds <= 86400
            ) {
              const newEta = formatRemainingTime(remainingSeconds);
              this.lastValidEta.set(taskId, newEta);
              etaDisplay = newEta;
            }
            this.sendProgress(taskId, DOWNLOAD_STATUS_ENUM.DownloadingVideo, {
              percent: progress.totalPercent,
              speed: progress.speed,
              eta: etaDisplay,
              downloadSize: progress.totalBytes,
            });
          },
          onComplete: () => {
            this.lastValidEta.delete(taskId);
            this.sendProgress(taskId, DOWNLOAD_STATUS_ENUM.DownloadingVideo, {
              percent: 100,
            });
          },
          onError: () => {
            this.lastValidEta.delete(taskId);
            // console.log('Downloader: onError:', error);
          },
        },
        taskId,
        controller,
      );

      console.log('Downloader: 开始匹配results中的url，和urlList中的url');
      urlList.forEach((url) => {
        const result = results.find((r) => r.url === url.url);
        if (result) {
          if (!url.info) {
            url.info = {}; // 如果没有 info 属性，创建一个空对象
          }
          url.info.filePath = result.filePath;
        }
      });

      // 提取urlList中的info，组成一个数组，表示下载完成的文件信息
      const downloadInfoList = urlList.map((url) => url.info);
      const mergeOutputDir = path.join(
        settings.defaultDownloadPath,
        '.snapany',
        taskId,
        `merged`,
      );

      try {
        await new Promise<void>((resolve, reject) => {
          FFmpegHandler.getInstance().processDirectory(
            downloadInfoList,
            // settings.downloadTypeVideo.format,
            // 判断downloadType值为audio时，使用settings.downloadTypeAudio.format
            settings.downloadType === 'audio'
              ? settings.downloadTypeAudio.format
              : settings.downloadTypeVideo.format,
            mergeOutputDir,
            true,
            (progress) => {
              console.log('merge progress:', progress.percent);
              // 发送合并进度到前端
              this.sendProgress(taskId, DOWNLOAD_STATUS_ENUM.Merging, {
                percent: progress.percent,
              });
            },
            async (result) => {
              try {
                console.log('result:', result);
                // 将result返回的数组中文件移动到用的目标输出目录，文件名后缀部分不变，名称改为rawInfo.title，如果存在language属性则在文件名中添加-language
                let isFirstFile = true; // 标记是否为第一个文件
                for (const item of result) {
                  // 直接使用item.filePath作为源文件路径
                  const filePath = item.filePath;
                  const language = item.language;
                  const targetDir = path.join(
                    settings.defaultDownloadPath,
                    ...(language ? [language] : []),
                  );
                  await fs.promises.mkdir(targetDir, { recursive: true });

                  // 获取文件扩展名
                  const fileExt = path.extname(filePath);

                  // 清理文件名，移除非法字符
                  const sanitizedTitle = this.sanitizeFileName(rawInfo.title);

                  // 构建新文件名：rawInfo.title + 可选的语言标识 + 原扩展名
                  let newFileName = language
                    ? `${sanitizedTitle}-${language}${fileExt}`
                    : `${sanitizedTitle}${fileExt}`;

                  // 检查目标文件是否已存在
                  let targetFilePath = path.join(targetDir, newFileName);
                  let fileExists = false;
                  try {
                    await fs.promises.access(targetFilePath, fs.constants.F_OK);
                    fileExists = true;
                  } catch {
                    // 文件不存在，不需要处理
                  }

                  if (fileExists) {
                    const ext = path.extname(newFileName);
                    const baseFilename = newFileName.slice(0, -ext.length);
                    let counter = 1;

                    while (true) {
                      const newFileNameWithCounter = `${baseFilename}(${counter})${ext}`;
                      targetFilePath = path.join(
                        targetDir,
                        newFileNameWithCounter,
                      );

                      try {
                        await fs.promises.access(
                          targetFilePath,
                          fs.constants.F_OK,
                        );
                        counter++;
                      } catch {
                        // 文件不存在，可以使用这个名称
                        newFileName = newFileNameWithCounter;
                        break;
                      }
                    }
                  }

                  // 检查源文件是否存在
                  try {
                    await fs.promises.access(filePath, fs.constants.F_OK);
                    await fs.promises.rename(filePath, targetFilePath);

                    console.log(
                      'Downloader: 移动文件成功，文件名:',
                      newFileName,
                      '源文件路径:',
                      filePath,
                      '目标文件路径:',
                      targetFilePath,
                    );

                    // 如果是第一个文件，更新任务信息中的finalFilename和finalFilePath
                    if (isFirstFile) {
                      // 获取这个文件的信息
                      const downloadStore = DownloadStore.getInstance();
                      downloadStore.updateTask(taskId, {
                        finalFilename: newFileName,
                        finalFilePath: targetFilePath,
                      });
                      isFirstFile = false;
                    }
                  } catch (error) {
                    console.log(
                      'Downloader: 源文件不存在或移动失败:',
                      filePath,
                      error,
                    );
                  }
                }

                // 所有文件处理完成后，发送下载完成状态
                this.sendProgress(taskId, DOWNLOAD_STATUS_ENUM.Completed, {
                  message: '下载完成',
                });

                // 更新任务状态为已完成
                const downloadStore = DownloadStore.getInstance();
                downloadStore.updateTask(taskId, {
                  status: DOWNLOAD_STATUS_ENUM.Completed,
                });

                // 清理临时目录
                await this.cleanupTempDirectory(taskId, settings);

                resolve();
              } catch (error) {
                reject(error);
              }
            },
            (error) => {
              reject(error);
            },
            taskId,
          );
        });
      } catch {
        console.log('Downloader: 合并失败:');
        // 合并失败时的错误处理
        downloadStore.updateTask(taskId, {
          status: DOWNLOAD_STATUS_ENUM.MergeError,
          error: i18n.t('errors.mergeError'),
        });

        this.sendProgress(taskId, DOWNLOAD_STATUS_ENUM.MergeError, {
          message: i18n.t('errors.mergeError'),
        });

        // 即使合并失败也清理临时目录
        await this.cleanupTempDirectory(taskId, settings, true);
      }

      // 清理临时文件
      fs.unlink(cookieFilePath, () => {});
    } catch (error) {
      // 更新任务状态为错误
      const downloadStore = DownloadStore.getInstance();
      downloadStore.updateTask(taskId, {
        status: DOWNLOAD_STATUS_ENUM.DownloadError,
        error: i18n.t('errors.downloadError'),
      });

      Sentry.captureException('下载视频失败:' + error);
      trackEvent('视频下载失败', {
        host: host,
        deviceId: deviceId,
        error: error instanceof Error ? error.message : String(error),
        timestamp: Date.now(),
      });

      this.sendProgress(taskId, DOWNLOAD_STATUS_ENUM.DownloadError, {
        message: i18n.t('errors.downloadError'),
      });

      // 清理临时文件
      fs.unlink(cookieFilePath, () => {});

      throw error; // 重新抛出错误
    }
  }

  private async saveSettings(settings: Settings): Promise<void> {
    try {
      const userDataPath = app.getPath('userData');
      const settingsPath = path.join(userDataPath, 'settings.json');

      let existingSettings: Settings = {
        defaultDownloadPath: app.getPath('downloads'),
        downloadType: 'video',
        downloadPlatform: 'none',
        downloadTypeVideo: {
          quality: '1080',
          format: 'mp4',
          subtitle: ['none'],
          audioChange: ['default'],
        },
        downloadTypeAudio: {
          quality: 'best',
          format: 'mp3',
        },
        thumbnail: false,
        subtitleType: 'embedded',
        proxy: {
          type: 'SYSTEM' as const,
          host: '',
          port: '',
          username: '',
          password: '',
        },
        language: 'zh',
      };

      try {
        await fs.promises.access(settingsPath, fs.constants.F_OK);
        const data = await fs.promises.readFile(settingsPath, 'utf8');
        existingSettings = JSON.parse(data);
      } catch {
        // 文件不存在，使用默认设置
      }

      // 合并新设置
      const newSettings = {
        ...existingSettings,
        ...settings,
        proxy: settings.proxy || existingSettings.proxy,
      };

      // 保存到文件
      await fs.promises.writeFile(
        settingsPath,
        JSON.stringify(newSettings, null, 2),
      );
      // 将设置应用到当前会话
      await setupGlobalProxy(newSettings.proxy);
    } catch (error) {
      console.error('保存设置失败:', error);
      Sentry.captureException('保存设置失败:' + error);
      throw error;
    }
  }

  public static async loadSettings(): Promise<Settings> {
    const defaultSettings: Settings = {
      defaultDownloadPath: app.getPath('downloads'),
      downloadType: 'video',
      downloadPlatform: 'none',
      downloadTypeVideo: {
        quality: '1080',
        format: 'mp4',
        subtitle: ['none'],
        audioChange: ['default'],
      },
      downloadTypeAudio: {
        quality: 'best',
        format: 'mp3',
      },
      thumbnail: false,
      subtitleType: 'embedded',
      proxy: {
        type: 'SYSTEM' as const,
        host: '',
        port: '',
        username: '',
        password: '',
      },
      language: 'zh',
    };

    try {
      const userDataPath = app.getPath('userData');
      const settingsPath = path.join(userDataPath, 'settings.json');

      try {
        await fs.promises.access(settingsPath, fs.constants.F_OK);
        const data = await fs.promises.readFile(settingsPath, 'utf8');
        const parsedSettings = JSON.parse(data);

        // 确保设置对象完整
        const mergedSettings: Settings = {
          defaultDownloadPath:
            parsedSettings.defaultDownloadPath ||
            defaultSettings.defaultDownloadPath,
          downloadType:
            parsedSettings.downloadType || defaultSettings.downloadType,
          downloadPlatform:
            parsedSettings.downloadPlatform || defaultSettings.downloadPlatform,
          downloadTypeVideo:
            parsedSettings.downloadTypeVideo ||
            defaultSettings.downloadTypeVideo,
          downloadTypeAudio:
            parsedSettings.downloadTypeAudio ||
            defaultSettings.downloadTypeAudio,
          thumbnail: parsedSettings.thumbnail || defaultSettings.thumbnail,
          subtitleType:
            parsedSettings.subtitleType || defaultSettings.subtitleType,
          proxy: {
            type: parsedSettings.proxy?.type || defaultSettings.proxy.type,
            host: parsedSettings.proxy?.host || defaultSettings.proxy.host,
            port: parsedSettings.proxy?.port || defaultSettings.proxy.port,
            username:
              parsedSettings.proxy?.username || defaultSettings.proxy.username,
            password:
              parsedSettings.proxy?.password || defaultSettings.proxy.password,
          },
          language: parsedSettings.language || defaultSettings.language,
        };

        return mergedSettings;
      } catch {
        // 文件不存在或读取失败，使用默认设置
        return defaultSettings;
      }
    } catch (error) {
      console.error('加载设置失败:', error);
      Sentry.captureException('加载设置失败:' + error);
      return defaultSettings;
    }
  }

  public async handleCancelDownload(taskId: string): Promise<void> {
    const task = this.downloadTasks.get(taskId);
    const downloadStore = DownloadStore.getInstance();

    try {
      if (task) {
        // 如果任务正在运行，需要终止进程
        task.status = DOWNLOAD_STATUS_ENUM.Cancelled;

        // 如果存在下载控制器，取消下载
        if (task.downloadController) {
          console.log(`正在取消下载任务: ${taskId}`);
          task.downloadController.abort();
        }

        if (task.process && !task.process.killed) {
          console.log(`正在终止进程: PID ${task.process.pid}`);
          try {
            if (process.platform === 'win32') {
              // 在Windows上使用taskkill强制终止进程树
              spawn('taskkill', [
                '/pid',
                task.process.pid!.toString(),
                '/f',
                '/t',
              ]);
            } else {
              // 在Unix系统上使用SIGKILL信号
              task.process.kill('SIGKILL');

              // 在Unix系统上查找并终止所有相关子进程
              if (task.process.pid) {
                try {
                  // 查找所有与此PID相关的子进程
                  const pgrep = spawn('pgrep', [
                    '-P',
                    task.process.pid.toString(),
                  ]);
                  let pids = '';

                  pgrep.stdout.on('data', (data) => {
                    pids += data.toString();
                  });

                  pgrep.on('close', () => {
                    // 终止所有找到的子进程
                    pids
                      .split('\n')
                      .filter(Boolean)
                      .forEach((pid) => {
                        try {
                          process.kill(parseInt(pid), 'SIGKILL');
                          console.log(`已终止子进程: PID ${pid}`);
                        } catch (e) {
                          console.error(`终止子进程失败: PID ${pid}`, e);
                        }
                      });
                  });
                } catch (e) {
                  console.error('查找子进程失败:', e);
                }
              }
            }
          } catch (killError) {
            console.error('终止进程失败:', killError);
            Sentry.captureException('终止进程失败:' + killError);
          }

          // 等待进程退出或超时
          await new Promise<void>((resolve) => {
            const exitHandler = () => {
              console.log(`进程已退出: PID ${task.process?.pid}`);
              resolve();
            };

            task.process?.once('exit', exitHandler);

            // 设置5秒超时
            setTimeout(() => {
              task.process?.removeListener('exit', exitHandler);
              console.log('等待进程退出超时');
              resolve();
            }, 5000);
          });
        }

        // 取消FFmpegHandler中的相关任务
        const ffmpegHandler = FFmpegHandler.getInstance();
        ffmpegHandler.cancelTask(taskId);

        // 从任务列表中移除
        this.downloadTasks.delete(taskId);
      }

      // 更新任务状态
      downloadStore.updateTask(taskId, {
        status: DOWNLOAD_STATUS_ENUM.Cancelled,
      });

      // 通知前端
      this.sendProgress(taskId, DOWNLOAD_STATUS_ENUM.Cancelled, {
        message: '下载已取消',
      });

      // 清理临时目录
      const settings = await Downloader.loadSettings();
      await this.cleanupTempDirectory(taskId, settings, true);

      console.log(`任务 ${taskId} 已完全取消并清理`);
    } catch (err) {
      console.error('取消下载任务失败:', err);
      Sentry.captureException('取消下载任务失败:' + err);
      showError(err instanceof Error ? err.message : '取消下载任务失败' + err);
      throw err;
    }
  }

  private getResourcePath(finalFilename: string): string {
    const isDev = process.env.NODE_ENV === 'development';
    const platform = process.platform;

    if (isDev) {
      if (finalFilename.startsWith('ffmpeg')) {
        return path.join(
          __dirname,
          '..',
          'public',
          'bin',
          platform === 'win32' ? 'ffmpeg.exe' : `ffmpeg-${process.arch}`,
        );
      }
      // 开发环境下的 yt-dlp 路径
      return path.join(__dirname, '..', 'public', 'bin', finalFilename);
    }

    // 生产环境路径
    const basePath = app.isPackaged
      ? path.join(process.resourcesPath, 'app.asar.unpacked')
      : path.join(__dirname, '..', '..');

    if (finalFilename.startsWith('ffmpeg')) {
      return path.join(
        basePath,
        'public',
        'bin',
        platform === 'win32' ? 'ffmpeg.exe' : `ffmpeg`,
      );
    } else {
      return path.join(
        basePath,
        'public',
        'bin',
        platform === 'win32' ? 'yt-dlp.exe' : 'yt-dlp',
      );
    }
  }
  // 清理文件名，移除非法字符
  private sanitizeFileName(fileName: string): string {
    return fileName.replace(/[<>:"/\\|?*]/g, '');
  }

  // 清理下载任务的临时目录
  private async cleanupTempDirectory(
    taskId: string,
    settings: Settings,
    isError: boolean = false,
  ): Promise<void> {
    try {
      const tempDir = path.join(
        settings.defaultDownloadPath,
        '.snapany',
        taskId,
      );
      console.log(
        `Downloader: ${isError ? '合并失败，' : ''}清理临时目录:`,
        tempDir,
      );

      // 使用异步的rm方法删除目录
      await fs.promises.rm(tempDir, { recursive: true, force: true });
      console.log('Downloader: 临时目录清理完成');
    } catch (cleanupError) {
      console.error('清理临时目录失败:', cleanupError);
      Sentry.captureException('清理临时目录失败:' + cleanupError);
      // 清理失败不影响下载状态
    }
  }

  // 向渲染进程返回进度信息

  /**
   * 向渲染进程发送下载进度和状态更新
   * @param taskId 下载任务ID
   * @param status 当前状态
   * @param options 其他可选参数，如进度百分比、速度、预计完成时间等
   */
  public async sendProgress(
    taskId: string,
    status: string,
    options: {
      percent?: number;
      speed?: number;
      eta?: string;
      downloadSize?: number;
      message?: string;
      finalFilename?: string;
      finalFilePath?: string;
      [key: string]: unknown;
    } = {},
  ): Promise<void> {
    this.window.webContents.send('download-status-change', {
      taskId,
      status,
      ...options,
    });
  }
}
