import { initialize, trackEvent } from '@aptabase/electron/main';
import { compareVersions } from '@common/utils/version';
import { LANGUAGE_MAP } from '@main/constants/language';
import type { Settings } from '@main/types/settings';
import { UpdateInfo } from '@main/types/update';
import { AuthWindowManager } from '@main/utils/auth-window-manager';
import { CookieManager } from '@main/utils/cookie-manager';
import { getOrCreateDeviceId } from '@main/utils/device-id';
import { DownloadStore } from '@main/utils/download-store';
import { Downloader } from '@main/utils/downloader';
import { initErrorHandler, showError } from '@main/utils/error-handler';
import i18n from '@main/utils/i18n';
import { setupGlobalProxy } from '@main/utils/proxy-setting';
import * as Sentry from '@sentry/electron/main';
import { exec, spawn } from 'child_process';
import * as crypto from 'crypto';
import { app, BrowserWindow, ipcMain, net, session, shell } from 'electron';
import * as fs from 'fs';
import * as path from 'path';
import { promisify } from 'util';

process.env.APP_ROOT = path.join(__dirname, '..');

// 添加环境判断
const isDev = process.env.NODE_ENV === 'development';
// Vite 给插件提供的开发环境 URL
export const VITE_DEV_SERVER_URL = process.env['VITE_DEV_SERVER_URL'];
// 主进程打包后的路径
export const MAIN_DIST = path.join(process.env.APP_ROOT, 'dist-electron');
// 渲染进程打包后的路径
export const RENDERER_DIST = path.join(process.env.APP_ROOT, 'dist');

process.env.VITE_PUBLIC = VITE_DEV_SERVER_URL
  ? path.join(process.env.APP_ROOT, 'public')
  : RENDERER_DIST;

// 修改 Sentry 初始化配置
Sentry.init({
  dsn: 'https://<EMAIL>/4508499640909824',
  // 开发环境禁用
  enabled: !isDev,
});

// 初始化 Aptabase，等待完成
initialize('A-US-**********');

// 修改为异步方式获取设备ID
(async () => {
  const deviceId = await getOrCreateDeviceId();
  trackEvent('应用启动', { deviceId });
})();

let mainWindow: BrowserWindow | null;
let downloader: Downloader | null = null;

// 在 app.whenReady() 之前添加
app.commandLine.appendSwitch('disable-features', 'AutofillServiceProvider');
app.commandLine.appendSwitch('disable-site-isolation-trials');
app.commandLine.appendSwitch('lang', 'zh-CN');
app.commandLine.appendSwitch('force-encoding', 'UTF-8');

// 在文件顶部添加
process.env.LANG = 'zh_CN.UTF-8';
process.env.PYTHONIOENCODING = 'utf-8';

// 获取ffprobe路径
function getFfprobePath(): string {
  if (isDev) {
    return path.join(
      __dirname,
      '..',
      'public',
      'bin',
      process.platform === 'win32' ? 'ffprobe.exe' : `ffprobe-${process.arch}`,
    );
  }

  // 生产环境路径
  const basePath = app.isPackaged
    ? path.join(process.resourcesPath, 'app.asar.unpacked')
    : path.join(__dirname, '..', '..');

  return path.join(
    basePath,
    'public',
    'bin',
    process.platform === 'win32' ? 'ffprobe.exe' : 'ffprobe',
  );
}

// 获取本地视频文件信息
export async function getLocalVideoInfo(filePath: string): Promise<{
  width?: number;
  height?: number;
  duration?: number;
  fps?: number;
  audioBitrate?: string;
}> {
  const ffprobePath = getFfprobePath();
  const execAsync = promisify(exec);

  try {
    // 使用 ffprobe 命令获取视频信息，以JSON格式输出
    const { stdout } = await execAsync(
      `"${ffprobePath}" -v error -select_streams v:0 -show_entries stream=width,height,r_frame_rate,duration -show_entries format=duration -of json "${filePath}"`,
    ).catch((error) => {
      console.log('获取视频信息时发生错误:', error);
      Sentry.captureException('获取视频信息时发生错误:' + error);

      // 处理错误
      if (error.message.includes('ENOENT')) {
        showError('视频文件不存在，读取信息失败');
      } else {
        showError('获取视频信息失败:' + error);
      }
      throw error;
    });

    // 解析JSON输出
    const info = JSON.parse(stdout);

    // 从视频流中获取宽度、高度和帧率
    let width, height, fps, duration;

    if (info.streams && info.streams.length > 0) {
      const videoStream = info.streams[0];
      width = videoStream.width;
      height = videoStream.height;

      // 解析帧率（格式通常为"分子/分母"）
      if (videoStream.r_frame_rate) {
        const [numerator, denominator] = videoStream.r_frame_rate
          .split('/')
          .map(Number);
        if (denominator && denominator > 0) {
          fps = parseFloat((numerator / denominator).toFixed(2));
        }
      }

      // 优先使用视频流的持续时间，如果没有则使用格式的持续时间
      duration = videoStream.duration
        ? parseFloat(videoStream.duration)
        : undefined;
    }

    // 如果视频流中没有持续时间，则使用格式中的持续时间
    if (!duration && info.format && info.format.duration) {
      duration = parseFloat(info.format.duration);
    }

    // 获取音频比特率（需要单独命令）
    let audioBitrate;
    try {
      const { stdout: audioStdout } = await execAsync(
        `"${ffprobePath}" -v error -select_streams a:0 -show_entries stream=bit_rate -of json "${filePath}"`,
      );

      const audioInfo = JSON.parse(audioStdout);
      if (
        audioInfo.streams &&
        audioInfo.streams.length > 0 &&
        audioInfo.streams[0].bit_rate
      ) {
        // 转换为kbps并添加单位
        const bitrateKbps = Math.round(
          parseInt(audioInfo.streams[0].bit_rate) / 1000,
        );
        audioBitrate = `${bitrateKbps} kbps`;
      }
    } catch (error) {
      console.log('获取音频比特率时发生错误:', error);
      // 音频信息获取失败不影响整体结果
    }

    console.log('解析结果:', { width, height, duration, fps, audioBitrate });

    if (!width || !height) {
      console.warn('无法解析视频尺寸');
    }

    return {
      width,
      height,
      duration,
      fps,
      audioBitrate,
    };
  } catch (error) {
    console.error('获取视频信息时发生错误:', error);
    Sentry.captureException('获取视频信息时发生错误:' + error);
    throw error;
  }
}

// 在创建窗口函数之前添加这个处理器
ipcMain.handle('open-file-location', async (_, taskId: string) => {
  try {
    // 从downloads.json中根据taskId获取finalFilePath
    const downloadStore = DownloadStore.getInstance();
    const task = downloadStore.getTask(taskId);
    const finalFilePath = task?.finalFilePath;

    console.log('尝试打开文件置:', finalFilePath);

    if (!finalFilePath) {
      return {
        success: false,
        error: `文件不存在: ${finalFilePath}`,
      };
    }

    try {
      await fs.promises.access(finalFilePath);
      await shell.showItemInFolder(finalFilePath);
      return { success: true };
    } catch (error) {
      console.error('打开文件夹失败:', error);
      return {
        success: false,
        error: `文件不存在: ${finalFilePath}`,
      };
    }
  } catch (err) {
    if (err instanceof Error && err.message.includes('ENOENT')) {
      Sentry.captureException('视频文件不存在，打开文件夹失败:' + err);
      showError('视频文件不存在，打开文件夹失败');
    } else {
      Sentry.captureException('打开文件夹失败:' + err);
      showError('打开文件夹失败:' + err);
    }
    return {
      success: false,
      error: err instanceof Error ? err.message : '打开文件夹失败',
    };
  }
});

ipcMain.handle('open-path-location', async (_, path: string) => {
  try {
    console.log('尝试打开路径:', path);

    if (!path) {
      return {
        success: false,
        // error: '路径不存在'
      };
    }

    // 检查路径是否存在且是一个目录
    try {
      const stats = await fs.promises.stat(path);
      console.log('路径状态:', stats);
      if (!stats.isDirectory()) {
        return {
          success: false,
          // error: '指定路径不是一个目录'
        };
      }
    } catch (error) {
      console.error('路径检查错误:', error);
      return {
        success: false,
        // error: '路径不存在或无法访问'
      };
    }

    await shell.openPath(path);
    return { success: true };
  } catch (err) {
    if (err instanceof Error && err.message.includes('ENOENT')) {
      Sentry.captureException('打开路径失败: 路径不存在:' + err);
      showError('打开路径失败: 路径不存在');
    } else {
      Sentry.captureException('打开路径失败:' + err);
      showError('打开路径失败:' + err);
    }
    return {
      success: false,
      error: err instanceof Error ? err.message : '打开路径失败',
    };
  }
});

// 获取已下载的文件信息
ipcMain.handle('get-file-info', async (_, filePath: string) => {
  try {
    console.log('获取已下载的文件信息:', filePath);
    // 从文件路径中获取文件名
    const finalFilename = path.basename(filePath);
    // 获取 DownloadStore 实例
    const downloadStore = DownloadStore.getInstance();

    // 查找对应的任务
    const tasks = downloadStore.getAllTasks();
    const task = tasks.find((t) => t.finalFilename === finalFilename);

    if (task && task.videoInfo.formats && task.videoInfo.formats.length > 0) {
      // 如果任务存在且已有格式信息，直接使用存储的信息
      const format = task.videoInfo.formats[0];
      return {
        title: task.videoInfo.title,
        resolutionWidth: format.resolutionWidth || 'Unknown',
        resolutionHeight: format.resolutionHeight || 'Unknown',
        filesize: format.filesize,
        format: format.ext,
        fps: format.fps ? format.fps.toFixed(2) : 'Unknown',
        duration: task.videoInfo.duration || 0,
        audioBitrate: format.audioBitrate || 'Unknown',
      };
    } else {
      // 如果没有存储的信息，才获取本地文件信息
      const localVideoInfo = await getLocalVideoInfo(filePath);
      const fileStats = await fs.promises.stat(filePath);

      // 更新任务信息（如果任务存在）
      if (task) {
        downloadStore.updateTask(task.id, {
          videoInfo: {
            ...task.videoInfo,
            ...(localVideoInfo.duration && {
              duration: localVideoInfo.duration,
            }),
            formats: [
              {
                format_id: 'local',
                ext: path.extname(filePath).slice(1),
                resolutionWidth: localVideoInfo.width,
                resolutionHeight: localVideoInfo.height,
                filesize: fileStats.size,
                duration: localVideoInfo.duration || 0,
                vcodec: 'unknown',
                acodec: 'unknown',
                fps: localVideoInfo.fps || 0,
                audioBitrate: localVideoInfo.audioBitrate || 'Unknown',
              },
            ],
          },
        });
      }

      return {
        title:
          task?.videoInfo.title ||
          path.basename(filePath, path.extname(filePath)),
        resolutionWidth: localVideoInfo.width,
        resolutionHeight: localVideoInfo.height,
        filesize: fileStats.size,
        format: path.extname(filePath).slice(1),
        fps: localVideoInfo.fps ? localVideoInfo.fps.toFixed(2) : 'Unknown',
        duration: localVideoInfo.duration || 0,
        audioBitrate: localVideoInfo.audioBitrate || 'Unknown',
      };
    }
  } catch (err) {
    if (err instanceof Error && err.message.includes('ENOENT')) {
      Sentry.captureException('视频文件不存在，获取文件信息失败:' + err);
      showError('视频文件不存在，获取文件信息失败');
    } else {
      Sentry.captureException('获取文件信息失败:' + err);
      showError('获取文件信息失败:' + err);
    }
    throw err;
  }
});

// 获取应用版本号
ipcMain.handle('get-app-version', () => {
  return app.getVersion();
});

// 获取系统语言
export function getSystemLanguage(): string {
  const langs = app.getPreferredSystemLanguages();
  console.log('系统语言:', langs);

  let locale: string;

  for (const lang of langs) {
    locale = lang.split('-')[0];

    // 特殊处理中文
    if (locale.startsWith('zh')) {
      locale = lang.includes('Hant') ? 'zh-Hant' : 'zh-Hans';
    }

    const mappedLocale = LANGUAGE_MAP[locale];
    if (mappedLocale) {
      return mappedLocale;
    }
  }

  // 兜底
  return 'en';
}

// 在 app ready 时设置初始代理
app.whenReady().then(async () => {
  try {
    const settings = await loadSettings();

    if (settings.proxy) {
      await setupGlobalProxy(settings.proxy);
    }
  } catch (error) {
    console.error('代理设置失败:', error);
    Sentry.captureException('代理设置失败:' + error);
  }

  // 创建窗口
  createWindow();

  // 初始化时，如果没有language设置，则使用system
  const settingsPath = path.join(app.getPath('userData'), 'settings.json');
  try {
    await fs.promises.access(settingsPath);
  } catch (error) {
    console.error('初始化设置失败:', error);
    // 文件不存在，创建默认设置
    const defaultSettings = {
      language: 'system',
    };
    await fs.promises.writeFile(
      settingsPath,
      JSON.stringify(defaultSettings, null, 2),
    );
  }
});

function createWindow() {
  // 修改 session 配置
  const ses = session.defaultSession;
  ses.clearStorageData({
    storages: ['serviceworkers', 'localstorage', 'websql'],
  });

  mainWindow = new BrowserWindow({
    width: 1000,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    autoHideMenuBar: true,
    frame: true,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js'),
      webSecurity: true,
      devTools: true, // 修改为始终允许devTools
      defaultEncoding: 'UTF-8',
      backgroundThrottling: false,
      javascript: true, // 确保启用 JavaScript
      webgl: true, // 启用 WebGL
      plugins: true, // 启用插件
      experimentalFeatures: true,
      sandbox: false,
      // 移除其他可能影响的限制
      spellcheck: false,
      enableWebSQL: false,
      autoplayPolicy: 'no-user-gesture-required',
    },
  });

  // 设置更现代的 User-Agent
  mainWindow.webContents.setUserAgent(
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
  );

  // if (!isDev) {
  //   mainWindow.removeMenu();
  // }

  // 只在开发环境添加 DevTools 相关代码
  if (isDev) {
    mainWindow.webContents.on('devtools-opened', () => {
      mainWindow?.webContents.executeJavaScript(`
                if (window.localStorage) {
                    window.localStorage.setItem('devtools.preferences', JSON.stringify({
                        'console.verbose': false,
                        'console.debug': false
                    }));
                }
            `);
    });
  }

  // 初始化下载器
  if (mainWindow) {
    console.log('Initializing downloader...');
    downloader = Downloader.getInstance(mainWindow);
    console.log('Downloader initialized');
  }

  if (isDev) {
    console.log('Development mode');
    mainWindow.loadURL(VITE_DEV_SERVER_URL);
  } else {
    console.log('Production mode');
    const rendererPath = path.join(RENDERER_DIST, 'index.html');
    console.log('Loading renderer from:', rendererPath);
    mainWindow.loadFile(rendererPath);
  }

  // 添加加载错误处理
  mainWindow.webContents.on(
    'did-fail-load',
    (event, errorCode, errorDescription) => {
      console.error('Failed to load:', errorCode, errorDescription);
    },
  );

  // 添加页面加载完成的日志
  mainWindow.webContents.on('did-finish-load', () => {
    console.log('页面加载成功');
  });

  mainWindow.on('closed', () => {
    mainWindow = null;
    downloader = null;
    app.quit();
  });

  // 在创建窗口时添加
  mainWindow.webContents.on('devtools-opened', () => {
    // 禁用控制台警告
    mainWindow?.webContents.executeJavaScript(`
            console.warn = () => {};
        `);
  });

  // 处理图片请求
  ipcMain.handle(
    'fetch-image',
    async (_, url: string, headers?: Record<string, string>) => {
      try {
        // 生成图片的 MD5 文件名
        const imageMD5 = getMD5(url);
        // 缓存目录路径
        const cacheDir = path.join(
          app.getPath('temp'),
          app.getName(),
          'images',
        );
        const cacheFile = path.join(cacheDir, imageMD5);

        console.log('缓存目录:', cacheDir);
        console.log('文件名:', imageMD5);
        console.log('请求URL:', url);

        // 确保缓存目录存在
        await ensureCacheDir(cacheDir);

        // 检查缓存是否存在且大小大于1字节
        let cacheExists = false;
        try {
          await fs.promises.access(cacheFile);
          cacheExists = true;
          console.log('缓存文件: 存在');
        } catch {
          console.log('缓存文件: 不存在');
        }

        if (cacheExists) {
          const stats = await fs.promises.stat(cacheFile);
          if (stats.size > 1) {
            console.log('从缓存加载图片:', url);
            const imageBuffer = await fs.promises.readFile(cacheFile);
            return {
              success: true,
              dataBase64: imageBuffer.toString('base64'),
            };
          } else {
            console.log('缓存文件大小异常，重新下载:', url);
            // 删除无效的缓存文件
            await fs.promises.unlink(cacheFile);
          }
        }
        console.log('从网络加载图片:', url);
        const defaultHeaders: Record<string, string> = {
          'User-Agent':
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
          Accept: 'image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
          'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
        };

        // 合并请求头
        const finalHeaders = { ...defaultHeaders, ...(headers || {}) };

        // 使用 net.request 发起请求
        const buffer = await new Promise<Buffer>((resolve, reject) => {
          const request = net.request({
            method: 'GET',
            url: url,
            headers: finalHeaders,
            session: session.defaultSession,
            referrerPolicy: 'unsafe-url',
          });

          const chunks: Buffer[] = [];
          request.on('response', (response) => {
            if (response.statusCode !== 200) {
              reject(new Error(`请求失败，状态码: ${response.statusCode}`));
              return;
            }

            response.on('data', (chunk) => chunks.push(Buffer.from(chunk)));
            response.on('end', () => resolve(Buffer.concat(chunks)));
            response.on('error', reject);
          });
          request.on('error', reject);
          request.end();
        });

        // 保存到缓存并返回
        await fs.promises.writeFile(cacheFile, buffer);
        return {
          success: true,
          dataBase64: buffer.toString('base64'),
        };
      } catch (error) {
        console.error('获取图片失败:', error);
        Sentry.captureException('获取图片失败:' + error);
        return {
          success: false,
          error: error instanceof Error ? error.message : '获取图片失败',
        };
      }
    },
  );

  // 添加全局错误处理
  process.on('uncaughtException', async (error) => {
    console.error('未捕获异常:', error);
    Sentry.captureException('未捕获异常:' + error);
    showError(error instanceof Error ? error.message : '未捕获异常' + error);
  });

  process.on('unhandledRejection', async (reason) => {
    console.error('未处理异常:', reason);
    Sentry.captureException('未处理异常:' + reason);
    const error = reason instanceof Error ? reason : new Error(String(reason));
    showError('未处理异常:' + error);
  });

  initErrorHandler(mainWindow);

  // 添加cookie处理程序
  ipcMain.handle('update-browser-cookies', async (_, browser) => {
    try {
      const cookieManager = CookieManager.getInstance();
      const success = await cookieManager.readBrowserCookies(browser);
      return { success };
    } catch (error) {
      if (error instanceof Error && error.message.includes('ENOENT')) {
        return { success: false, error: '浏览器Cookie文件不存在' };
      } else {
        return { success: false, error: '更新Cookie失败' };
      }
    }
  });

  // 添加授权窗口处理程序
  ipcMain.handle(
    'open-auth-window',
    async (_, url: string, siteKey: string) => {
      try {
        console.log('open-auth-window', url);
        const authManager = AuthWindowManager.getInstance();
        await authManager.openAuthWindow(url, siteKey);
        return { success: true };
      } catch (error) {
        console.log('open-auth-window error:', error);

        // 用户取消登录，不需要报错
        if (
          error instanceof Error &&
          error.message === i18n.t('auth.cancelLogin')
        ) {
          return { success: false, error: i18n.t('auth.cancelLogin') };
        }

        // 对象已销毁错误
        if (
          error instanceof Error &&
          error.message.includes('Object has been destroyed')
        ) {
          Sentry.captureException('授权窗口状态错误:' + error);
          return { success: false, error: '请稍后重试' };
        }

        // 其他错误
        Sentry.captureException('授权窗口打开失败:' + error);
        if (error instanceof Error && error.message.includes('ENOENT')) {
          return { success: false, error: '授权窗口打开失败' };
        } else {
          return { success: false, error: '授权失败：' + error };
        }
      }
    },
  );

  ipcMain.handle('remove-auth', async (_, url: string) => {
    try {
      await CookieManager.getInstance().removeSiteCookies(url);
      return { success: true };
    } catch (error) {
      Sentry.captureException('移除授权失败:' + error);
      return { success: false, error: '移除授权失败' };
    }
  });

  ipcMain.handle('check-auth-status', async (_, url: string) => {
    const cookiesFilePath = path.join(app.getPath('userData'), 'cookies.txt');
    try {
      await fs.promises.access(cookiesFilePath);
      const content = await fs.promises.readFile(cookiesFilePath, 'utf8');
      const domain = new URL(url).hostname;
      return content.includes(domain);
    } catch (error) {
      console.error('检查授权状态失败:', error);
      return false;
    }
  });

  ipcMain.handle('get-saved-sites', async () => {
    return await CookieManager.getInstance().getSavedSites();
  });

  ipcMain.handle('save-sites', async (_, sites) => {
    await CookieManager.getInstance().saveSites(sites);
    return { success: true };
  });

  // 先移除所有现有的处理程序
  ipcMain.removeHandler('get-download-tasks');
  ipcMain.removeHandler('clear-json-tasks');
  ipcMain.removeHandler('get-download-task');
  ipcMain.removeHandler('saveDownloadTask');

  // 然后重新注册处理程序
  ipcMain.handle('get-download-tasks', () => {
    const downloadStore = DownloadStore.getInstance();
    return downloadStore.getAllTasks();
  });

  ipcMain.handle('clear-json-tasks', async (_, taskId: string) => {
    try {
      await DownloadStore.getInstance().clearJsonTasks(taskId);
      return { success: true };
    } catch (error) {
      Sentry.captureException('删除任务失败:' + error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '删除任务失败',
      };
    }
  });

  ipcMain.handle('save-download-task', async (_, task) => {
    try {
      const downloadStore = DownloadStore.getInstance();
      await downloadStore.addTask(
        task.id,
        task.url,
        task.tempFilename,
        task.finalFilename,
        task.title,
        task.format,
        task.quality,
        task.status,
        task.command,
      );
      return { success: true };
    } catch (error) {
      console.error('保存下载任务失败:', error);
      Sentry.captureException('保存下载任务失败:' + error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '保存失败',
      };
    }
  });

  // 添加下载器初始化事件追踪
  if (!downloader) {
    downloader = Downloader.getInstance(mainWindow);
  }

  const authManager = AuthWindowManager.getInstance();
  authManager.setMainWindow(mainWindow);

  // 添加语言切换的 IPC 处理
  ipcMain.handle('change-language', async (_, lang: string) => {
    try {
      const success = i18n.changeLanguage(lang);
      if (success) {
        return { success: true };
      }
      return { success: false, error: '切换语言失败' };
    } catch (error) {
      console.error('切换语言失败:', error);
      Sentry.captureException('切换语言失败:' + error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '切换语言失败',
      };
    }
  });

  ipcMain.handle('get-system-language', () => {
    return getSystemLanguage();
  });
}

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (mainWindow === null) {
    createWindow();
  }
});

// 在外部浏览器打开链接
ipcMain.handle('open-external', async (_, url: string) => {
  try {
    await shell.openExternal(url);
    return { success: true };
  } catch (error) {
    Sentry.captureException('打开链接失败:' + error);
    return {
      success: false,
      error: error instanceof Error ? error.message : '打开链接失败',
    };
  }
});

ipcMain.handle('get-path', (_, name: string) => {
  return app.getPath(name as never);
});

// 添加 loadSettings 函数
async function loadSettings(): Promise<Settings> {
  const defaultSettings: Settings = {
    defaultDownloadPath: app.getPath('downloads'),
    downloadType: 'video',
    downloadPlatform: 'windows',
    downloadTypeVideo: {
      quality: '1080',
      format: 'mp4',
      subtitle: ['none'],
      audioChange: ['default'],
    },
    downloadTypeAudio: {
      quality: 'best',
      format: 'mp3',
    },
    thumbnail: false,
    subtitleType: 'embedded',
    proxy: {
      type: 'SYSTEM',
      host: '',
      port: '',
      username: '',
      password: '',
    },
    language: 'zh',
  };

  try {
    const userDataPath = app.getPath('userData');
    const settingsPath = path.join(userDataPath, 'settings.json');

    try {
      await fs.promises.access(settingsPath);
      const data = await fs.promises.readFile(settingsPath, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      console.error('加载设置失败:', error);
      Sentry.captureException('加载设置失败:' + error);
      return defaultSettings;
    }
  } catch (error) {
    console.error('加载设置失败:', error);
    Sentry.captureException('加载设置失败:' + error);
    return defaultSettings;
  }
}

// 添加一个辅助函数来生成 MD5
function getMD5(str: string): string {
  return crypto.createHash('md5').update(str).digest('hex');
}

// 添加一个辅助函数来确保缓存目录存在
async function ensureCacheDir(dir: string) {
  try {
    await fs.promises.mkdir(dir, { recursive: true });
  } catch (error) {
    console.error('创建缓存目录失败:', error);
    Sentry.captureException('创建缓存目录失败:' + error);
    throw error;
  }
}

// 检查更新方法
ipcMain.handle('check-for-updates', async () => {
  try {
    const response = await fetch('https://snapany.com/api/desktop/info');
    /*
        返回数据示例
        {
            "latestVersion": "0.4.2",
            "normalUpgradeVersion": "0.4.0",
            "forcedUpgradeVersion": "0.2.0",
            "downloadUrls": {
                "windows": "https://files.smilelikeyou.com/SnapAny_0.4.2_x64.exe",
                "macAppleSilicon": "https://files.smilelikeyou.com/SnapAny_0.4.2_arm64.dmg",
                "macIntel": "https://files.smilelikeyou.com/SnapAny_0.4.2_x64.dmg"
            },
            "upgradeContent": {
                "en": "Fix some issues and improve performance.",
                "zh": "修复了若干问题，提升了性能。"
            }
        }
        */
    const updateInfo = await response.json();
    const currentVersion = app.getVersion();

    // 获取当前语言

    // 获取更新内容，如果当前语言的内容不存在则使用英文
    const upgradeContent = updateInfo.upgradeContent;

    // 如果当前版本<=强制更新版本，则强制更新
    if (compareVersions(currentVersion, updateInfo.forcedUpgradeVersion) <= 0) {
      return {
        hasUpdate: true,
        version: updateInfo.latestVersion,
        updateType: 'force',
        upgradeContent,
        downloadUrl: getDownloadUrl(updateInfo),
      };
    }
    // 如果当前版本<=建议更新版本，则提示更新
    else if (
      compareVersions(currentVersion, updateInfo.normalUpgradeVersion) <= 0
    ) {
      return {
        hasUpdate: true,
        version: updateInfo.latestVersion,
        updateType: 'optional',
        upgradeContent,
        downloadUrl: getDownloadUrl(updateInfo),
      };
    }

    return {
      hasUpdate: false,
      version: updateInfo.latestVersion,
      updateType: 'optional',
      upgradeContent,
      downloadUrl: getDownloadUrl(updateInfo),
    };
  } catch (error) {
    console.error('检查更新失败:', error);
    Sentry.captureException('检查更新失败:' + error);
    return {
      hasUpdate: false,
      error: error instanceof Error ? error.message : '检查更新失败',
    };
  }
});

// 添加获取下载链接函数
function getDownloadUrl(updateInfo: UpdateInfo): string {
  if (process.platform === 'darwin') {
    // 检查是否为 Apple Silicon
    const isArm64 = process.arch === 'arm64';
    return isArm64
      ? updateInfo.downloadUrls.macAppleSilicon
      : updateInfo.downloadUrls.macIntel;
  } else if (process.platform === 'win32') {
    return updateInfo.downloadUrls.windows;
  }
  throw new Error('不支持的操作系统');
}

// 添加下载更新的方法
ipcMain.handle('download-update', async (_, downloadUrl: string) => {
  try {
    const tempDir = path.join(app.getPath('temp'), 'updates');
    try {
      await fs.promises.access(tempDir);
    } catch {
      // 目录不存在，创建它
      await fs.promises.mkdir(tempDir, { recursive: true });
    }

    const fileName = path.basename(downloadUrl);
    const tempFilePath = path.join(tempDir, `${fileName}.temp`);
    const finalFilePath = path.join(tempDir, fileName);

    const response = await fetch(downloadUrl);
    const fileSize = parseInt(response.headers.get('content-length') || '0');
    let downloadedSize = 0;

    const fileStream = fs.createWriteStream(tempFilePath);
    const reader = response.body?.getReader();

    if (!reader) {
      throw new Error('无法创建下载流');
    }

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      downloadedSize += value.length;
      fileStream.write(value);

      mainWindow?.webContents.send('update-download-progress', {
        progress: (downloadedSize / fileSize) * 100,
      });
    }

    await new Promise((resolve) => fileStream.end(resolve));

    // 重命名文件
    await fs.promises.rename(tempFilePath, finalFilePath);
    await fs.promises.chmod(finalFilePath, 0o755);

    // 下载完成后发送通知
    mainWindow?.webContents.send('update-downloaded', {
      filePath: finalFilePath,
    });

    return { success: true, filePath: finalFilePath };
  } catch (error) {
    console.error('下载更新失败:', error);
    Sentry.captureException('下载更新失败:' + error);
    return {
      success: false,
      error: error instanceof Error ? error.message : '下载更新失败',
    };
  }
});

// 修改退出并安装的方法
ipcMain.handle('quit-and-install', async (_, filePath: string) => {
  try {
    console.log('filePath', filePath);
    if (filePath) {
      // 如果有文件路径，则安装更新
      console.log('filePath', filePath);
      if (process.platform === 'darwin') {
        console.log('macOS');
        // macOS
        await shell.openPath(filePath);
        // 等待一小段时间确保安装包被打开
        setTimeout(() => app.quit(), 1000);
      } else if (process.platform === 'win32') {
        console.log('Windows');
        // Windows
        const child = spawn(filePath, [], {
          detached: true,
          stdio: ['ignore', 'ignore', 'ignore'],
        });

        // 等待进程启动
        child.unref();
        setTimeout(() => app.quit(), 1000);
      }
    } else {
      // 如果没有文件路径直接退出
      app.quit();
    }
  } catch (error) {
    console.error('安装更新失败:', error);
    Sentry.captureException('安装更新失败:' + error);
    // 出错时延迟退出，让用户看到错误信息
    setTimeout(() => app.quit(), 2000);
  }
});

// 添加检查本地是否有安装包的方法
ipcMain.handle('check-local-installer', async (_, version: string) => {
  try {
    const tempDir = path.join(app.getPath('temp'), 'updates');
    const installerName = `SnapAny_${version}${
      process.platform === 'darwin'
        ? process.arch === 'arm64'
          ? '_arm64.dmg'
          : '_x64.dmg'
        : '_x64.exe'
    }`;
    const installerPath = path.join(tempDir, installerName);

    let exists = false;
    try {
      await fs.promises.access(installerPath);
      exists = true;
    } catch {
      // 文件不存在
    }

    return {
      exists,
      path: installerPath,
    };
  } catch (error) {
    console.error('检查本地安装包失败:', error);
    Sentry.captureException('检查本地安装包失败:' + error);
    return { exists: false };
  }
});
