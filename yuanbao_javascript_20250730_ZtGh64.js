const https = require('https');
const { URL } = require('url');

// 目标URL（包含长参数）
const videoUrl = 'https://upos-sz-estghw.bilivideo.com/upgcxcode/21/01/1071420121/1071420121-1-100050.m4s?e=ig8euxZM2rNcNbdlhoNvNC8BqJIzNbfqXBvEqxTEto8BTrNvN0GvT90W5JZMkX_YN0MvXg8gNEV4NC8xNEV4N03eN0B5tZlqNxTEto8BTrNvNeZVuJ10Kj_g2UB02J0mN0B5tZlqNCNEto8BTrNvNC7MTX502C8f2jmMQJ6mqF2fka1mqx6gqj0eN0B599M=&uipk=5&trid=c1a6dffc27ce4e08ae11b9f4a0a0b7cu&mid=333871969&deadline=1753847008&nbs=1&oi=0x240e039006b76230c8eabe13dcb19b84&gen=playurlv3&os=upos&platform=pc&og=hw&upsig=b13cd949f9fa2e6146dcf88125e0f3b3&uparams=e,uipk,trid,mid,deadline,nbs,oi,gen,os,platform,og&bvc=vod&nettype=0&bw=184914&buvid=CD93FEEB-A73A-7D4E-BCA1-8FF74F72671053947infoc&build=0&dl=0&f=u_0_0&agrr=1&orderid=0,3';

// 解析URL
const parsedUrl = new URL(videoUrl);

// 严格按照提供的请求头配置
const headers = {
  'accept': '*/*',
  'accept-language': 'zh,zh-CN;q=0.9,en;q=0.8,es;q=0.7',
  'cache-control': 'no-cache',
  'pragma': 'no-cache',
  'priority': 'u=1, i',
  'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
  'sec-ch-ua-mobile': '?0',
  'sec-ch-ua-platform': '"Windows"',
  'sec-fetch-dest': 'empty',
  'sec-fetch-mode': 'cors',
  'sec-fetch-site': 'cross-site',
  'origin':'https://www.bilibili.com',
  'referer': 'https://www.bilibili.com/video/BV1Ps4y1J7Ve/?spm_id_from=333.788.player.switch&vd_source=8691ef0eefa4f9b42baa2bbbb705ed75&p=22',
  // 注意：Node.js会自动添加host头，无需手动设置
};

// 请求选项
const options = {
  hostname: parsedUrl.hostname,
  path: parsedUrl.pathname + parsedUrl.search,
  method: 'GET',
  headers: headers,
  // 重要配置
  timeout: 10000, // 10秒超时
  agent: new https.Agent({ 
    keepAlive: true, // 保持连接
    rejectUnauthorized: true // 验证SSL证书
  })
};

console.log('正在发送请求到B站CDN...');
const startTime = Date.now();

const req = https.request(options, (res) => {
  const responseTime = Date.now() - startTime;
  
  console.log('\n====== 响应信息 ======');
  console.log(`状态码: ${res.statusCode} (耗时: ${responseTime}ms)`);
  console.log('响应头:', JSON.stringify(res.headers, null, 2));
  
  // 处理分块数据（视频流）
  let contentLength = parseInt(res.headers['content-length']) || 0;
  let receivedLength = 0;
  let chunks = [];
  
  res.on('data', (chunk) => {
    chunks.push(chunk);
    receivedLength += chunk.length;
    console.log(`接收数据: ${chunk.length}字节 | 进度: ${(receivedLength/contentLength*100).toFixed(1)}%`);
  });
  
  res.on('end', () => {
    console.log('\n====== 下载完成 ======');
    console.log(`总大小: ${receivedLength}字节`);
    
    // 合并Buffer（实际使用时建议流式写入文件）
    const videoData = Buffer.concat(chunks);
    console.log(`最终Buffer大小: ${videoData.length}字节`);
    
    // 此处可写入文件
    // const fs = require('fs');
    // fs.writeFileSync('video.m4s', videoData);
  });
});

// 错误处理
req.on('error', (err) => {
  console.error('\n====== 请求失败 ======');
  console.error('错误代码:', err.code);
  console.error('错误信息:', err.message);
  
  if (err.code === 'ETIMEDOUT') {
    console.error('请求超时，请检查网络');
  } else if (err.code === 'ECONNRESET') {
    console.error('连接被服务器重置，可能触发风控');
  }
});

req.on('timeout', () => {
  console.error('请求超时，强制终止');
  req.destroy(new Error('Request timeout'));
});

// 执行请求
req.end();

// 进程异常捕获
process.on('unhandledRejection', (err) => {
  console.error('未处理的Promise拒绝:', err);
});