import { PROXY_TYPE_ENUM } from '@common/constants/setting';
import { Button, Select, TextInput } from 'flowbite-react';
import { FormEventHandler, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { ApplicationRequireSpan } from '@/client/application-compose';
import { Notice } from '@/components/Notice';
import { useSnapany } from '@/hooks/snapany';
import { validateHost, validatePort } from '@/utils/setting';

function SettingProxyPanel() {
  const { t } = useTranslation();

  const { settings, patchSetting } = useSnapany();

  const proxy = settings?.proxy;
  const { type, host, password, port, username } = proxy ?? {};

  const [proxyType, setProxyType] = useState(type ?? PROXY_TYPE_ENUM.none);

  const mainDisabled = useMemo(
    () => [PROXY_TYPE_ENUM.none, PROXY_TYPE_ENUM.system].includes(proxyType),
    [proxyType],
  );

  const onSubmit: FormEventHandler<HTMLFormElement> = (e) => {
    e.preventDefault();

    if (mainDisabled) {
      patchSetting({
        proxy: {
          type: proxyType,
        },
      }).then(
        () => Notice.success(t('messages.saveSuccess')),
        () => Notice.error(t('messages.saveFailed')),
      );

      return;
    }

    const formData = new FormData(e.currentTarget);

    const host = formData.get('host') as string;
    const port = formData.get('port') as string;
    const username = formData.get('username') as string;
    const password = formData.get('password') as string;

    if (!host) {
      Notice.error(t('settings.proxyInfoMessage.pleaseEnterValidProxyHost'));
      return;
    }

    if (!validateHost(host)) {
      Notice.error(t('settings.proxyInfoMessage.pleaseEnterValidProxyHost'));
      return;
    }

    if (!port) {
      Notice.error(t('settings.proxyInfoMessage.pleaseEnterProxyPort'));
      return;
    }

    if (!validatePort(port)) {
      Notice.error(t('settings.proxyInfoMessage.pleaseEnterValidProxyPort'));
      return;
    }

    patchSetting({
      proxy: {
        type: proxyType,
        host,
        port,
        username,
        password,
      },
    }).then(
      () => Notice.success(t('messages.saveSuccess')),
      () => Notice.error(t('messages.saveFailed')),
    );
  };

  return (
    <form
      className="grid grid-cols-[auto_1fr] gap-x-4.5 gap-y-6 items-center"
      onSubmit={onSubmit}
    >
      <ApplicationRequireSpan required>
        {t('settings.proxyType')}
      </ApplicationRequireSpan>

      <Select
        defaultValue={type ?? proxyType}
        onChange={(e) => setProxyType(e.target.value as PROXY_TYPE_ENUM)}
      >
        <option value={PROXY_TYPE_ENUM.system}>
          {t('settings.usingSystemProxy')}
        </option>
        <option value={PROXY_TYPE_ENUM.http}>HTTP {t('settings.proxy')}</option>
        <option value={PROXY_TYPE_ENUM.socks5}>
          SOCKS5 {t('settings.proxy')}
        </option>
        <option value={PROXY_TYPE_ENUM.none}>
          {t('settings.notUsingProxy')}
        </option>
      </Select>

      <ApplicationRequireSpan required>
        {t('settings.host')}
      </ApplicationRequireSpan>
      <TextInput
        defaultValue={host}
        name="host"
        placeholder="127.0.0.1"
        disabled={mainDisabled}
      />

      <ApplicationRequireSpan required>
        {t('settings.port')}
      </ApplicationRequireSpan>
      <TextInput
        name="port"
        defaultValue={port}
        placeholder="7890"
        disabled={mainDisabled}
      />

      <ApplicationRequireSpan>{t('settings.login')}</ApplicationRequireSpan>
      <TextInput
        defaultValue={username}
        name="username"
        placeholder={t('settings.proxyInfoMessage.optional')}
        disabled={mainDisabled}
      />

      <ApplicationRequireSpan>{t('settings.password')}</ApplicationRequireSpan>
      <TextInput
        defaultValue={password}
        name="password"
        type="password"
        placeholder={t('settings.proxyInfoMessage.optional')}
        disabled={mainDisabled}
      />

      <span />
      <div>
        <Button size="sm" color="blue" type="submit">
          {t('settings.save')}
        </Button>
      </div>
    </form>
  );
}

export default SettingProxyPanel;
