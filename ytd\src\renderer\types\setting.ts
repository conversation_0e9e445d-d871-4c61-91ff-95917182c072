import { AudioFormat, VideoFormat } from '@/store/download';
import { Platform } from '@/utils/environment';

export interface AuthSite {
  name: string;
  url: string;
  authUrl: string;
  isAuthorized: boolean;
  enableDelete?: boolean;
}

export interface HostAlias {
  hostname: string;
  redirect: string;
}

export type FormatPlatformMap = {
  video: Record<Platform, VideoFormat>;
  audio: Record<Platform, AudioFormat>;
};
