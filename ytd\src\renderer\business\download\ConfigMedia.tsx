import { Dropdown } from 'flowbite-react';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import {
  DownloadConfig,
  DownloadConfigShowCurrent,
  DownloadSubDropdown,
} from '@/client/download-compose';
import { AUDIO_TRACK_LANGUAGES, SUBTITLE_LANGUAGES } from '@/constants/media';
import { useSnapany } from '@/hooks/snapany';
import { useDownloadStore } from '@/store/download';

function DownloadConfigMedia() {
  const { t } = useTranslation();

  const {
    subtitleAddOrRemove,
    audioTracksAddOrRemove,
    changeMediaType,
    mediaType,
    hasCover,
    updateHasCover,
    subtitles,
    audioTracks,
    resetAudioTracks,
    resetSubtitles,
    setDefaultAudioTracks,
  } = useDownloadStore();

  const { updateDownloadConfig } = useSnapany();

  const isVideo = useMemo(() => mediaType === 'video', [mediaType]);

  const currentInfo = useMemo(() => {
    return isVideo
      ? t('download.resourceType.video')
      : t('download.resourceType.audio');
  }, [isVideo, t]);

  const bindAfter = useCallback(
    (fn: () => void) => async () => {
      await fn();
      const latestDownloadConfig = useDownloadStore.getState();
      await updateDownloadConfig(latestDownloadConfig);
    },
    [updateDownloadConfig],
  );

  return (
    <Dropdown
      label={
        <DownloadConfigShowCurrent
          label={t('download.download')}
          content={currentInfo}
        />
      }
      inline
      dismissOnClick={false}
      theme={{
        arrowIcon: 'ml-1 text-gray-500',
      }}
    >
      <DownloadConfig
        label={t('download.resourceType.video')}
        showCheck={isVideo}
        onClick={bindAfter(() => changeMediaType('video'))}
      />

      <DownloadConfig
        label={t('download.resourceType.audio')}
        showCheck={!isVideo}
        onClick={bindAfter(() => changeMediaType('audio'))}
      />

      <Dropdown.Divider />

      <DownloadConfig
        label={t('download.thumbnail')}
        showCheck={hasCover}
        onClick={bindAfter(() => updateHasCover())}
      />

      {isVideo && (
        <DownloadSubDropdown label={t('download.subtitles')}>
          <DownloadConfig
            label={t('download.none')}
            showCheck={subtitles === 'none'}
            onClick={bindAfter(() => resetAudioTracks())}
          />

          {SUBTITLE_LANGUAGES.map(({ label, value }) => (
            <DownloadConfig
              showCheck={subtitles.includes(value)}
              key={value}
              label={label}
              onClick={bindAfter(() => subtitleAddOrRemove(value))}
            />
          ))}
        </DownloadSubDropdown>
      )}

      <DownloadSubDropdown label={t('download.audioTracks')}>
        <DownloadConfig
          label={t('download.default')}
          showCheck={audioTracks !== 'all'}
          onClick={bindAfter(() => setDefaultAudioTracks())}
        />
        <DownloadConfig
          label={t('download.allTracks')}
          showCheck={audioTracks === 'all'}
          onClick={bindAfter(() => resetSubtitles())}
        />
        <Dropdown.Divider />

        {AUDIO_TRACK_LANGUAGES.map(({ label, value }) => (
          <DownloadConfig
            key={value}
            label={label}
            showCheck={!!audioTracks?.includes(value)}
            onClick={bindAfter(() => audioTracksAddOrRemove(value))}
          />
        ))}
      </DownloadSubDropdown>
    </Dropdown>
  );
}

export default DownloadConfigMedia;
