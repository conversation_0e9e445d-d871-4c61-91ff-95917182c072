import * as Sentry from '@sentry/electron/main';
import { app } from 'electron';
import * as fs from 'fs';
import * as path from 'path';
import { v4 as uuidv4 } from 'uuid';

// 获取或创建设备 ID 的函数
export async function getOrCreateDeviceId(): Promise<string> {
  const deviceIdPath = path.join(app.getPath('userData'), 'device-id');

  try {
    // 尝试读取现有的设备 ID
    try {
      await fs.promises.access(deviceIdPath);
      console.log('读取现有的设备 ID');
      const deviceId = await fs.promises.readFile(deviceIdPath, 'utf8');
      console.log('现有的设备 ID:', deviceId);
      return deviceId;
    } catch {
      console.log('创建新的设备 ID');
      // 如果不存在，创建新的设备 ID
      const newDeviceId = uuidv4();
      console.log('新的设备 ID:', newDeviceId);
      await fs.promises.writeFile(deviceIdPath, newDeviceId);
      return newDeviceId;
    }
  } catch (error) {
    console.error('获取设备ID失败:', error);
    Sentry.captureException(error);
    // 如果出错，返回一个临时 ID
    return uuidv4();
  }
}
