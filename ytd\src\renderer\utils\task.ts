import { DOWNLOAD_STATUS_ENUM } from '@common/constants/download';
import { RendererFunction } from '@common/types/electron-bridge';
import { v4 as uuidv4 } from 'uuid';

import { TaskStore } from '@/store/task';
import { ReceiveTask } from '@/types/task';

export class TaskIdGenerator {
  private static instance: TaskIdGenerator;

  private constructor() {}

  public static getInstance(): TaskIdGenerator {
    if (!TaskIdGenerator.instance) {
      TaskIdGenerator.instance = new TaskIdGenerator();
    }
    return TaskIdGenerator.instance;
  }

  public async generateId(): Promise<string> {
    // 在渲染进程生成 UUID
    console.log('生成任务ID:', `task-${uuidv4()}`);
    return `task-${uuidv4()}`;
  }
}

export const taskIdGenerator = TaskIdGenerator.getInstance();

const needTaskFetchCache = new Set<string>();

const TASK_START_STATUS = [
  DOWNLOAD_STATUS_ENUM.DownloadingVideo,
  DOWNLOAD_STATUS_ENUM.DownloadingAudio,
];

const TASK_FINAL_STATUS = [
  DOWNLOAD_STATUS_ENUM.Completed,
  DOWNLOAD_STATUS_ENUM.ParseError,
  DOWNLOAD_STATUS_ENUM.DownloadError,
  DOWNLOAD_STATUS_ENUM.ParseSignError,
  DOWNLOAD_STATUS_ENUM.DownloadSignError,
  DOWNLOAD_STATUS_ENUM.MergeError,
];

export const createSyncHandleTaskFactory =
  (
    set: (
      partial: (state: TaskStore) => TaskStore | Partial<TaskStore>,
      replace?: false,
    ) => void,
    get: () => TaskStore,
  ): RendererFunction =>
  (_: unknown, data: ReceiveTask) => {
    // TODO 待改造，前端目前维护唯一已开始状态
    if (
      !needTaskFetchCache.has(data.taskId) &&
      TASK_START_STATUS.includes(data.status)
    ) {
      needTaskFetchCache.add(data.taskId);
      get().fetchTask();
      return;
    }
    // TODO 待改造，同理
    if (TASK_FINAL_STATUS.includes(data.status)) {
      needTaskFetchCache.delete(data.taskId);
      get().fetchTask();
      return;
    }

    set(({ tasks }) => ({
      tasks: tasks.map((item) => {
        if (item.id === data.taskId) {
          return {
            ...item,
            status: data.status ?? item.status,
            progress: data.percent ?? item.progress,
            speed: data.speed ?? item.speed,
            eta: data.eta ?? item.eta,
            downloadSize: data.downloadSize ?? item.downloadSize,
          };
        }
        return item;
      }),
    }));
  };
