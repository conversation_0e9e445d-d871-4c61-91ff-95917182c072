import { Label, Select } from 'flowbite-react';
import { ChangeEvent, useMemo, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { HiFolder } from 'react-icons/hi';

import PathComboBox, { PathComboBoxRef } from '@/components/file/PathComboBox';
import { Notice } from '@/components/Notice';
import { SUPPORTED_LANGUAGES } from '@/constants/language';
import { useSnapany } from '@/hooks/snapany';
import {
  changeLanguage,
  getSystemLanguage,
  openPathLocation,
  selectDirectory,
} from '@/service/render';

const DEFAULT_SYSTEM_VALUE = 'system';

function SettingGeneralPanel() {
  const { t, i18n } = useTranslation();

  const pathComboBoxRef = useRef<PathComboBoxRef>(null);

  // 软件语言选项
  const languageOptions = useMemo(
    () =>
      [{ value: DEFAULT_SYSTEM_VALUE, label: t('settings.system') }].concat(
        SUPPORTED_LANGUAGES,
      ),
    [t],
  );

  const { settings, patchSetting } = useSnapany();

  const languageHandle = async (e: ChangeEvent<HTMLSelectElement>) => {
    const selectValue = e.target.value;

    const lang =
      selectValue === DEFAULT_SYSTEM_VALUE
        ? await getSystemLanguage()
        : selectValue;

    await changeLanguage(selectValue);
    await patchSetting({ language: selectValue });

    i18n.changeLanguage(lang);
  };

  const changeDownloadPath = async () => {
    const result = await selectDirectory();
    if (result.success && result.path) {
      await patchSetting({
        defaultDownloadPath: result.path,
      });
    }
  };

  const validShowPath = async () => {
    pathComboBoxRef.current?.setShowPath(settings?.defaultDownloadPath ?? '');
    Notice.error(t('errors.notImplemented'));
  };

  const openFolder = async () => {
    const path = settings?.defaultDownloadPath;
    if (path) {
      await openPathLocation(path);
    }
  };

  return (
    <div className="grid grid-cols-[auto_1fr] gap-x-[33px] gap-y-[22px] justify-center items-center">
      <Label htmlFor="settingFileUpload" className="text-end">
        {t('settings.saveTo')} :
      </Label>

      <PathComboBox
        id="settingFileUpload"
        handleValidPath={validShowPath}
        path={settings?.defaultDownloadPath}
        handleOpenFolder={openFolder}
        handleChangeDownloadPath={changeDownloadPath}
        ref={pathComboBoxRef}
        rightIcon={HiFolder}
        actionText={t('settings.changeFolderBrowser')}
      />

      <Label htmlFor="settingLanguage" className="text-end">
        {t('settings.language')} :
      </Label>

      <Select
        id="settingLanguage"
        required
        defaultValue={settings?.language}
        onChange={languageHandle}
      >
        {languageOptions.map((option) => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </Select>
    </div>
  );
}

export default SettingGeneralPanel;
