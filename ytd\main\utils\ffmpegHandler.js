"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FFmpegHandler = void 0;
var fluent_ffmpeg_1 = require("fluent-ffmpeg");
var electron_1 = require("electron");
var path = require("path");
var Sentry = require("@sentry/electron/main");
var fs = require("fs");
var FFmpegHandler = /** @class */ (function () {
    function FFmpegHandler() {
        // 获取 ffmpeg 和 ffprobe 路径
        this.ffmpegPath = this.getResourcePath('ffmpeg');
        this.ffprobePath = this.getResourcePath('ffprobe');
        // 设置 ffmpeg 路径
        fluent_ffmpeg_1.default.setFfmpegPath(this.ffmpegPath);
        fluent_ffmpeg_1.default.setFfprobePath(this.ffprobePath);
    }
    FFmpegHandler.prototype.getResourcePath = function (toolName) {
        var isDev = process.env.NODE_ENV === 'development';
        var platform = process.platform;
        var executable = platform === 'win32' ? "".concat(toolName, ".exe") : toolName;
        if (isDev) {
            // 开发环境路径
            return path.join(__dirname, '..', '..', '..', 'public', 'bin', platform === 'win32' ? executable : "".concat(toolName, "-").concat(process.arch));
        }
        // 生产环境路径
        var basePath = electron_1.app.isPackaged
            ? path.join(process.resourcesPath, 'app.asar.unpacked')
            : path.join(__dirname, '..', '..');
        return path.join(basePath, 'public', 'bin', executable);
    };
    FFmpegHandler.getInstance = function () {
        if (!FFmpegHandler.instance) {
            FFmpegHandler.instance = new FFmpegHandler();
        }
        return FFmpegHandler.instance;
    };
    // 获取媒体信息
    FFmpegHandler.prototype.getMediaInfo = function (filePath) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                try {
                    return [2 /*return*/, new Promise(function (resolve, reject) {
                            fluent_ffmpeg_1.default.ffprobe(filePath, function (err, info) {
                                if (err) {
                                    console.error('获取媒体信息失败:', err);
                                    Sentry.captureException('获取媒体信息失败:' + err);
                                    reject(err);
                                    return;
                                }
                                var videoStream = info.streams.find(function (s) { return s.codec_type === 'video'; });
                                var audioStream = info.streams.find(function (s) { return s.codec_type === 'audio'; });
                                resolve({
                                    width: videoStream === null || videoStream === void 0 ? void 0 : videoStream.width,
                                    height: videoStream === null || videoStream === void 0 ? void 0 : videoStream.height,
                                    duration: info.format.duration,
                                    fps: (videoStream === null || videoStream === void 0 ? void 0 : videoStream.r_frame_rate) ? eval(videoStream.r_frame_rate) : undefined,
                                    audioBitrate: audioStream ? "".concat(Math.round(parseInt(audioStream.bit_rate || '0') / 1000), "k") : undefined,
                                    audioCodec: audioStream === null || audioStream === void 0 ? void 0 : audioStream.codec_name,
                                    videoCodec: videoStream === null || videoStream === void 0 ? void 0 : videoStream.codec_name,
                                    streams: info.streams
                                        .filter(function (stream) { return stream.codec_type && stream.codec_name; })
                                        .map(function (stream) { return ({
                                        codec_type: stream.codec_type,
                                        codec_name: stream.codec_name,
                                        width: stream.width,
                                        height: stream.height,
                                        r_frame_rate: stream.r_frame_rate,
                                        channels: stream.channels,
                                        sample_rate: stream.sample_rate,
                                        tags: stream.tags
                                    }); })
                                });
                            });
                        })];
                }
                catch (error) {
                    console.error('获取媒体信息时发生错误:', error);
                    Sentry.captureException('获取媒体信息时发生错误:' + error);
                    throw error;
                }
                return [2 /*return*/];
            });
        });
    };
    // 转换音频格式
    FFmpegHandler.prototype.convertAudio = function (inputPath, outputPath, format, quality, onProgress, onError) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, new Promise(function (resolve, reject) {
                        try {
                            var command = (0, fluent_ffmpeg_1.default)(inputPath);
                            // 设置音频编码器和比特率
                            switch (format) {
                                case 'mp3':
                                    command.audioCodec('libmp3lame')
                                        .audioBitrate(quality);
                                    break;
                                case 'm4a':
                                    command.audioCodec('aac')
                                        .audioBitrate(quality);
                                    break;
                                case 'ogg':
                                    command.audioCodec('libvorbis')
                                        .audioBitrate(quality);
                                    break;
                            }
                            command
                                .on('progress', function (progress) {
                                if (onProgress) {
                                    onProgress(progress);
                                }
                            })
                                .on('end', function () {
                                resolve();
                            })
                                .on('error', function (err) {
                                console.error('音频转换失败:', err);
                                Sentry.captureException('音频转换失败:' + err);
                                if (onError) {
                                    onError(err);
                                }
                                reject(err);
                            })
                                .save(outputPath);
                        }
                        catch (error) {
                            console.error('音频转换时发生错误:', error);
                            Sentry.captureException('音频转换时发生错误:' + error);
                            reject(error);
                        }
                    })];
            });
        });
    };
    // 转换视频格式
    FFmpegHandler.prototype.convertVideo = function (inputPath, outputPath, format, quality, onProgress, onError) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, new Promise(function (resolve, reject) {
                        try {
                            var command = (0, fluent_ffmpeg_1.default)(inputPath);
                            // 设置视频编码器和质量
                            switch (format) {
                                case 'mp4':
                                    command.videoCodec('libx264')
                                        .outputOptions([
                                        '-movflags', '+faststart',
                                        '-c:a', 'aac'
                                    ]);
                                    // 只有在不是原始质量时才设置大小
                                    if (quality !== 'original') {
                                        command.size("?x".concat(quality));
                                    }
                                    break;
                                case 'mkv':
                                    command.videoCodec('libx264');
                                    if (quality !== 'original') {
                                        command.size("?x".concat(quality));
                                    }
                                    break;
                                default:
                                    // 对于其他格式，保持原始编码
                                    command.outputOptions([
                                        '-c:v', 'copy',
                                        '-c:a', 'copy'
                                    ]);
                                    break;
                            }
                            // 保持字幕流
                            command.outputOptions([
                                '-c:s', 'copy'
                            ]);
                            command
                                .on('progress', function (progress) {
                                if (onProgress) {
                                    onProgress(progress);
                                }
                            })
                                .on('end', function () {
                                resolve();
                            })
                                .on('error', function (err) {
                                console.error('视频转换失败:', err);
                                Sentry.captureException('视频转换失败:' + err);
                                if (onError) {
                                    onError(err);
                                }
                                reject(err);
                            })
                                .save(outputPath);
                        }
                        catch (error) {
                            console.error('视频转换时发生错误:', error);
                            Sentry.captureException('视频转换时发生错误:' + error);
                            reject(error);
                        }
                    })];
            });
        });
    };
    // 合并音视频
    FFmpegHandler.prototype.mergeAudioVideo = function (videoPath, audioPath, outputPath, onProgress, onError) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                return [2 /*return*/, new Promise(function (resolve, reject) {
                        try {
                            (0, fluent_ffmpeg_1.default)()
                                .input(videoPath)
                                .input(audioPath)
                                .outputOptions([
                                '-c:v', 'copy',
                                '-c:a', 'aac',
                                '-strict', 'experimental'
                            ])
                                .on('progress', function (progress) {
                                if (onProgress) {
                                    onProgress(progress);
                                }
                            })
                                .on('end', function () {
                                resolve();
                            })
                                .on('error', function (err) {
                                console.error('音视频合并失败:', err);
                                Sentry.captureException('音视频合并失败:' + err);
                                if (onError) {
                                    onError(err);
                                }
                                reject(err);
                            })
                                .save(outputPath);
                        }
                        catch (error) {
                            console.error('音视频合并时发生错误:', error);
                            Sentry.captureException('音视频合并时发生错误:' + error);
                            reject(error);
                        }
                    })];
            });
        });
    };
    FFmpegHandler.prototype.checkStreamCompatibility = function (filePath, targetFormat) {
        return __awaiter(this, void 0, void 0, function () {
            var info, incompatibleStreams;
            var _a;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, this.getMediaInfo(filePath)];
                    case 1:
                        info = _b.sent();
                        incompatibleStreams = [];
                        // 检查编码兼容性
                        (_a = info.streams) === null || _a === void 0 ? void 0 : _a.forEach(function (stream) {
                            if (targetFormat === 'mp4') {
                                if (stream.codec_type === 'video' && !['h264', 'avc1'].some(function (codec) { var _a; return (_a = stream.codec_name) === null || _a === void 0 ? void 0 : _a.includes(codec); })) {
                                    incompatibleStreams.push("video:".concat(stream.codec_name));
                                }
                                else if (stream.codec_type === 'audio' && !['aac', 'mp3'].includes(stream.codec_name || '')) {
                                    incompatibleStreams.push("audio:".concat(stream.codec_name));
                                }
                            }
                            else if (targetFormat === 'mkv') {
                                // MKV 支持几乎所有编码，不需要转码
                                return;
                            }
                            else if (['mp3', 'm4a', 'ogg'].includes(targetFormat)) {
                                if (stream.codec_type === 'audio') {
                                    var compatibleCodecs = {
                                        'mp3': ['mp3', 'libmp3lame'],
                                        'm4a': ['aac'],
                                        'ogg': ['vorbis', 'opus']
                                    };
                                    if (!compatibleCodecs[targetFormat]
                                        .includes(stream.codec_name || '')) {
                                        incompatibleStreams.push("audio:".concat(stream.codec_name));
                                    }
                                }
                            }
                        });
                        return [2 /*return*/, {
                                needsTranscode: incompatibleStreams.length > 0,
                                incompatibleStreams: incompatibleStreams
                            }];
                }
            });
        });
    };
    FFmpegHandler.prototype.isImageFile = function (filePath) {
        return __awaiter(this, void 0, void 0, function () {
            var buffer, fd, signatures, hex_1, error_1;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 4, , 5]);
                        buffer = Buffer.alloc(8);
                        return [4 /*yield*/, fs.promises.open(filePath, 'r')];
                    case 1:
                        fd = _a.sent();
                        return [4 /*yield*/, fd.read(buffer, 0, 8, 0)];
                    case 2:
                        _a.sent();
                        return [4 /*yield*/, fd.close()];
                    case 3:
                        _a.sent();
                        signatures = {
                            // JPEG/JPG
                            'ffd8ff': ['jpg', 'jpeg'],
                            // PNG
                            '89504e47': ['png'],
                            // GIF
                            '47494638': ['gif'],
                            // BMP
                            '424d': ['bmp'],
                            // WEBP
                            '52494646': ['webp'],
                            // TIFF
                            '49492a00': ['tif', 'tiff'],
                            '4d4d002a': ['tif', 'tiff']
                        };
                        hex_1 = buffer.toString('hex').toLowerCase();
                        // 检查是否匹配任何图片格式的标识
                        return [2 /*return*/, Object.keys(signatures).some(function (signature) { return hex_1.startsWith(signature); })];
                    case 4:
                        error_1 = _a.sent();
                        console.error('检查文件类型失败:', error_1);
                        return [2 /*return*/, false];
                    case 5: return [2 /*return*/];
                }
            });
        });
    };
    FFmpegHandler.prototype.processDirectory = function (inputDir, outputFormat, outputFilename, keepSourceFiles, // 是否保留源媒体文件
    keepImages, // 是否保留图片文件
    options, // FFmpeg 选项
    onProgress, onComplete, onError) {
        return __awaiter(this, void 0, void 0, function () {
            var files_2, command_1, isAudioOnly, imageFiles_1, nonMediaFiles_1, allFiles, _i, allFiles_1, file, filePath, stat, _a, files_1, file, compatibility, tempFile, audioCodecs, error_2;
            var _this = this;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        _b.trys.push([0, 18, , 19]);
                        return [4 /*yield*/, this.getMediaFiles(inputDir)];
                    case 1:
                        files_2 = _b.sent();
                        command_1 = (0, fluent_ffmpeg_1.default)();
                        isAudioOnly = ['mp3', 'm4a', 'ogg'].includes(outputFormat);
                        imageFiles_1 = [];
                        nonMediaFiles_1 = [];
                        return [4 /*yield*/, fs.promises.readdir(inputDir)];
                    case 2:
                        allFiles = _b.sent();
                        _i = 0, allFiles_1 = allFiles;
                        _b.label = 3;
                    case 3:
                        if (!(_i < allFiles_1.length)) return [3 /*break*/, 7];
                        file = allFiles_1[_i];
                        filePath = path.join(inputDir, file);
                        return [4 /*yield*/, fs.promises.stat(filePath)];
                    case 4:
                        stat = _b.sent();
                        if (!stat.isFile()) return [3 /*break*/, 6];
                        return [4 /*yield*/, this.isImageFile(filePath)];
                    case 5:
                        if (_b.sent()) {
                            imageFiles_1.push(filePath);
                        }
                        else if (!files_2.includes(filePath)) {
                            nonMediaFiles_1.push(filePath);
                        }
                        _b.label = 6;
                    case 6:
                        _i++;
                        return [3 /*break*/, 3];
                    case 7:
                        _a = 0, files_1 = files_2;
                        _b.label = 8;
                    case 8:
                        if (!(_a < files_1.length)) return [3 /*break*/, 16];
                        file = files_1[_a];
                        return [4 /*yield*/, this.checkStreamCompatibility(file, outputFormat)];
                    case 9:
                        compatibility = _b.sent();
                        if (!compatibility.needsTranscode) return [3 /*break*/, 14];
                        console.log("\u6587\u4EF6 ".concat(path.basename(file), " \u9700\u8981\u8F6C\u7801:"), compatibility.incompatibleStreams);
                        tempFile = path.join(path.dirname(file), "temp_".concat(path.basename(file), ".").concat(outputFormat));
                        if (!isAudioOnly) return [3 /*break*/, 11];
                        return [4 /*yield*/, this.convertAudio(file, tempFile, outputFormat, 'original', onProgress, onError)];
                    case 10:
                        _b.sent();
                        return [3 /*break*/, 13];
                    case 11: return [4 /*yield*/, this.convertVideo(file, tempFile, outputFormat, 'original', onProgress, onError)];
                    case 12:
                        _b.sent();
                        _b.label = 13;
                    case 13:
                        command_1.input(tempFile);
                        return [3 /*break*/, 15];
                    case 14:
                        command_1.input(file);
                        _b.label = 15;
                    case 15:
                        _a++;
                        return [3 /*break*/, 8];
                    case 16:
                        // 设置输出选项
                        if (isAudioOnly) {
                            audioCodecs = {
                                'mp3': 'libmp3lame',
                                'm4a': 'aac',
                                'ogg': 'libvorbis'
                            };
                            command_1.outputOptions(__spreadArray([
                                '-c:a', audioCodecs[outputFormat],
                                '-map', '0:a'
                            ], (files_2.length > 1 ? files_2.slice(1).map(function (_, i) { return ['-map', "".concat(i + 1, ":a")]; }).flat() : []), true));
                            // 添加封面
                            if (imageFiles_1.length > 0) {
                                command_1.input(imageFiles_1[0])
                                    .outputOptions('-map', "".concat(files_2.length, ":v"));
                            }
                        }
                        else {
                            // 视频格式处理
                            command_1.outputOptions(__spreadArray(__spreadArray([
                                '-c:v', outputFormat === 'mp4' ? 'libx264' : 'copy',
                                '-c:a', outputFormat === 'mp4' ? 'aac' : 'copy',
                                '-c:s', outputFormat === 'mp4' ? 'mov_text' : 'copy'
                            ], (outputFormat === 'mp4' ? ['-movflags', '+faststart'] : []), true), files_2.map(function (_, i) { return [
                                "-map",
                                "".concat(i, ":v?"), // 视频流（如果有）
                                "-map",
                                "".concat(i, ":a?"), // 音频流（如果有）
                                "-map",
                                "".concat(i, ":s?") // 字幕流（如果有）
                            ]; }).flat(), true));
                        }
                        // 应用自定义选项
                        if (options) {
                            if (options.inputOptions) {
                                options.inputOptions.forEach(function (opt) { return command_1.inputOption(opt); });
                            }
                            if (options.outputOptions) {
                                options.outputOptions.forEach(function (opt) { return command_1.outputOption(opt); });
                            }
                            if (options.videoCodec)
                                command_1.videoCodec(options.videoCodec);
                            if (options.audioCodec)
                                command_1.audioCodec(options.audioCodec);
                            if (options.videoBitrate)
                                command_1.videoBitrate(options.videoBitrate);
                            if (options.audioBitrate)
                                command_1.audioBitrate(options.audioBitrate);
                            if (options.size)
                                command_1.size(options.size);
                            if (options.fps)
                                command_1.fps(options.fps);
                        }
                        // 监听事件和清理代码保持不变
                        command_1
                            .on('progress', function (progress) {
                            if (onProgress)
                                onProgress(progress);
                        })
                            .on('end', function () { return __awaiter(_this, void 0, void 0, function () {
                            var _i, files_3, file, tempFile, _a, files_4, file, _b, imageFiles_2, file, error_3;
                            return __generator(this, function (_c) {
                                switch (_c.label) {
                                    case 0:
                                        _c.trys.push([0, 13, , 14]);
                                        _i = 0, files_3 = files_2;
                                        _c.label = 1;
                                    case 1:
                                        if (!(_i < files_3.length)) return [3 /*break*/, 4];
                                        file = files_3[_i];
                                        tempFile = path.join(path.dirname(file), "temp_".concat(path.basename(file), ".").concat(outputFormat));
                                        if (!fs.existsSync(tempFile)) return [3 /*break*/, 3];
                                        return [4 /*yield*/, fs.promises.unlink(tempFile)];
                                    case 2:
                                        _c.sent();
                                        _c.label = 3;
                                    case 3:
                                        _i++;
                                        return [3 /*break*/, 1];
                                    case 4:
                                        if (!!keepSourceFiles) return [3 /*break*/, 8];
                                        _a = 0, files_4 = files_2;
                                        _c.label = 5;
                                    case 5:
                                        if (!(_a < files_4.length)) return [3 /*break*/, 8];
                                        file = files_4[_a];
                                        return [4 /*yield*/, fs.promises.unlink(file)];
                                    case 6:
                                        _c.sent();
                                        _c.label = 7;
                                    case 7:
                                        _a++;
                                        return [3 /*break*/, 5];
                                    case 8:
                                        if (!!keepImages) return [3 /*break*/, 12];
                                        _b = 0, imageFiles_2 = imageFiles_1;
                                        _c.label = 9;
                                    case 9:
                                        if (!(_b < imageFiles_2.length)) return [3 /*break*/, 12];
                                        file = imageFiles_2[_b];
                                        return [4 /*yield*/, fs.promises.unlink(file)];
                                    case 10:
                                        _c.sent();
                                        _c.label = 11;
                                    case 11:
                                        _b++;
                                        return [3 /*break*/, 9];
                                    case 12:
                                        // 保留非媒体文件
                                        console.log('保留的非媒体文件:', nonMediaFiles_1);
                                        if (onComplete)
                                            onComplete();
                                        return [3 /*break*/, 14];
                                    case 13:
                                        error_3 = _c.sent();
                                        console.error('清理文件时发生错误:', error_3);
                                        Sentry.captureException('清理文件时发生错误:' + error_3);
                                        return [3 /*break*/, 14];
                                    case 14: return [2 /*return*/];
                                }
                            });
                        }); })
                            .on('error', function (err) {
                            console.error('处理失败:', err);
                            Sentry.captureException('处理失败:' + err);
                            if (onError)
                                onError(err);
                        });
                        // 开始处理
                        return [4 /*yield*/, command_1.save(path.join(path.dirname(inputDir), outputFilename))];
                    case 17:
                        // 开始处理
                        _b.sent();
                        return [3 /*break*/, 19];
                    case 18:
                        error_2 = _b.sent();
                        console.error('处理文件夹时发生错误:', error_2);
                        Sentry.captureException('处理文件夹时发生错误:' + error_2);
                        if (onError) {
                            onError(error_2 instanceof Error ? error_2 : new Error(String(error_2)));
                        }
                        throw error_2;
                    case 19: return [2 /*return*/];
                }
            });
        });
    };
    // 使用自定义参数处理媒体
    FFmpegHandler.prototype.processWithOptions = function (inputs, output, options, onProgress, onComplete, onError) {
        return __awaiter(this, void 0, void 0, function () {
            var command_2, error_4;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 2, , 3]);
                        command_2 = (0, fluent_ffmpeg_1.default)();
                        // 添加输入
                        if (Array.isArray(inputs)) {
                            inputs.forEach(function (input) { return command_2.input(input); });
                        }
                        else {
                            command_2.input(inputs);
                        }
                        // 应用所有选项
                        if (options.inputOptions) {
                            options.inputOptions.forEach(function (opt) { return command_2.inputOption(opt); });
                        }
                        if (options.outputOptions) {
                            options.outputOptions.forEach(function (opt) { return command_2.outputOption(opt); });
                        }
                        if (options.videoCodec)
                            command_2.videoCodec(options.videoCodec);
                        if (options.audioCodec)
                            command_2.audioCodec(options.audioCodec);
                        if (options.videoBitrate)
                            command_2.videoBitrate(options.videoBitrate);
                        if (options.audioBitrate)
                            command_2.audioBitrate(options.audioBitrate);
                        if (options.size)
                            command_2.size(options.size);
                        if (options.fps)
                            command_2.fps(options.fps);
                        // 监听事件
                        command_2
                            .on('progress', function (progress) {
                            if (onProgress) {
                                onProgress(__assign({ percent: progress.percent, currentFps: progress.currentFps, currentKbps: progress.currentKbps, targetSize: progress.targetSize, timemark: progress.timemark }, progress));
                            }
                        })
                            .on('end', function () {
                            if (onComplete) {
                                onComplete();
                            }
                        })
                            .on('error', function (err) {
                            console.error('处理失败:', err);
                            Sentry.captureException('处理失败:' + err);
                            if (onError) {
                                onError(err);
                            }
                        });
                        // 开始处理
                        return [4 /*yield*/, command_2.save(output)];
                    case 1:
                        // 开始处理
                        _a.sent();
                        return [3 /*break*/, 3];
                    case 2:
                        error_4 = _a.sent();
                        console.error('处理时发生错误:', error_4);
                        Sentry.captureException('处理时发生错误:' + error_4);
                        if (onError) {
                            onError(error_4 instanceof Error ? error_4 : new Error(String(error_4)));
                        }
                        throw error_4;
                    case 3: return [2 /*return*/];
                }
            });
        });
    };
    // 获取目录中的有效媒体文件
    FFmpegHandler.prototype.getMediaFiles = function (dir) {
        return __awaiter(this, void 0, void 0, function () {
            var files, validFiles_1, _loop_1, _i, _a, file, error_5;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        _b.trys.push([0, 6, , 7]);
                        return [4 /*yield*/, fs.promises.readdir(dir)];
                    case 1:
                        files = _b.sent();
                        validFiles_1 = [];
                        _loop_1 = function (file) {
                            var filePath, error_6;
                            return __generator(this, function (_c) {
                                switch (_c.label) {
                                    case 0:
                                        filePath = path.join(dir, file);
                                        _c.label = 1;
                                    case 1:
                                        _c.trys.push([1, 3, , 4]);
                                        // 使用 ffprobe 检查文件
                                        return [4 /*yield*/, new Promise(function (resolve, reject) {
                                                fluent_ffmpeg_1.default.ffprobe(filePath, function (err, info) {
                                                    var _a;
                                                    if (err) {
                                                        resolve(null); // 不是媒体文件
                                                        return;
                                                    }
                                                    // 检查是否包含视频、音频或字幕流
                                                    if ((_a = info.streams) === null || _a === void 0 ? void 0 : _a.some(function (stream) {
                                                        return stream.codec_type && ['video', 'audio', 'subtitle'].includes(stream.codec_type);
                                                    })) {
                                                        validFiles_1.push(filePath);
                                                    }
                                                    resolve(null);
                                                });
                                            })];
                                    case 2:
                                        // 使用 ffprobe 检查文件
                                        _c.sent();
                                        return [3 /*break*/, 4];
                                    case 3:
                                        error_6 = _c.sent();
                                        console.log("\u8DF3\u8FC7\u975E\u5A92\u4F53\u6587\u4EF6: ".concat(file));
                                        return [2 /*return*/, "continue"];
                                    case 4: return [2 /*return*/];
                                }
                            });
                        };
                        _i = 0, _a = files.sort(function (a, b) { return a.localeCompare(b); });
                        _b.label = 2;
                    case 2:
                        if (!(_i < _a.length)) return [3 /*break*/, 5];
                        file = _a[_i];
                        return [5 /*yield**/, _loop_1(file)];
                    case 3:
                        _b.sent();
                        _b.label = 4;
                    case 4:
                        _i++;
                        return [3 /*break*/, 2];
                    case 5:
                        if (validFiles_1.length === 0) {
                            throw new Error('目录中没有找到有效的媒体文件');
                        }
                        return [2 /*return*/, validFiles_1];
                    case 6:
                        error_5 = _b.sent();
                        console.error('读取目录失败:', error_5);
                        Sentry.captureException('读取目录失败:' + error_5);
                        throw error_5;
                    case 7: return [2 /*return*/];
                }
            });
        });
    };
    FFmpegHandler.instance = null;
    return FFmpegHandler;
}());
exports.FFmpegHandler = FFmpegHandler;
/*
// 获取 FFmpegHandler 实例
const ffmpeg = FFmpegHandler.getInstance();

// 1. 基本的文件夹处理（合并所有媒体文件）
await ffmpeg.processDirectory(
    '/path/to/input/folder',  // 输入文件夹路径
    'mp4',                    // 输出格式
    'output.mp4',            // 输出文件名
    true,                    // 保留源媒体文件
    true,                    // 保留图片文件
    undefined,               // 默认选项
    (progress) => {
        console.log(`处理进度: ${progress.percent}%`);
        console.log(`当前速度: ${progress.currentFps} fps`);
        console.log(`时间标记: ${progress.timemark}`);
    },
    () => console.log('处理完成'),
    (error) => console.error('处理出错:', error)
);

// 2. 使用自定义选项处理
await ffmpeg.processWithOptions(
    '/path/to/input/folder',  // 输入路径
    '/path/to/output.mp4',    // 输出路径
    {
        // 自定义选项
        outputOptions: [
            '-map', '0:v:0',  // 第一个文件的视频流
            '-map', '0:a:0',  // 第一个文件的音频流
            '-map', '1:s?',   // 第二个文件的字幕流（如果有）
            '-c:v', 'copy',   // 复制视频流
            '-c:a', 'aac',    // 音频转换为 AAC
            '-c:s', 'mov_text' // 字幕格式
        ]
    },
    (progress) => {
        console.log(`处理进度: ${progress.percent}%`);
    },
    () => console.log('处理完成'),
    (error) => console.error('处理出错:', error)
);

// 3. 获取媒体信息
const mediaInfo = await ffmpeg.getMediaInfo('/path/to/file.mp4');
console.log('媒体信息:', mediaInfo);

// 4. 合并音视频
await ffmpeg.mergeAudioVideo(
    '/path/to/video.mp4',
    '/path/to/audio.m4a',
    '/path/to/output.mp4',
    (progress) => console.log(`合并进度: ${progress.percent}%`),
    (error) => console.error('合并失败:', error)
);




*/ 
