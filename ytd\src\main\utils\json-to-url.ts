import { Settings } from '@main/types/settings';

import { Downloader } from './downloader';
/**
 * 表示URL项的接口定义
 */
export interface UrlItem {
  url: string;
  headers?: Record<string, string>;
  info?: Record<string, string>;
}

// 字幕格式接口定义
interface SubtitleFormat {
  ext: string;
  url: string;
  name?: string;
}

/**
 * 将JSON文本解析为URL对象数组
 * @param jsonText - 要解析的JSON文本
 * @returns 包含url和可选headers的对象数组
 */
export async function parseJsonToUrlList(jsonText: string): Promise<UrlItem[]> {
  const urlList: UrlItem[] = [];

  try {
    const parsedJsonData = JSON.parse(jsonText);
    // 读取用户设置
    const userSettings = await Downloader.loadSettings();

    // 获取视频支持的音频语言列表
    const availableLanguages: string[] = Array.from(
      new Set(
        parsedJsonData.formats
          ?.filter(
            (format: AudioFormat) => format.acodec && format.acodec !== 'none',
          )
          ?.filter((format: AudioFormat) => format.language)
          .map((format: AudioFormat) => format.language),
      ),
    );
    console.log('Available audio languages:', availableLanguages);

    // 确定需要下载的音频语言列表
    let targetLanguages: string[] = [];
    const selectedLanguages = userSettings.downloadTypeVideo.audioChange;

    if (selectedLanguages[0] === 'all') {
      targetLanguages = availableLanguages;
    } else {
      // 获取用户选择的语言与可用语言的交集
      targetLanguages = availableLanguages.filter((availableLang) =>
        selectedLanguages.some(
          (selectedLang) =>
            selectedLang !== 'default' &&
            availableLang.toLowerCase().startsWith(selectedLang.toLowerCase()),
        ),
      );
    }

    console.log('Target languages to download:', targetLanguages);

    if (parsedJsonData.direct) {
      if (parsedJsonData.url) {
        const headers = getHttpHeaders(parsedJsonData);
        urlList.push({
          url: parsedJsonData.url,
          ...(Object.keys(headers).length > 0 && { headers }),
        });
      }
    } else {
      // 检查 formats 是否存在且为非空数组
      if (
        !parsedJsonData.formats ||
        !Array.isArray(parsedJsonData.formats) ||
        parsedJsonData.formats.length === 0
      ) {
        return [];
      }

      if (userSettings.downloadType === 'audio') {
        // 遍历formats数组，找到所有有效的音频格式
        const audioFormats = parsedJsonData.formats.filter(
          (format) =>
            (format.acodec && format.acodec !== 'none') || // acodec 存在且不为 "none"
            format.resolution === 'audio only',
        );

        if (audioFormats.length > 0) {
          // 提取符合要求的音频url
          const filterAudio = filterAudioUrls(audioFormats, userSettings);
          const headers = getHttpHeaders(filterAudio);
          urlList.push({
            url: filterAudio.url,
            ...(Object.keys(headers).length > 0 && { headers }),
          });
        } else {
          // 音视频不分离但无音频编码，尝试下载视频提取音频
          const filterVideo = filterVideoUrls(
            parsedJsonData.formats,
            userSettings,
          );
          const headers = getHttpHeaders(filterVideo);
          urlList.push({
            url: filterVideo.url,
            ...(Object.keys(headers).length > 0 && { headers }),
          });
        }
      } else {
        // 遍历formats数组，找到所有有效的视频格式
        const videoFormats = parsedJsonData.formats.filter(
          (format) => format.vcodec && format.vcodec !== 'none',
        );
        if (videoFormats.length > 0) {
          // 筛选到了有视频编码的格式
          const filterVideo = filterVideoUrls(videoFormats, userSettings);

          const headers = getHttpHeaders(parsedJsonData);
          urlList.push({
            url: filterVideo.url,
            ...(Object.keys(headers).length > 0 && { headers }),
          });
          if (targetLanguages.length === 0) {
            if (filterVideo.acodec === 'none') {
              const filterAudio = filterAudioUrls(
                parsedJsonData.formats,
                userSettings,
              );
              const headers = getHttpHeaders(filterAudio);
              urlList.push({
                url: filterAudio.url,
                ...(Object.keys(headers).length > 0 && { headers }),
                info: {
                  language: filterAudio.language,
                },
              });
            }
          } else {
            // 按照availableLanguages分组后每个语言都触发filterAudioUrls
            targetLanguages.forEach((language) => {
              const languageGroup = parsedJsonData.formats.filter(
                (format) => format.language === language,
              );
              const filterAudio = filterAudioUrls(languageGroup, userSettings);
              const headers = getHttpHeaders(filterAudio);
              urlList.push({
                url: filterAudio.url,
                ...(Object.keys(headers).length > 0 && { headers }),
                info: {
                  language: filterAudio.language,
                },
              });
            });
          }
        } else {
          // 没有筛选到有视频编码的格式，则下载音频
          // 过滤出所有acodec不为null，不为none，不为undefined 或resolution: 'audio only'
          const audioFormats = parsedJsonData.formats.filter(
            (format) =>
              (format.acodec && format.acodec !== 'none') ||
              format.resolution === 'audio only',
          );

          if (audioFormats.length > 0) {
            const filterAudio = filterAudioUrls(audioFormats, userSettings);
            const headers = getHttpHeaders(filterAudio);
            urlList.push({
              url: filterAudio.url,
              ...(Object.keys(headers).length > 0 && { headers }),
            });
          } else {
            // 音视频都没有编码信息
            // 过滤出acodec无效的格式
            const acodecInvalidFormats = parsedJsonData.formats.filter(
              (format) => !format.acodec,
            );
            if (acodecInvalidFormats.length > 0) {
              const filterVideo = filterVideoUrls(
                acodecInvalidFormats,
                userSettings,
              );
              const headers = getHttpHeaders(filterVideo);
              urlList.push({
                url: filterVideo.url,
                ...(Object.keys(headers).length > 0 && { headers }),
              });
              if (targetLanguages.length === 0) {
                if (filterVideo.acodec === 'none') {
                  const filterAudio = filterAudioUrls(
                    acodecInvalidFormats,
                    userSettings,
                  );
                  const headers = getHttpHeaders(filterAudio);
                  urlList.push({
                    url: filterAudio.url,
                    ...(Object.keys(headers).length > 0 && { headers }),
                    info: {
                      language: filterAudio.language,
                    },
                  });
                }
              } else {
                // 按照availableLanguages分组后每个语言都触发filterAudioUrls
                targetLanguages.forEach((language) => {
                  const languageGroup = parsedJsonData.formats.filter(
                    (format) => format.language === language,
                  );
                  const filterAudio = filterAudioUrls(
                    languageGroup,
                    userSettings,
                  );
                  const headers = getHttpHeaders(filterAudio);
                  urlList.push({
                    url: filterAudio.url,
                    ...(Object.keys(headers).length > 0 && { headers }),
                    info: {
                      language: language,
                    },
                  });
                });
              }
            } else {
              const videoList: VideoFormat[] = [];
              const audioList: AudioFormat[] = [];
              parsedJsonData.formats.forEach((format) => {
                if (format.vcodec !== 'none' && format.vcodec !== 'none') {
                  if (!format.acodec || format.acodec === 'none') {
                    audioList.push(format);
                  } else {
                    videoList.push(format);
                  }
                }
              });
              if (audioList.length > 0) {
                const filterAudio = filterAudioUrls(audioList, userSettings);
                const headers = getHttpHeaders(filterAudio);
                urlList.push({
                  url: filterAudio.url,
                  ...(Object.keys(headers).length > 0 && { headers }),
                });
              } else if (
                userSettings.downloadType === 'video' &&
                videoList.length > 0
              ) {
                const filterVideo = filterVideoUrls(videoList, userSettings);
                const headers = getHttpHeaders(filterVideo);
                urlList.push({
                  url: filterVideo.url,
                  ...(Object.keys(headers).length > 0 && { headers }),
                });
              }
            }
          }
        }
      }

      // 字幕下载
      if (
        userSettings.downloadType === 'video' &&
        userSettings.downloadTypeVideo.subtitle.length > 0 &&
        userSettings.downloadTypeVideo.subtitle[0] !== 'none'
      ) {
        // 获取用户选择的字幕语言
        const selectedSubtitleLanguages =
          userSettings.downloadTypeVideo.subtitle;

        // 合并手动字幕和自动生成的字幕
        const allSubtitles = {
          ...(parsedJsonData.subtitles || {}),
          ...(parsedJsonData.automatic_captions || {}),
        };

        // 如果存在字幕
        if (Object.keys(allSubtitles).length > 0) {
          // 遍历所有可用的字幕语言
          for (const [subtitleLang, subtitleFormats] of Object.entries(
            allSubtitles,
          )) {
            // 检查该字幕语言是否匹配用户选择的任一语言
            const matchedLanguage = selectedSubtitleLanguages.find(
              (selectedLang) => {
                // 忽略大小写比较
                const selectedLangLower = selectedLang.toLowerCase();
                const subtitleLangLower = subtitleLang.toLowerCase();

                if (selectedLangLower === subtitleLangLower) {
                  // 完全匹配
                  return true;
                } else if (subtitleLangLower.includes('-')) {
                  // 对于带有区域代码的语言（如zh-Hant），检查主要语言部分是否匹配
                  const [mainLang] = subtitleLangLower.split('-');
                  return mainLang === selectedLangLower;
                } else if (selectedLangLower.includes('-')) {
                  // 对于用户选择了特定区域的语言（如zh-Hant），检查是否完全匹配
                  return selectedLangLower === subtitleLangLower;
                }

                return false;
              },
            );

            // 如果找到匹配的语言
            if (matchedLanguage) {
              // 优先选择vtt格式，其次是json3，再次是ttml
              const preferredFormats = [
                'srt',
                'ass',
                'vtt',
                'json3',
                'ttml',
                'srv1',
                'srv2',
                'srv3',
              ];
              let selectedFormat = null;

              for (const format of preferredFormats) {
                selectedFormat = (subtitleFormats as SubtitleFormat[]).find(
                  (f) => f.ext === format,
                );
                if (selectedFormat) break;
              }

              // 如果没有找到首选格式，使用最后一个可用格式
              if (
                !selectedFormat &&
                (subtitleFormats as SubtitleFormat[]).length > 0
              ) {
                selectedFormat = (subtitleFormats as SubtitleFormat[])[
                  (subtitleFormats as SubtitleFormat[]).length - 1
                ];
              }

              // 如果找到了字幕格式，添加到下载列表
              if (selectedFormat) {
                const headers = getHttpHeaders(selectedFormat);
                // 检查URL是否已存在
                if (!isUrlDuplicate(urlList, selectedFormat.url)) {
                  urlList.push({
                    url: selectedFormat.url,
                    ...(Object.keys(headers).length > 0 && { headers }),
                    // 添加info，info为字幕语言
                    info: {
                      language: subtitleLang,
                    },
                  });
                  console.log(
                    `Added subtitle: ${subtitleLang} (${selectedFormat.ext})`,
                  );
                } else {
                  console.log(
                    `Skipped duplicate subtitle: ${subtitleLang} (${selectedFormat.ext})`,
                  );
                }
              }
            }
          }
        } else {
          console.log('No subtitles available');
        }
      }

      // 封面图下载
      if (parsedJsonData.thumbnail) {
        urlList.push({
          url: parsedJsonData.thumbnail,
          // 标识为封面图
          info: {
            type: 'thumbnail',
          },
        });
      }
    }
  } catch (error) {
    console.error('JSON解析失败:', error);
    return [];
  }

  // console.log('urlList:', urlList);

  return urlList;
}

interface AudioFormat {
  vcodec?: string;
  abr?: number;
  acodec?: string;
  language?: string;
  resolution?: string;
  url: string;
  http_headers?: Record<string, string>;
  format_id?: string;
}

interface VideoFormat {
  width?: number;
  height?: number;
  vcodec: string;
  acodec?: string;
  fps?: number;
  filesize?: number;
  filesize_approx?: number;
  url: string;
  http_headers?: Record<string, string>;
  format_id?: string;
}

/**
 * 过滤音频格式
 * @param audioFormats 音频格式
 * @param userSettings 用户设置
 * @returns 过滤后的完整一条音频信息（包含该音频所有的对象）
 */
function filterAudioUrls(
  audioFormats: AudioFormat[],
  userSettings: {
    downloadTypeAudio: {
      format: string;
    };
  },
) {
  // 预筛选：确保只处理真正的音频格式
  const tempAudioFormats = audioFormats.filter(
    (format) => format.acodec && format.acodec !== 'none',
  );

  // 如果筛选后没有任何格式，返回原始数组的最后一个格式
  if (tempAudioFormats.length === 0) {
    console.warn('没有找到有效的音频格式，将使用最后一个可用格式');
    return audioFormats[audioFormats.length - 1];
  }

  // 定义音频编码优先级映射
  const codecPriorityMap = {
    mp3: ['mp3', 'aac', 'mp4a', 'm4a', 'opus'], // mp3格式的优先级
    ogg: ['opus', 'aac', 'mp4a', 'm4a', 'mp3'], // ogg格式的优先级
    m4a: ['aac', 'mp4a', 'm4a', 'mp3', 'opus'], // m4a格式的优先级
  };

  // 获取用户选择的格式，默认为'mp3'
  const userFormat = userSettings.downloadTypeAudio?.format || 'mp3';

  const codecPriorities =
    codecPriorityMap[userFormat] || codecPriorityMap['m4a'];

  let filteredFormats: AudioFormat[] = [];

  // 遍历优先级列表，找到第一个匹配的编码组
  for (const codec of codecPriorities) {
    if (codec === 'aac' || codec === 'mp4a' || codec === 'm4a') {
      // 特殊处理AAC相关编码（aac、mp4a、m4a）
      filteredFormats = audioFormats.filter(
        (format) =>
          format.acodec?.startsWith('aac') ||
          format.acodec?.startsWith('mp4a') ||
          format.acodec?.startsWith('m4a'),
      );
    } else {
      filteredFormats = audioFormats.filter((format) =>
        format.acodec?.startsWith(codec),
      );
    }

    // 如果找到了匹配的编码，则跳出循环
    if (filteredFormats.length > 0) {
      break;
    }
  }

  // 如果没有找到任何匹配的编码，则使用所有音频格式
  if (filteredFormats.length === 0) {
    filteredFormats = audioFormats;
  }

  // 更新audioFormats
  audioFormats = filteredFormats;

  // 根据设置中的比特率过滤，按照format.abr所在范围分组，取分组值最高的一组
  // 320 289+
  // 256 225-288
  // 192 161-224
  // 128 97-160
  // 64 96-
  const abrGroups = audioFormats.reduce((acc, format) => {
    const abr = format.abr;
    if (abr >= 320) {
      acc['320+'] = acc['320+'] || [];
      acc['320+'].push(format);
    } else if (abr >= 256 && abr <= 288) {
      acc['256-288'] = acc['256-288'] || [];
      acc['256-288'].push(format);
    } else if (abr >= 192 && abr <= 224) {
      acc['192-224'] = acc['192-224'] || [];
      acc['192-224'].push(format);
    } else if (abr >= 128 && abr <= 160) {
      acc['128-160'] = acc['128-160'] || [];
      acc['128-160'].push(format);
    } else if (abr >= 64 && abr <= 96) {
      acc['64-96'] = acc['64-96'] || [];
      acc['64-96'].push(format);
    }
    return acc;
  }, {});

  // 取分组值最高的一组，若无分组则取所有
  const highestGroup = Object.keys(abrGroups).sort(
    (a, b) => parseInt(b) - parseInt(a),
  )[0];
  audioFormats = abrGroups[highestGroup] ?? audioFormats;

  // 返回audioFormats中最后一项的url和headers
  const lastFormat = audioFormats[audioFormats.length - 1];
  return lastFormat;
}

/**
 * 过滤视频格式
 * @param videoFormats 视频格式
 * @param userSettings 用户设置
 * @returns 过滤后的完整一条视频信息（包含该视频所有的对象）
 */
function filterVideoUrls(videoFormats: VideoFormat[], userSettings: Settings) {
  let currentFormats = videoFormats;

  // 按照标准分辨率对视频格式进行分组
  const resolutionGroups = currentFormats.reduce((groups, format) => {
    if (format.width || format.height) {
      // 取两个值中较小的一个
      const smaller = Math.min(
        format.width || Number.MAX_SAFE_INTEGER,
        format.height || Number.MAX_SAFE_INTEGER,
      );
      // 计算标准分辨率
      const standardResolution = findClosestResolution(smaller);

      // 将视频格式添加到对应的分辨率组
      groups[standardResolution] = groups[standardResolution] || [];
      groups[standardResolution].push(format);
    }
    return groups;
  }, {});

  // 获取所有可用的分辨率，并按照从高到低排序
  const availableResolutions = Object.keys(resolutionGroups)
    .map(Number)
    .sort((a, b) => b - a);

  // 如果有可用分辨率，则进行分辨率筛选
  if (availableResolutions.length > 0) {
    // 根据用户设置选择合适的分辨率组
    console.log(
      'userSettings-------------------------:',
      userSettings.downloadTypeVideo?.quality,
    );
    const preferredResolution =
      userSettings.downloadTypeVideo?.quality ?? '1080'; // 设置默认值为1080p

    // 将preferredResolution转换为数字
    const numericResolution = Number(preferredResolution);

    // 检查转换结果是否为有效数字
    if (!isNaN(numericResolution) && preferredResolution !== 'best') {
      // 找到与用户选择的目标清晰度最接近的分组
      const resolutionDiffs = availableResolutions.map((res) => ({
        resolution: res,
        diff: Math.abs(res - numericResolution),
      }));

      // 找出最小差值
      const minDiff = Math.min(...resolutionDiffs.map((item) => item.diff));

      // 筛选出具有最小差值的所有分辨率
      const closestResolutions = resolutionDiffs
        .filter((item) => item.diff === minDiff)
        .map((item) => item.resolution);

      // 如果有多个最接近的分辨率，选择较低的一组
      const selectedResolution = Math.min(...closestResolutions);
      console.log('selectedResolution:', selectedResolution);

      // 从选定的组中选择最佳的视频格式
      const selectedGroup = resolutionGroups[selectedResolution];
      if (selectedGroup.length > 0) {
        currentFormats = selectedGroup;
      }
    } else {
      // 如果分辨率设置无效或为'best'，使用最高可用分辨率
      const highestResolution = Math.max(...availableResolutions);
      const selectedGroup = resolutionGroups[highestResolution];
      if (selectedGroup.length > 0) {
        currentFormats = selectedGroup;
      }
    }
  }

  // 定义编码优先级
  const codecPriorities = {
    h264: 1, // avc1, h264
    avc1: 1,
    h265: 2, // h265, hevc, hev1
    hevc: 2,
    hev1: 2,
    vp9: 3, // vp9, vp09
    vp09: 3,
    av01: 4, // av01, av1
    av1: 4,
    unknown: 5, // 所有未知编码和未定义 vcodec 的项
  };

  // 获取编码优先级
  const getCodecPriority = (format: VideoFormat) => {
    const vcodec = (format.vcodec || '').toLowerCase(); // 处理未定义 vcodec 的情况
    if (!vcodec) {
      return codecPriorities.unknown; // 如果 vcodec 未定义，直接归类到 unknown
    }

    // 检查 vcodec 是否以任何已知编码开头
    for (const [codec, priority] of Object.entries(codecPriorities)) {
      if (vcodec.startsWith(codec)) {
        return priority;
      }
    }

    return codecPriorities.unknown; // 如果 vcodec 不以任何已知编码开头，归类到 unknown
  };

  // 按编码优先级对格式进行分组
  const formatsByCodecPriority = currentFormats.reduce((acc, format) => {
    const priority = getCodecPriority(format);
    acc[priority] = acc[priority] || [];
    acc[priority].push(format);
    return acc;
  }, {});

  // 取优先级最高的不为空的组
  const formatsInPriority = Object.keys(formatsByCodecPriority)
    .sort((a, b) => Number(a) - Number(b)) // 按优先级数字升序排序
    .reduce((result, priority) => {
      // 如果还没有找到格式组且当前优先级组不为空，则使用当前组
      if (!result.length && formatsByCodecPriority[priority].length > 0) {
        return formatsByCodecPriority[priority];
      }
      return result;
    }, []);

  // 如果筛选到结果，则更新 currentFormats
  if (formatsInPriority.length > 0) {
    currentFormats = formatsInPriority;
  }

  // 在当前编码优先级组中，优先选择有音频的格式
  const formatsWithAudio = currentFormats.filter(
    (format) => format.acodec && format.acodec !== 'none',
  );
  // 使用有音频的格式(如果存在)，否则使用原始格式
  if (formatsWithAudio.length > 0) {
    currentFormats = formatsWithAudio;
  }

  // 按fps排序并筛选最高fps组成数组
  currentFormats.sort((a, b) => (b.fps || 0) - (a.fps || 0));
  const maxFps = currentFormats[0]?.fps || 0;
  const highestFpsFormats = currentFormats.filter(
    (f) => (f.fps || 0) === maxFps,
  );
  if (highestFpsFormats.length > 0) {
    currentFormats = highestFpsFormats;
  }

  // 按filesize筛选，取最大的值组成数组
  const formatsWithSize = currentFormats.filter((f) => f.filesize != null);
  if (formatsWithSize.length > 0) {
    // 找到最大的 filesize
    const maxSize = Math.max(...formatsWithSize.map((f) => f.filesize));
    // 筛选出所有 filesize 等于最大值的格式
    const maxSizeFormats = formatsWithSize.filter(
      (f) => f.filesize === maxSize,
    );
    if (maxSizeFormats.length > 0) {
      currentFormats = maxSizeFormats;
    }
  } else {
    // 如果没有filesize，尝试使用filesize_approx，取最大的值组成数组
    const formatsWithApproxSize = currentFormats.filter(
      (f) => f.filesize_approx != null,
    );
    if (formatsWithApproxSize.length > 0) {
      // 找到最大的 filesize_approx
      const maxSize = Math.max(
        ...formatsWithApproxSize.map((f) => f.filesize_approx),
      );
      // 筛选出所有 filesize_approx 等于最大值的格式
      const maxSizeFormats = formatsWithApproxSize.filter(
        (f) => f.filesize_approx === maxSize,
      );
      if (maxSizeFormats.length > 0) {
        currentFormats = maxSizeFormats;
      }
    }
  }

  // 返回筛选后的数组中最后一项
  const lastFormat = currentFormats[currentFormats.length - 1];
  return lastFormat;
}

// 查找一个数字最接近哪个标准分辨率
function findClosestResolution(number: number): number {
  // 定义标准分辨率列表
  const standardResolutions = [
    '144', // 144p
    '240', // 240p
    '360', // 360p
    '480', // 480p
    '720', // 720p HD
    '1080', // 1080p HD
    '1440', // 1440p 2K
    '2160', // 2160p 4K
    '4320', // 4320p 8K
  ];

  // 如果数字小于最小分辨率，返回最小分辨率
  if (number <= parseInt(standardResolutions[0])) {
    return parseInt(standardResolutions[0]);
  }

  // 如果数字大于最大分辨率，返回最大分辨率
  if (number >= parseInt(standardResolutions[standardResolutions.length - 1])) {
    return parseInt(standardResolutions[standardResolutions.length - 1]);
  }

  // 找到最接近的分辨率
  let closest = parseInt(standardResolutions[0]);
  let minDiff = Math.abs(number - closest);

  for (const resolution of standardResolutions) {
    const diff = Math.abs(number - parseInt(resolution));
    if (diff < minDiff) {
      minDiff = diff;
      closest = parseInt(resolution);
    }
  }

  return closest;
}

// 为 getHttpHeaders 添加类型
interface JsonData {
  http_headers?: Record<string, string>;
  cookies?: string;
}

export function getHttpHeaders(obj: JsonData): Record<string, string> {
  const headers: Record<string, string> = {};

  // 确保obj存在
  if (!obj) {
    return headers;
  }

  // 如果存在http_headers对象,直接合并
  if (obj.http_headers && typeof obj.http_headers === 'object') {
    Object.assign(headers, obj.http_headers);
  }

  // 处理cookies
  if (obj.cookies) {
    headers.Cookie = handleCookies(obj.cookies);
  }

  return headers;
}

// 处理cookies
function handleCookies(cookies: string) {
  // 将cookies转换为对象
  const cookiesObj = cookies
    .split(';')
    .reduce((acc: Record<string, string>, cookie) => {
      const cookieStr = cookie.trim();
      // 找到第一个等号的位置
      const firstEqualIndex = cookieStr.indexOf('=');
      if (firstEqualIndex === -1) return acc;

      // 分别获取key和value
      const key = cookieStr.slice(0, firstEqualIndex).trim();
      const value = cookieStr.slice(firstEqualIndex + 1).trim();

      if (value) {
        const cleanValue = value.replace(/^["']|["']$/g, '');
        acc[key] = cleanValue;
      }
      return acc;
    }, {});

  // 将对象转换回字符串格式
  return Object.entries(cookiesObj)
    .map(([key, value]) => `${key}=${value}`)
    .join('; ');
}

// 检查url是否重复
function isUrlDuplicate(urlList: UrlItem[], newUrl: string): boolean {
  return urlList.some((item) => item.url === newUrl);
}

// if (require.main === module) {
//   // 从D:\Download\demodownload\测试\tiktok.json
//   const jsonText = fs.readFileSync(
//     'D:\\Download\\demodownload\\测试\\tiktok2.json',
//     'utf8',
//   );
//   parseJsonToUrlList(jsonText);
// }
