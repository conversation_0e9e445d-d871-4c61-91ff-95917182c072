import { BrowserWindow } from 'electron';

let mainWindow: <PERSON><PERSON><PERSON>Window | null = null;

export function initError<PERSON><PERSON>ler(window: BrowserWindow) {
  mainWindow = window;
}

export function showError(error: string | Error) {
  const errorMessage = error instanceof Error ? error.message : error;

  if (mainWindow) {
    mainWindow.webContents.send('show-error', errorMessage);
  }
}

export function handleError(error: unknown, context?: string) {
  if (error instanceof Error) {
    // 处理文件不存在错误
    if (error.message.includes('ENOENT')) {
      showError(`${context ? context + ': ' : ''}文件不存在`);
    } else {
      showError(`${context ? context + ': ' : ''}${error.message}`);
    }
  } else {
    showError(String(error));
  }
}
