{
    "compilerOptions": {
        "composite": true,
        "skipLibCheck": true,
        "module": "ESNext",
        "moduleResolution": "bundler",
        "allowSyntheticDefaultImports": true,
        "baseUrl": ".",
        "paths": {
            "@main/*": [
                "src/main/*"
            ],
            "@common/*": [
                "src/common/*"
            ]
        }
    },
    "include": [
        "vite.config.mts",
        "src/main",
        "src/common",
    ]
}