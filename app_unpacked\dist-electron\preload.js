"use strict";const i=require("electron"),g="8.51.0",p=globalThis;function y(t,e,n){const r=p,s=r.__SENTRY__=r.__SENTRY__||{},o=s[g]=s[g]||{};return o[t]||(o[t]=e())}const B=typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__,W="Sentry Logger ",L=["debug","info","warn","error","log","assert","trace"],x={};function K(t){if(!("console"in p))return t();const e=p.console,n={},r=Object.keys(x);r.forEach(s=>{const o=x[s];n[s]=e[s],e[s]=o});try{return t()}finally{r.forEach(s=>{e[s]=n[s]})}}function j(){let t=!1;const e={enable:()=>{t=!0},disable:()=>{t=!1},isEnabled:()=>t};return B?L.forEach(n=>{e[n]=(...r)=>{t&&K(()=>{p.console[n](`${W}[${n}]:`,...r)})}}):L.forEach(n=>{e[n]=()=>{}}),e}const k=y("logger",j),U=50,h="?",I=/\(error: (.*)\)/,P=/captureMessage|captureException/;function J(...t){const e=t.sort((n,r)=>n[0]-r[0]).map(n=>n[1]);return(n,r=0,s=0)=>{const o=[],a=n.split(`
`);for(let d=r;d<a.length;d++){const c=a[d];if(c.length>1024)continue;const u=I.test(c)?c.replace(I,"$1"):c;if(!u.match(/\S*Error: /)){for(const m of e){const f=m(u);if(f){o.push(f);break}}if(o.length>=U+s)break}}return X(o.slice(s))}}function X(t){if(!t.length)return[];const e=Array.from(t);return/sentryWrapped/.test(v(e).function||"")&&e.pop(),e.reverse(),P.test(v(e).function||"")&&(e.pop(),P.test(v(e).function||"")&&e.pop()),e.slice(0,U).map(n=>({...n,filename:n.filename||v(e).filename,function:n.function||h}))}function v(t){return t[t.length-1]||{}}function F(){return w(p),p}function w(t){const e=t.__SENTRY__=t.__SENTRY__||{};return e.version=e.version||g,e[g]=e[g]||{}}const H=Object.prototype.toString;function z(t,e){return H.call(t)===`[object ${e}]`}function Q(t){return z(t,"Object")}function Z(t){return!!(t&&t.then&&typeof t.then=="function")}function ee(t,e,n){try{Object.defineProperty(t,e,{value:n,writable:!0,configurable:!0})}catch{B&&k.log(`Failed to add non-enumerable property "${e}" to object`,t)}}const q=1e3;function $(){return Date.now()/q}function te(){const{performance:t}=p;if(!t||!t.now)return $;const e=Date.now()-t.now(),n=t.timeOrigin==null?e:t.timeOrigin;return()=>(n+t.now())/q}const ne=te();(()=>{const{performance:t}=p;if(!t||!t.now)return;const e=3600*1e3,n=t.now(),r=Date.now(),s=t.timeOrigin?Math.abs(t.timeOrigin+n-r):e,o=s<e,a=t.timing&&t.timing.navigationStart,c=typeof a=="number"?Math.abs(a+n-r):e,u=c<e;return o||u?s<=c?t.timeOrigin:a:r})();function l(){const t=p,e=t.crypto||t.msCrypto;let n=()=>Math.random()*16;try{if(e&&e.randomUUID)return e.randomUUID().replace(/-/g,"");e&&e.getRandomValues&&(n=()=>{const r=new Uint8Array(1);return e.getRandomValues(r),r[0]})}catch{}return("10000000100040008000"+1e11).replace(/[018]/g,r=>(r^(n()&15)>>r/4).toString(16))}function re(t,e={}){if(e.user&&(!t.ipAddress&&e.user.ip_address&&(t.ipAddress=e.user.ip_address),!t.did&&!e.did&&(t.did=e.user.id||e.user.email||e.user.username)),t.timestamp=e.timestamp||ne(),e.abnormal_mechanism&&(t.abnormal_mechanism=e.abnormal_mechanism),e.ignoreDuration&&(t.ignoreDuration=e.ignoreDuration),e.sid&&(t.sid=e.sid.length===32?e.sid:l()),e.init!==void 0&&(t.init=e.init),!t.did&&e.did&&(t.did=`${e.did}`),typeof e.started=="number"&&(t.started=e.started),t.ignoreDuration)t.duration=void 0;else if(typeof e.duration=="number")t.duration=e.duration;else{const n=t.timestamp-t.started;t.duration=n>=0?n:0}e.release&&(t.release=e.release),e.environment&&(t.environment=e.environment),!t.ipAddress&&e.ipAddress&&(t.ipAddress=e.ipAddress),!t.userAgent&&e.userAgent&&(t.userAgent=e.userAgent),typeof e.errors=="number"&&(t.errors=e.errors),e.status&&(t.status=e.status)}function A(){return l()}function N(){return l().substring(16)}function Y(t,e,n=2){if(!e||typeof e!="object"||n<=0)return e;if(t&&e&&Object.keys(e).length===0)return t;const r={...t};for(const s in e)Object.prototype.hasOwnProperty.call(e,s)&&(r[s]=Y(r[s],e[s],n-1));return r}const E="_sentrySpan";function T(t,e){e?ee(t,E,e):delete t[E]}function D(t){return t[E]}const se=100;class b{constructor(){this._notifyingListeners=!1,this._scopeListeners=[],this._eventProcessors=[],this._breadcrumbs=[],this._attachments=[],this._user={},this._tags={},this._extra={},this._contexts={},this._sdkProcessingMetadata={},this._propagationContext={traceId:A(),spanId:N()}}clone(){const e=new b;return e._breadcrumbs=[...this._breadcrumbs],e._tags={...this._tags},e._extra={...this._extra},e._contexts={...this._contexts},this._contexts.flags&&(e._contexts.flags={values:[...this._contexts.flags.values]}),e._user=this._user,e._level=this._level,e._session=this._session,e._transactionName=this._transactionName,e._fingerprint=this._fingerprint,e._eventProcessors=[...this._eventProcessors],e._requestSession=this._requestSession,e._attachments=[...this._attachments],e._sdkProcessingMetadata={...this._sdkProcessingMetadata},e._propagationContext={...this._propagationContext},e._client=this._client,e._lastEventId=this._lastEventId,T(e,D(this)),e}setClient(e){this._client=e}setLastEventId(e){this._lastEventId=e}getClient(){return this._client}lastEventId(){return this._lastEventId}addScopeListener(e){this._scopeListeners.push(e)}addEventProcessor(e){return this._eventProcessors.push(e),this}setUser(e){return this._user=e||{email:void 0,id:void 0,ip_address:void 0,username:void 0},this._session&&re(this._session,{user:e}),this._notifyScopeListeners(),this}getUser(){return this._user}getRequestSession(){return this._requestSession}setRequestSession(e){return this._requestSession=e,this}setTags(e){return this._tags={...this._tags,...e},this._notifyScopeListeners(),this}setTag(e,n){return this._tags={...this._tags,[e]:n},this._notifyScopeListeners(),this}setExtras(e){return this._extra={...this._extra,...e},this._notifyScopeListeners(),this}setExtra(e,n){return this._extra={...this._extra,[e]:n},this._notifyScopeListeners(),this}setFingerprint(e){return this._fingerprint=e,this._notifyScopeListeners(),this}setLevel(e){return this._level=e,this._notifyScopeListeners(),this}setTransactionName(e){return this._transactionName=e,this._notifyScopeListeners(),this}setContext(e,n){return n===null?delete this._contexts[e]:this._contexts[e]=n,this._notifyScopeListeners(),this}setSession(e){return e?this._session=e:delete this._session,this._notifyScopeListeners(),this}getSession(){return this._session}update(e){if(!e)return this;const n=typeof e=="function"?e(this):e,[r,s]=n instanceof S?[n.getScopeData(),n.getRequestSession()]:Q(n)?[e,e.requestSession]:[],{tags:o,extra:a,user:d,contexts:c,level:u,fingerprint:m=[],propagationContext:f}=r||{};return this._tags={...this._tags,...o},this._extra={...this._extra,...a},this._contexts={...this._contexts,...c},d&&Object.keys(d).length&&(this._user=d),u&&(this._level=u),m.length&&(this._fingerprint=m),f&&(this._propagationContext=f),s&&(this._requestSession=s),this}clear(){return this._breadcrumbs=[],this._tags={},this._extra={},this._user={},this._contexts={},this._level=void 0,this._transactionName=void 0,this._fingerprint=void 0,this._requestSession=void 0,this._session=void 0,T(this,void 0),this._attachments=[],this.setPropagationContext({traceId:A()}),this._notifyScopeListeners(),this}addBreadcrumb(e,n){const r=typeof n=="number"?n:se;if(r<=0)return this;const s={timestamp:$(),...e},o=this._breadcrumbs;return o.push(s),this._breadcrumbs=o.length>r?o.slice(-r):o,this._notifyScopeListeners(),this}getLastBreadcrumb(){return this._breadcrumbs[this._breadcrumbs.length-1]}clearBreadcrumbs(){return this._breadcrumbs=[],this._notifyScopeListeners(),this}addAttachment(e){return this._attachments.push(e),this}clearAttachments(){return this._attachments=[],this}getScopeData(){return{breadcrumbs:this._breadcrumbs,attachments:this._attachments,contexts:this._contexts,tags:this._tags,extra:this._extra,user:this._user,level:this._level,fingerprint:this._fingerprint||[],eventProcessors:this._eventProcessors,propagationContext:this._propagationContext,sdkProcessingMetadata:this._sdkProcessingMetadata,transactionName:this._transactionName,span:D(this)}}setSDKProcessingMetadata(e){return this._sdkProcessingMetadata=Y(this._sdkProcessingMetadata,e,2),this}setPropagationContext(e){return this._propagationContext={spanId:N(),...e},this}getPropagationContext(){return this._propagationContext}captureException(e,n){const r=n&&n.event_id?n.event_id:l();if(!this._client)return k.warn("No client configured on scope - will not capture exception!"),r;const s=new Error("Sentry syntheticException");return this._client.captureException(e,{originalException:e,syntheticException:s,...n,event_id:r},this),r}captureMessage(e,n,r){const s=r&&r.event_id?r.event_id:l();if(!this._client)return k.warn("No client configured on scope - will not capture message!"),s;const o=new Error(e);return this._client.captureMessage(e,n,{originalException:e,syntheticException:o,...r,event_id:s},this),s}captureEvent(e,n){const r=n&&n.event_id?n.event_id:l();return this._client?(this._client.captureEvent(e,{...n,event_id:r},this),r):(k.warn("No client configured on scope - will not capture event!"),r)}_notifyScopeListeners(){this._notifyingListeners||(this._notifyingListeners=!0,this._scopeListeners.forEach(e=>{e(this)}),this._notifyingListeners=!1)}}const S=b;function ie(){return y("defaultCurrentScope",()=>new S)}function oe(){return y("defaultIsolationScope",()=>new S)}class ae{constructor(e,n){let r;e?r=e:r=new S;let s;n?s=n:s=new S,this._stack=[{scope:r}],this._isolationScope=s}withScope(e){const n=this._pushScope();let r;try{r=e(n)}catch(s){throw this._popScope(),s}return Z(r)?r.then(s=>(this._popScope(),s),s=>{throw this._popScope(),s}):(this._popScope(),r)}getClient(){return this.getStackTop().client}getScope(){return this.getStackTop().scope}getIsolationScope(){return this._isolationScope}getStackTop(){return this._stack[this._stack.length-1]}_pushScope(){const e=this.getScope().clone();return this._stack.push({client:this.getClient(),scope:e}),e}_popScope(){return this._stack.length<=1?!1:!!this._stack.pop()}}function _(){const t=F(),e=w(t);return e.stack=e.stack||new ae(ie(),oe())}function ce(t){return _().withScope(t)}function de(t,e){const n=_();return n.withScope(()=>(n.getStackTop().scope=t,e(t)))}function O(t){return _().withScope(()=>t(_().getIsolationScope()))}function ue(){return{withIsolationScope:O,withScope:ce,withSetScope:de,withSetIsolationScope:(t,e)=>O(e),getCurrentScope:()=>_().getScope(),getIsolationScope:()=>_().getIsolationScope()}}function pe(t){const e=w(t);return e.acs?e.acs:ue()}function le(){const t=F();return pe(t).getCurrentScope()}function he(t,e){return le().captureException(t,void 0)}const _e=30,fe=50;function R(t,e,n,r){const s={filename:t,function:e==="<anonymous>"?h:e,in_app:!0};return n!==void 0&&(s.lineno=n),r!==void 0&&(s.colno=r),s}const ge=/^\s*at (\S+?)(?::(\d+))(?::(\d+))\s*$/i,Se=/^\s*at (?:(.+?\)(?: \[.+\])?|.*?) ?\((?:address at )?)?(?:async )?((?:<anonymous>|[-a-z]+:|.*bundle|\/)?.*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,me=/\((\S*)(?::(\d+))(?::(\d+))\)/,ve=t=>{const e=ge.exec(t);if(e){const[,r,s,o]=e;return R(r,h,+s,+o)}const n=Se.exec(t);if(n){if(n[2]&&n[2].indexOf("eval")===0){const a=me.exec(n[2]);a&&(n[2]=a[1],n[3]=a[2],n[4]=a[3])}const[s,o]=V(n[1]||h,n[2]);return R(o,s,n[3]?+n[3]:void 0,n[4]?+n[4]:void 0)}},G=[_e,ve],ke=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)?((?:[-a-z]+)?:\/.*?|\[native code\]|[^@]*(?:bundle|\d+\.js)|\/[\w\-. /=]+)(?::(\d+))?(?::(\d+))?\s*$/i,Ee=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i,Re=t=>{const e=ke.exec(t);if(e){if(e[3]&&e[3].indexOf(" > eval")>-1){const o=Ee.exec(e[3]);o&&(e[1]=e[1]||"eval",e[3]=o[1],e[4]=o[2],e[5]="")}let r=e[3],s=e[1]||h;return[s,r]=V(s,r),R(r,s,e[4]?+e[4]:void 0,e[5]?+e[5]:void 0)}},ye=[fe,Re],we=[G,ye];J(...we);const V=(t,e)=>{const n=t.indexOf("safari-extension")!==-1,r=t.indexOf("safari-web-extension")!==-1;return n||r?[t.indexOf("@")!==-1?t.split("@")[0]:h,n?`safari-extension:${e}`:`safari-web-extension:${e}`]:[t,e]};var C;(function(t){t[t.Classic=1]="Classic",t[t.Protocol=2]="Protocol",t[t.Both=3]="Both"})(C||(C={}));var M;(function(t){t.RENDERER_START="sentry-electron.renderer-start",t.EVENT="sentry-electron.event",t.SCOPE="sentry-electron.scope",t.ENVELOPE="sentry-electron.envelope",t.STATUS="sentry-electron.status",t.ADD_METRIC="sentry-electron.add-metric"})(M||(M={}));const[,be]=G;console.log("Preload脚本加载...");i.contextBridge.exposeInMainWorld("electronAPI",{download:(t,e,n,r,s,o,a,d,c,u)=>i.ipcRenderer.invoke("start-download",t,e,n,r,s,o,a,d,c,u),resumeDownload:t=>i.ipcRenderer.invoke("resume-download",t),cancelDownload:t=>i.ipcRenderer.invoke("cancel-download",t),getDownloadVideoInfo:(t,e)=>i.ipcRenderer.invoke("get-download-video-info",t,e),getSettings:()=>i.ipcRenderer.invoke("get-settings"),saveSettings:t=>i.ipcRenderer.invoke("save-settings",t),selectDirectory:()=>i.ipcRenderer.invoke("select-directory"),openFileLocation:t=>i.ipcRenderer.invoke("open-file-location",t),openPathLocation:t=>i.ipcRenderer.invoke("open-path-location",t),getFileInfo:t=>i.ipcRenderer.invoke("get-file-info",t),fetchImage:async(t,e)=>{try{const n=e?JSON.parse(JSON.stringify(e)):void 0;return{success:!0,dataBase64:(await i.ipcRenderer.invoke("fetch-image",t,n)).dataBase64,error:void 0}}catch(n){return console.error("无法获取图片:",n),he("无法获取图片:"+n),{success:!1,dataBase64:void 0,error:n instanceof Error?n.message:String(n)}}},openExternal:t=>i.ipcRenderer.invoke("open-external",t),getAppVersion:()=>i.ipcRenderer.invoke("get-app-version"),logError:t=>i.ipcRenderer.invoke("log-error",t),logEvent:(t,e)=>i.ipcRenderer.invoke("log-event",t,e),updateBrowserCookies:t=>i.ipcRenderer.invoke("update-browser-cookies",t),openAuthWindow:(t,e)=>i.ipcRenderer.invoke("open-auth-window",t,e),removeAuth:t=>i.ipcRenderer.invoke("remove-auth",t),getPath:t=>i.ipcRenderer.invoke("get-path",t),checkAuthStatus:t=>i.ipcRenderer.invoke("check-auth-status",t),getSavedSites:()=>i.ipcRenderer.invoke("get-saved-sites"),saveSites:t=>i.ipcRenderer.invoke("save-sites",t),saveDownloadTask:t=>i.ipcRenderer.invoke("save-download-task",t),getDownloadTasks:()=>i.ipcRenderer.invoke("get-download-tasks"),clearCompletedTasks:()=>i.ipcRenderer.invoke("clear-completed-tasks"),getDownloadTask:t=>i.ipcRenderer.invoke("get-download-task",t),clearJsonTasks:t=>i.ipcRenderer.invoke("clear-json-tasks",t),checkFileExists:t=>i.ipcRenderer.invoke("check-file-exists",t),checkForUpdates:()=>i.ipcRenderer.invoke("check-for-updates"),downloadUpdate:t=>i.ipcRenderer.invoke("download-update",t),onUpdateDownloadProgress:t=>{i.ipcRenderer.on("update-download-progress",(e,n)=>t(n.progress))},removeUpdateProgressListener:()=>{i.ipcRenderer.removeAllListeners("update-download-progress")},quitAndInstall:t=>i.ipcRenderer.invoke("quit-and-install",t),checkLocalInstaller:t=>i.ipcRenderer.invoke("check-local-installer",t),onUpdateDownloaded:t=>{i.ipcRenderer.on("update-downloaded",(e,n)=>t(n.filePath))},removeUpdateDownloadedListener:()=>{i.ipcRenderer.removeAllListeners("update-downloaded")},platform:process.platform,changeLanguage:t=>i.ipcRenderer.invoke("change-language",t),getSystemLanguage:()=>i.ipcRenderer.invoke("get-system-language")});i.contextBridge.exposeInMainWorld("electron",{ipcRenderer:{on:(t,e)=>i.ipcRenderer.on(t,e),removeListener:(t,e)=>i.ipcRenderer.removeListener(t,e),onError:t=>i.ipcRenderer.on("show-error",t)}});console.log("Preload脚本加载完成");
