{"download": {"pasteLink": "Paste Link", "pasteLinkFromClipboard": "Paste Link From Clipboard", "playlistChannel": "Playlist/Channel", "pastePlaylistChannelLink": "Paste Playlist/Channel Link", "download": "Download", "resourceType": {"video": "Video", "audio": "Audio"}, "quality": "Quality", "videoQuality": {"best": "Best"}, "audioQuality": {"highest": "Highest"}, "format": "Format", "for": "For", "thumbnail": "<PERSON><PERSON><PERSON><PERSON>", "subtitles": "Subtitles", "audioTracks": "Audio tracks", "allTracks": "All Tracks", "default": "<PERSON><PERSON><PERSON>", "none": "None", "modal": {"selectAll": "Select All", "cancelAll": "Cancel All", "cancel": "Cancel", "download": "Download", "needDownloadToSelect": "Please select at least one item to download"}, "emptyState": {"step1": "Step 1: Copy video URL", "step2": "Step 2: Click to paste link and download"}, "popconfirm": {"downloadingDeleteTitle": "Are you sure you want to delete this task?", "deleteText": "Delete", "openFileFailed": "Open file failed, do you want to delete?"}}, "taskStatus": {"retrievingInformation": "Retrieving information", "downloading": "Downloading", "audioDownloading": "Audio Downloading", "videoDownloading": "Video Downloading", "subtitlesDownloading": "Subtitles Downloading", "converting": "Converting", "merging": "Merging", "downloadFailed": "Download Failed", "parseFailed": "Parse Failed", "cancelled": "Cancelled", "preparingToDownload": "Preparing to download"}, "taskActions": {"retry": "Retry", "delete": "Delete", "showInFinder": "Show in Finder", "showInFolder": "Show in Folder", "more": "More", "logIn": "Log in", "timeLeft": "Time left", "speed": "Speed", "fileSize": "File size"}, "auth": {"logIn": "Log in", "logOut": "Log out", "logInToX": "Log in to https://x.com", "done": "Done", "cancel": "Cancel", "cancelLogin": "<PERSON><PERSON>", "loginTo": "Login to"}, "contextMenu": {"copyCaption": "Copy Caption", "copyLinkAddress": "Copy Link Address", "openInBrowser": "Open Link in Browser", "remove": "Remove", "removeAll": "Remove All"}, "errors": {"connectionTimeout": "Connection timed out, please check the network connection", "unsupportedUrl": "URL not supported yet. Coming soon", "needLoginToDownload": "Need to log in to download", "fileNotFound": "File not found", "folderNotFound": "Folder not found", "openFileLocationFailed": "Open File Location Failed", "clipboardNotContainsValidUrl": "Clipboard does not contain a valid URL", "retryFailed": "Retry failed", "checkVersionFailed": "Check Version Failed", "notImplemented": "Not implemented yet", "parseError": "<PERSON><PERSON>", "downloadError": "Download Error, please check the network connection", "mergeError": "Convert <PERSON>"}, "messages": {"saveSuccess": "Save Success", "saveFailed": "Save Failed", "authSuccess": "Authorization Success", "authFailed": "Authorization Failed", "removeAuthSuccess": "Remove Authorization Success", "removeAuthFailed": "Remove Authorization Failed", "validUrlPrompt": "Please enter a valid URL", "websiteAlreadyInList": "This website is already in the list", "defaultError": "Operation Failed"}, "dialogs": {"removeAll": {"removeAllItemsFromTheList": "Remove all items from the list?", "deleteDownloadedFiles": "Delete downloaded files", "remove": "Remove", "cancel": "Cancel"}, "fileDeleted": {"fileHasBeenDeletedOrMoved": "The file has been deleted or moved. Remove this item?", "remove": "Remove", "cancel": "Cancel"}, "deleteDownloading": {"fileIsDownloading": "The file is downloading. Delete it？", "delete": "Delete", "cancel": "Cancel"}}, "menu": {"website": "Website", "settings": "Settings"}, "settings": {"general": "General", "saveTo": "Save to", "changeFolderBrowser": "Change Folder", "language": "Language", "system": "System", "createSubdirectoriesForDownloadedPlaylistsAndChannels": "Create subdirectories for downloaded playlists and channels", "numerateFilesInPlaylistsAndChannels": "Numerate files in playlists and channels", "embedSubtitlesInVideoFile": "Embed subtitles in video file", "authorization": "Authorization", "logOut": "Log out", "logIn": "Log in", "delete": "Delete", "addUrl": "Add", "enterTheWebsiteUrl": "Enter the website URL", "authorizationPanelTips": "Logging in to the website allows for downloading of age-restricted content, membership content you have purchased, and other private content.", "proxy": "Proxy", "proxyType": "Proxy Type", "httpProxy": "HTTP Proxy", "socks5Proxy": "SOCKS5 Proxy", "usingSystemProxy": "Using System Proxy", "notUsingProxy": "Not Using Proxy", "host": "Host", "port": "Port", "proxyInfoMessage": {"pleaseEnterProxyHost": "Please enter the proxy host address", "pleaseEnterValidProxyHost": "Please enter a valid host address", "pleaseEnterProxyPort": "Please enter the proxy port", "pleaseEnterValidProxyPort": "Please enter a valid port number (1-65535)", "optional": "Optional"}, "login": "<PERSON><PERSON>", "password": "Password", "save": "Save", "about": "About", "version": "Version", "latestVersion": "Latest Version", "upgrade": "Upgrade", "message": {"loadSettingsFailed": "Load settings failed"}, "checkVersion": "Check Version", "latestVersionAvailable": "Found latest version", "latestVersionNotAvailable": "Already the latest version"}, "update": {"newVersionAvailable": "New version available", "whatsNew": "What's New", "upgradeNow": "Upgrade Now", "downloading": "Downloading...", "remindAfterDownload": "Remind Me After Download", "newVersionReady": "New version is ready", "installNow": "Install Now", "remindLater": "Remind Me Later"}, "mainMenu": {"download": "Download", "online": "Online", "convert": "Convert", "audioVideoMerger": "Audio Video Merger", "joinTelegramGroup": "Join Telegram Group", "joinDiscordCommunity": "Join Discord Community"}, "application": {"menu": {"download": "Link Download", "network": "Network Sniffing", "format": "Format Conversion", "merge": "Audio and Video Merge"}, "loading": "loading..."}, "common": {"cancel": "Cancel", "ok": "Ok"}}