name: Build Electron App

on:
  push:
    branches:
      - main
  workflow_dispatch:

jobs:
  check-version:
    name: Check if package.json version has changed
    runs-on: ubuntu-22.04
    outputs:
      version_changed: ${{ steps.check_version.outputs.version_changed }}
    steps:
      - name: Check out Git repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 18

      - name: Get current package.json version
        id: get_current_version
        run: echo "current_version=$(node -p "require('./package.json').version")" >> $GITHUB_OUTPUT

      - name: Get previous package.json version
        id: get_previous_version
        run: |
          PREVIOUS_VERSION=$(git show HEAD^:package.json | node -p "JSON.parse(require('fs').readFileSync(0, 'utf-8')).version")
          echo "previous_version=$PREVIOUS_VERSION" >> $GITHUB_OUTPUT

      - name: Check if version has changed
        id: check_version
        run: |
          if [ "${{ steps.get_current_version.outputs.current_version }}" != "${{ steps.get_previous_version.outputs.previous_version }}" ]; then
            echo "version_changed=true" >> $GITHUB_OUTPUT
          else
            echo "version_changed=false" >> $GITHUB_OUTPUT
          fi

      - name: Debug versions
        run: |
          echo "Current version: ${{ steps.get_current_version.outputs.current_version }}"
          echo "Previous version: ${{ steps.get_previous_version.outputs.previous_version }}"
          echo "Version changed: ${{ steps.check_version.outputs.version_changed }}"

  build-windows:
    name: Build Windows (${{ matrix.arch }})
    runs-on: windows-latest
    needs: check-version
    if: needs.check-version.outputs.version_changed == 'true'
    strategy:
      fail-fast: false
      matrix:
        arch: [x64, ia32]
        include:
          - arch: x64
            command: make:win64
          - arch: ia32
            command: make:win32

    steps:
      - name: Check out Git repository
        uses: actions/checkout@v4

      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 18

      - name: Install dependencies
        run: |
          npm install pnpm -g
          pnpm i

      - name: Build Windows
        env:
          WINDOWS_CODESIGN_FILE: ./extra/certificates/certificate.pfx
          CSC_KEY_PASSWORD: ${{ secrets.WINDOWS_CODESIGN_PASSWORD }}
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: pnpm run ${{ matrix.command }}

      - name: Upload Windows artifacts
        uses: actions/upload-artifact@v4
        with:
          name: windows-${{ matrix.arch }}-build
          path: release/*.exe
          if-no-files-found: error

  build-macos:
    name: Build macOS (${{ matrix.arch }})
    runs-on: macos-latest
    needs: check-version
    if: needs.check-version.outputs.version_changed == 'true'
    strategy:
      fail-fast: false
      matrix:
        arch: [x64, arm64]
        include:
          - arch: x64
            command: make:mac
          - arch: arm64
            command: make:mac-arm64

    steps:
      - name: Check out Git repository
        uses: actions/checkout@v4

      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 18

      - name: Install dependencies
        run: |
          npm install pnpm -g
          pnpm i

      - name: Build macOS
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          CSC_KEY_PASSWORD: ${{ secrets.MAC_CERTIFICATE_PASSWORD }}
        run: pnpm run ${{ matrix.command }}

      - name: Upload macOS artifacts
        uses: actions/upload-artifact@v4
        with:
          name: macos-${{ matrix.arch }}-build
          path: release/*.dmg
          if-no-files-found: error

  create-release:
    needs: [build-windows, build-macos]
    if: startsWith(github.ref, 'refs/tags/')
    runs-on: ubuntu-latest
    steps:
      - name: Get version from tag
        id: get_version
        run: echo "VERSION=${GITHUB_REF#refs/tags/v}" >> $GITHUB_OUTPUT

      - name: Download all artifacts
        uses: actions/download-artifact@v4
        with:
          path: releases
        continue-on-error: true

      - name: Create Release
        uses: softprops/action-gh-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ github.ref }}
          name: SnapAny v${{ steps.get_version.outputs.VERSION }}
          body: |
            SnapAny 版本 ${{ steps.get_version.outputs.VERSION }} 发布

            ### 下载
            - Windows 64位
            - Windows 32位
            - macOS Intel
            - macOS Apple Silicon
          files: releases/**/*
          draft: true
          prerelease: contains(github.ref, '-beta')
          generate_release_notes: true
          fail_on_unmatched_files: false
