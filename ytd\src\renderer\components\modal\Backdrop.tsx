import { ReactNode } from 'react';

export interface BackdropProps {
  invisible?: boolean;
  children?: ReactNode;
  onClick?: () => void;
}

function Backdrop(props: BackdropProps) {
  return (
    <div
      aria-hidden
      className={`fixed flex items-center justify-center top-0 left-0 right-0 bottom-0 bg-black/50 bg-opacity-50 ${props.invisible ? 'bg-transparent' : ''}`}
      onClick={props.onClick}
    >
      {props.children}
    </div>
  );
}

export default Backdrop;
