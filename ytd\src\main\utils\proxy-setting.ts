import { session } from 'electron';
import { Settings } from '@main/types/settings';
import * as Sentry from '@sentry/electron/main';

let lastProxyConfig: string | null = null;

async function setupGlobalProxy(proxySettings: Settings['proxy']) {
  try {
    const config: Electron.ProxyConfig = {
      mode: 'fixed_servers',
      proxyBypassRules: '<local>,localhost,127.0.0.1,::1',
    };

    switch (proxySettings.type) {
      case 'NONE':
        config.mode = 'direct';
        break;

      case 'SYSTEM':
        config.mode = 'system';
        break;

      case 'HTTP':
        const httpProxyAuth =
          proxySettings.username && proxySettings.password
            ? `${proxySettings.username}:${proxySettings.password}@`
            : '';
        const httpProxyUrl = `${httpProxyAuth}${proxySettings.host}:${proxySettings.port}`;
        config.proxyRules = `http://${httpProxyUrl};https://${httpProxyUrl}`;
        break;

      case 'SOCKS5':
        const socksProxyAuth =
          proxySettings.username && proxySettings.password
            ? `${proxySettings.username}:${proxySettings.password}@`
            : '';
        const socksProxyUrl = `${socksProxyAuth}${proxySettings.host}:${proxySettings.port}`;
        config.proxyRules = `socks5://${socksProxyUrl}`;
        break;
    }

    // 将当前配置转换为字符串以进行比较
    const currentConfig = JSON.stringify({
      type: proxySettings.type,
      mode: config.mode,
      rules: config.proxyRules,
    });

    // 如果配置没有变化，则不重复设置
    if (currentConfig === lastProxyConfig) {
      return;
    }

    await session.defaultSession.setProxy(config);
    console.log('代理设置成功:', {
      type: proxySettings.type,
      mode: config.mode,
      rules: config.proxyRules || '无',
    });

    // 保存最后的配置
    lastProxyConfig = currentConfig;
  } catch (error) {
    console.error('代理设置失败:', error);
    Sentry.captureException(error, {
      tags: {
        proxyType: proxySettings.type,
        context: 'setupGlobalProxy',
      },
    });

    // 发生错误时，尝试直接连接
    try {
      await session.defaultSession.setProxy({
        mode: 'direct',
      } as Electron.ProxyConfig);
    } catch (fallbackError) {
      console.error('回退到直接连接也失败:', fallbackError);
      Sentry.captureException(fallbackError, {
        tags: {
          context: 'proxyFallback',
        },
      });
    }
  }
}

export { setupGlobalProxy };
