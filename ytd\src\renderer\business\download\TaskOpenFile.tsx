import { Tooltip } from 'flowbite-react';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { HiOutlineFolderOpen } from 'react-icons/hi';

import { TaskItemProps } from '@/business/download/TaskItem';
import Popconfirm from '@/components/Popconfirm';
import { isMac } from '@/utils/environment';

type TaskOpenFileProps = Pick<TaskItemProps, 'onOpen' | 'onDelete' | 'task'> & {
  className?: string;
};

function TaskOpenFile({
  onDelete,
  onOpen,
  task,
  className,
}: TaskOpenFileProps) {
  const [isOpenFailed, setIsOpenFailed] = useState(false);
  const { t } = useTranslation();

  return (
    <Popconfirm
      title={
        <div className="max-w-[181px] text-center">
          {t('download.popconfirm.openFileFailed')}
        </div>
      }
      disabled={!isOpenFailed}
      onConfirm={() => onDelete?.(task)}
    >
      <Tooltip
        content={
          isMac ? t('taskActions.showInFinder') : t('taskActions.showInFolder')
        }
      >
        <HiOutlineFolderOpen
          className={className}
          onClick={(e) => {
            if (isOpenFailed) {
              return;
            }

            onOpen?.(task).then(({ success }) => {
              if (!success) {
                setIsOpenFailed(true);
                setTimeout(() => {
                  // 再模拟一次点击已达到非受控 Popconfirm 效果
                  e.target?.dispatchEvent(
                    new MouseEvent('click', { bubbles: true }),
                  );
                }, 0);
              }
            });
          }}
        />
      </Tooltip>
    </Popconfirm>
  );
}

export default TaskOpenFile;
