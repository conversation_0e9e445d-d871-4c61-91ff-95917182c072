import { AuthSite, Setting } from '@common/types/setting';

const { electronAPI, electron } = window;

export const checkForUpdate = () => electronAPI.checkForUpdates();

export const getAppVersion = () => electronAPI.getAppVersion();

export const getSettings = () => electronAPI.getSettings();

export const saveSettings = (settingData: Setting) =>
  electronAPI.saveSettings(settingData);

export const openPathLocation = (path: string) =>
  electronAPI.openPathLocation(path);

export const getSystemLanguage = () => electronAPI.getSystemLanguage();

export const selectDirectory = () => electronAPI.selectDirectory();

export const getFileInfo = (fileName: string) =>
  electronAPI.getFileInfo(fileName);

export const openExternal = (link: string) => electronAPI.openExternal(link);

export const changeLanguage = (lang: string) =>
  electronAPI.changeLanguage(lang);

export const getSavedSites = () => electronAPI.getSavedSites();

export const saveSites = (sites: AuthSite[]) => electronAPI.saveSites(sites);

export const openAuthWindow = (authUrl: string, siteUrl: string) =>
  electronAPI.openAuthWindow(authUrl, siteUrl);

export const removeAuth = (authUrl: string) => electronAPI.removeAuth(authUrl);

export const fetchImage = (url: string, headers?: HeadersInit) =>
  electronAPI.fetchImage(url, headers);

export const getDownloadVideoInfo = electronAPI.getDownloadVideoInfo;

export const saveDownloadTask = electronAPI.saveDownloadTask;

export const openFileLocation = electronAPI.openFileLocation;

export const cancelDownload = electronAPI.cancelDownload;

export const clearJsonTasks = electronAPI.clearJsonTasks;

export const getDownloadTask = electronAPI.getDownloadTask;

export const resumeDownload = electronAPI.resumeDownload;

export const getDownloadTasks = electronAPI.getDownloadTasks;

export const downloadUpdate = electronAPI.downloadUpdate;

export const quitAndInstall = electronAPI.quitAndInstall;

export const checkLocalInstaller = electronAPI.checkLocalInstaller;

export const onUpdateDownloadProgress = electronAPI.onUpdateDownloadProgress;

export const removeUpdateProgressListener =
  electronAPI.removeUpdateProgressListener;

const { ipcRenderer } = electron;

export const onRenderer = ipcRenderer.on;
export const removeRenderer = ipcRenderer.removeListener;
