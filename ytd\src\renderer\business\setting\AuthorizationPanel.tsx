import { List } from 'flowbite-react';
import { useTranslation } from 'react-i18next';

import {
  SettingSiteAction,
  SettingSiteInfo,
  SettingSiteItem,
} from '@/client/setting-compose';
import PathComboBox from '@/components/file/PathComboBox';
import { useAuthSite } from '@/hooks/auth-site';

function SettingAuthorizationPanel() {
  const { t } = useTranslation();

  const { authSites, loginSite, logoutSite, addCustomSite, deleteSite } =
    useAuthSite();

  return (
    <div className="flex flex-col grow">
      <p className="text-[#6B7280] font-[400] mb-2">
        {t('settings.authorizationPanelTips')}
      </p>

      <List className="space-y-2.5" unstyled>
        {authSites.map((authSite) => (
          <SettingSiteItem key={authSite.url}>
            <SettingSiteInfo
              icon={
                <img
                  src={`https://www.google.com/s2/favicons?domain=${authSite.url}&sz=24`}
                />
              }
              siteLink={authSite.url}
              siteName={authSite.name}
            />
            <SettingSiteAction
              isAuthorized={authSite.isAuthorized}
              enableDelete={authSite.enableDelete}
              onLogout={() => logoutSite(authSite)}
              onLogin={() => loginSite(authSite)}
              onDelete={() => deleteSite(authSite)}
              logoutText={t('settings.logOut')}
              loginText={t('settings.logIn')}
              deleteText={t('settings.delete')}
            />
          </SettingSiteItem>
        ))}
      </List>

      <div className="grow flex justify-center items-end py-4">
        <PathComboBox
          className="w-[80%]"
          actionText={t('settings.addUrl')}
          placeholder={t('messages.validUrlPrompt')}
          handleValidPath={addCustomSite}
          disableBlurSubmit
        />
      </div>
    </div>
  );
}

export default SettingAuthorizationPanel;
