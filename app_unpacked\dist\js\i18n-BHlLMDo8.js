import{a as P}from"./vendor-DjSYeWVf.js";const De=(r,e,t,n)=>{var i,a,l,o;const s=[t,{code:e,...n||{}}];if((a=(i=r==null?void 0:r.services)==null?void 0:i.logger)!=null&&a.forward)return r.services.logger.forward(s,"warn","react-i18next::",!0);j(s[0])&&(s[0]=`react-i18next:: ${s[0]}`),(o=(l=r==null?void 0:r.services)==null?void 0:l.logger)!=null&&o.warn?r.services.logger.warn(...s):console!=null&&console.warn&&console.warn(...s)},ue={},te=(r,e,t,n)=>{j(t)&&ue[t]||(j(t)&&(ue[t]=new Date),De(r,e,t,n))},Re=(r,e)=>()=>{if(r.isInitialized)e();else{const t=()=>{setTimeout(()=>{r.off("initialized",t)},0),e()};r.on("initialized",t)}},ne=(r,e,t)=>{r.loadNamespaces(e,Re(r,t))},fe=(r,e,t,n)=>{if(j(t)&&(t=[t]),r.options.preload&&r.options.preload.indexOf(e)>-1)return ne(r,t,n);t.forEach(s=>{r.options.ns.indexOf(s)<0&&r.options.ns.push(s)}),r.loadLanguages(e,Re(r,n))},je=(r,e,t={})=>!e.languages||!e.languages.length?(te(e,"NO_LANGUAGES","i18n.languages were undefined or empty",{languages:e.languages}),!0):e.hasLoadedNamespace(r,{lng:t.lng,precheck:(n,s)=>{var i;if(((i=t.bindI18n)==null?void 0:i.indexOf("languageChanging"))>-1&&n.services.backendConnector.backend&&n.isLanguageChangingTo&&!s(n.isLanguageChangingTo,r))return!1}}),j=r=>typeof r=="string",Ue=r=>typeof r=="object"&&r!==null,Ve=/&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g,Ke={"&amp;":"&","&#38;":"&","&lt;":"<","&#60;":"<","&gt;":">","&#62;":">","&apos;":"'","&#39;":"'","&quot;":'"',"&#34;":'"',"&nbsp;":" ","&#160;":" ","&copy;":"©","&#169;":"©","&reg;":"®","&#174;":"®","&hellip;":"…","&#8230;":"…","&#x2F;":"/","&#47;":"/"},Me=r=>Ke[r],He=r=>r.replace(Ve,Me);let se={bindI18n:"languageChanged",bindI18nStore:"",transEmptyNodeValue:"",transSupportBasicHtmlNodes:!0,transWrapTextNodes:"",transKeepBasicHtmlNodesFor:["br","strong","i","p"],useSuspense:!0,unescape:He};const ze=(r={})=>{se={...se,...r}},Be=()=>se;let $e;const Je=r=>{$e=r},We=()=>$e,$t={type:"3rdParty",init(r){ze(r.options.react),Je(r)}},Pe=P.createContext();class Qe{constructor(){this.usedNamespaces={}}addUsedNamespaces(e){e.forEach(t=>{this.usedNamespaces[t]||(this.usedNamespaces[t]=!0)})}getUsedNamespaces(){return Object.keys(this.usedNamespaces)}}const Ge=(r,e)=>{const t=P.useRef();return P.useEffect(()=>{t.current=r},[r,e]),t.current},Ee=(r,e,t,n)=>r.getFixedT(e,t,n),Ye=(r,e,t,n)=>P.useCallback(Ee(r,e,t,n),[r,e,t,n]),Pt=(r,e={})=>{var k,I,A,w;const{i18n:t}=e,{i18n:n,defaultNS:s}=P.useContext(Pe)||{},i=t||n||We();if(i&&!i.reportNamespaces&&(i.reportNamespaces=new Qe),!i){te(i,"NO_I18NEXT_INSTANCE","useTranslation: You will need to pass in an i18next instance by using initReactI18next");const y=(C,S)=>j(S)?S:Ue(S)&&j(S.defaultValue)?S.defaultValue:Array.isArray(C)?C[C.length-1]:C,v=[y,{},!1];return v.t=y,v.i18n={},v.ready=!1,v}(k=i.options.react)!=null&&k.wait&&te(i,"DEPRECATED_OPTION","useTranslation: It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.");const a={...Be(),...i.options.react,...e},{useSuspense:l,keyPrefix:o}=a;let u=s||((I=i.options)==null?void 0:I.defaultNS);u=j(u)?[u]:u||["translation"],(w=(A=i.reportNamespaces).addUsedNamespaces)==null||w.call(A,u);const f=(i.isInitialized||i.initializedStoreOnce)&&u.every(y=>je(y,i,a)),d=Ye(i,e.lng||null,a.nsMode==="fallback"?u:u[0],o),c=()=>d,m=()=>Ee(i,e.lng||null,a.nsMode==="fallback"?u:u[0],o),[g,h]=P.useState(c);let x=u.join();e.lng&&(x=`${e.lng}${x}`);const O=Ge(x),b=P.useRef(!0);P.useEffect(()=>{const{bindI18n:y,bindI18nStore:v}=a;b.current=!0,!f&&!l&&(e.lng?fe(i,e.lng,u,()=>{b.current&&h(m)}):ne(i,u,()=>{b.current&&h(m)})),f&&O&&O!==x&&b.current&&h(m);const C=()=>{b.current&&h(m)};return y&&(i==null||i.on(y,C)),v&&(i==null||i.store.on(v,C)),()=>{b.current=!1,i&&(y==null||y.split(" ").forEach(S=>i.off(S,C))),v&&i&&v.split(" ").forEach(S=>i.store.off(S,C))}},[i,x]),P.useEffect(()=>{b.current&&f&&h(c)},[i,o,f]);const L=[g,i,f];if(L.t=g,L.i18n=i,L.ready=f,f||!f&&!l)return L;throw new Promise(y=>{e.lng?fe(i,e.lng,u,()=>y()):ne(i,u,()=>y())})};function Et({i18n:r,defaultNS:e,children:t}){const n=P.useMemo(()=>({i18n:r,defaultNS:e}),[r,e]);return P.createElement(Pe.Provider,{value:n},t)}const p=r=>typeof r=="string",B=()=>{let r,e;const t=new Promise((n,s)=>{r=n,e=s});return t.resolve=r,t.reject=e,t},ce=r=>r==null?"":""+r,qe=(r,e,t)=>{r.forEach(n=>{e[n]&&(t[n]=e[n])})},Ze=/###/g,de=r=>r&&r.indexOf("###")>-1?r.replace(Ze,"."):r,ge=r=>!r||p(r),J=(r,e,t)=>{const n=p(e)?e.split("."):e;let s=0;for(;s<n.length-1;){if(ge(r))return{};const i=de(n[s]);!r[i]&&t&&(r[i]=new t),Object.prototype.hasOwnProperty.call(r,i)?r=r[i]:r={},++s}return ge(r)?{}:{obj:r,k:de(n[s])}},he=(r,e,t)=>{const{obj:n,k:s}=J(r,e,Object);if(n!==void 0||e.length===1){n[s]=t;return}let i=e[e.length-1],a=e.slice(0,e.length-1),l=J(r,a,Object);for(;l.obj===void 0&&a.length;)i=`${a[a.length-1]}.${i}`,a=a.slice(0,a.length-1),l=J(r,a,Object),l!=null&&l.obj&&typeof l.obj[`${l.k}.${i}`]<"u"&&(l.obj=void 0);l.obj[`${l.k}.${i}`]=t},Xe=(r,e,t,n)=>{const{obj:s,k:i}=J(r,e,Object);s[i]=s[i]||[],s[i].push(t)},Y=(r,e)=>{const{obj:t,k:n}=J(r,e);if(t&&Object.prototype.hasOwnProperty.call(t,n))return t[n]},_e=(r,e,t)=>{const n=Y(r,t);return n!==void 0?n:Y(e,t)},Fe=(r,e,t)=>{for(const n in e)n!=="__proto__"&&n!=="constructor"&&(n in r?p(r[n])||r[n]instanceof String||p(e[n])||e[n]instanceof String?t&&(r[n]=e[n]):Fe(r[n],e[n],t):r[n]=e[n]);return r},U=r=>r.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&");var et={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"};const tt=r=>p(r)?r.replace(/[&<>"'\/]/g,e=>et[e]):r;class nt{constructor(e){this.capacity=e,this.regExpMap=new Map,this.regExpQueue=[]}getRegExp(e){const t=this.regExpMap.get(e);if(t!==void 0)return t;const n=new RegExp(e);return this.regExpQueue.length===this.capacity&&this.regExpMap.delete(this.regExpQueue.shift()),this.regExpMap.set(e,n),this.regExpQueue.push(e),n}}const st=[" ",",","?","!",";"],it=new nt(20),rt=(r,e,t)=>{e=e||"",t=t||"";const n=st.filter(a=>e.indexOf(a)<0&&t.indexOf(a)<0);if(n.length===0)return!0;const s=it.getRegExp(`(${n.map(a=>a==="?"?"\\?":a).join("|")})`);let i=!s.test(r);if(!i){const a=r.indexOf(t);a>0&&!s.test(r.substring(0,a))&&(i=!0)}return i},ie=function(r,e){let t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:".";if(!r)return;if(r[e])return Object.prototype.hasOwnProperty.call(r,e)?r[e]:void 0;const n=e.split(t);let s=r;for(let i=0;i<n.length;){if(!s||typeof s!="object")return;let a,l="";for(let o=i;o<n.length;++o)if(o!==i&&(l+=t),l+=n[o],a=s[l],a!==void 0){if(["string","number","boolean"].indexOf(typeof a)>-1&&o<n.length-1)continue;i+=o-i+1;break}s=a}return s},q=r=>r==null?void 0:r.replace("_","-"),at={type:"logger",log(r){this.output("log",r)},warn(r){this.output("warn",r)},error(r){this.output("error",r)},output(r,e){var t,n;(n=(t=console==null?void 0:console[r])==null?void 0:t.apply)==null||n.call(t,console,e)}};class Z{constructor(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.init(e,t)}init(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.prefix=t.prefix||"i18next:",this.logger=e||at,this.options=t,this.debug=t.debug}log(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.forward(t,"log","",!0)}warn(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.forward(t,"warn","",!0)}error(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.forward(t,"error","")}deprecate(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.forward(t,"warn","WARNING DEPRECATED: ",!0)}forward(e,t,n,s){return s&&!this.debug?null:(p(e[0])&&(e[0]=`${n}${this.prefix} ${e[0]}`),this.logger[t](e))}create(e){return new Z(this.logger,{prefix:`${this.prefix}:${e}:`,...this.options})}clone(e){return e=e||this.options,e.prefix=e.prefix||this.prefix,new Z(this.logger,e)}}var F=new Z;class _{constructor(){this.observers={}}on(e,t){return e.split(" ").forEach(n=>{this.observers[n]||(this.observers[n]=new Map);const s=this.observers[n].get(t)||0;this.observers[n].set(t,s+1)}),this}off(e,t){if(this.observers[e]){if(!t){delete this.observers[e];return}this.observers[e].delete(t)}}emit(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),s=1;s<t;s++)n[s-1]=arguments[s];this.observers[e]&&Array.from(this.observers[e].entries()).forEach(a=>{let[l,o]=a;for(let u=0;u<o;u++)l(...n)}),this.observers["*"]&&Array.from(this.observers["*"].entries()).forEach(a=>{let[l,o]=a;for(let u=0;u<o;u++)l.apply(l,[e,...n])})}}class pe extends _{constructor(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{ns:["translation"],defaultNS:"translation"};super(),this.data=e||{},this.options=t,this.options.keySeparator===void 0&&(this.options.keySeparator="."),this.options.ignoreJSONStructure===void 0&&(this.options.ignoreJSONStructure=!0)}addNamespaces(e){this.options.ns.indexOf(e)<0&&this.options.ns.push(e)}removeNamespaces(e){const t=this.options.ns.indexOf(e);t>-1&&this.options.ns.splice(t,1)}getResource(e,t,n){var u,f;let s=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{};const i=s.keySeparator!==void 0?s.keySeparator:this.options.keySeparator,a=s.ignoreJSONStructure!==void 0?s.ignoreJSONStructure:this.options.ignoreJSONStructure;let l;e.indexOf(".")>-1?l=e.split("."):(l=[e,t],n&&(Array.isArray(n)?l.push(...n):p(n)&&i?l.push(...n.split(i)):l.push(n)));const o=Y(this.data,l);return!o&&!t&&!n&&e.indexOf(".")>-1&&(e=l[0],t=l[1],n=l.slice(2).join(".")),o||!a||!p(n)?o:ie((f=(u=this.data)==null?void 0:u[e])==null?void 0:f[t],n,i)}addResource(e,t,n,s){let i=arguments.length>4&&arguments[4]!==void 0?arguments[4]:{silent:!1};const a=i.keySeparator!==void 0?i.keySeparator:this.options.keySeparator;let l=[e,t];n&&(l=l.concat(a?n.split(a):n)),e.indexOf(".")>-1&&(l=e.split("."),s=t,t=l[1]),this.addNamespaces(t),he(this.data,l,s),i.silent||this.emit("added",e,t,n,s)}addResources(e,t,n){let s=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{silent:!1};for(const i in n)(p(n[i])||Array.isArray(n[i]))&&this.addResource(e,t,i,n[i],{silent:!0});s.silent||this.emit("added",e,t,n)}addResourceBundle(e,t,n,s,i){let a=arguments.length>5&&arguments[5]!==void 0?arguments[5]:{silent:!1,skipCopy:!1},l=[e,t];e.indexOf(".")>-1&&(l=e.split("."),s=n,n=t,t=l[1]),this.addNamespaces(t);let o=Y(this.data,l)||{};a.skipCopy||(n=JSON.parse(JSON.stringify(n))),s?Fe(o,n,i):o={...o,...n},he(this.data,l,o),a.silent||this.emit("added",e,t,n)}removeResourceBundle(e,t){this.hasResourceBundle(e,t)&&delete this.data[e][t],this.removeNamespaces(t),this.emit("removed",e,t)}hasResourceBundle(e,t){return this.getResource(e,t)!==void 0}getResourceBundle(e,t){return t||(t=this.options.defaultNS),this.getResource(e,t)}getDataByLanguage(e){return this.data[e]}hasLanguageSomeTranslations(e){const t=this.getDataByLanguage(e);return!!(t&&Object.keys(t)||[]).find(s=>t[s]&&Object.keys(t[s]).length>0)}toJSON(){return this.data}}var Te={processors:{},addPostProcessor(r){this.processors[r.name]=r},handle(r,e,t,n,s){return r.forEach(i=>{var a;e=((a=this.processors[i])==null?void 0:a.process(e,t,n,s))??e}),e}};const me={},xe=r=>!p(r)&&typeof r!="boolean"&&typeof r!="number";class X extends _{constructor(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};super(),qe(["resourceStore","languageUtils","pluralResolver","interpolator","backendConnector","i18nFormat","utils"],e,this),this.options=t,this.options.keySeparator===void 0&&(this.options.keySeparator="."),this.logger=F.create("translator")}changeLanguage(e){e&&(this.language=e)}exists(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{interpolation:{}};if(e==null)return!1;const n=this.resolve(e,t);return(n==null?void 0:n.res)!==void 0}extractFromKey(e,t){let n=t.nsSeparator!==void 0?t.nsSeparator:this.options.nsSeparator;n===void 0&&(n=":");const s=t.keySeparator!==void 0?t.keySeparator:this.options.keySeparator;let i=t.ns||this.options.defaultNS||[];const a=n&&e.indexOf(n)>-1,l=!this.options.userDefinedKeySeparator&&!t.keySeparator&&!this.options.userDefinedNsSeparator&&!t.nsSeparator&&!rt(e,n,s);if(a&&!l){const o=e.match(this.interpolator.nestingRegexp);if(o&&o.length>0)return{key:e,namespaces:p(i)?[i]:i};const u=e.split(n);(n!==s||n===s&&this.options.ns.indexOf(u[0])>-1)&&(i=u.shift()),e=u.join(s)}return{key:e,namespaces:p(i)?[i]:i}}translate(e,t,n){if(typeof t!="object"&&this.options.overloadTranslationOptionHandler&&(t=this.options.overloadTranslationOptionHandler(arguments)),typeof t=="object"&&(t={...t}),t||(t={}),e==null)return"";Array.isArray(e)||(e=[String(e)]);const s=t.returnDetails!==void 0?t.returnDetails:this.options.returnDetails,i=t.keySeparator!==void 0?t.keySeparator:this.options.keySeparator,{key:a,namespaces:l}=this.extractFromKey(e[e.length-1],t),o=l[l.length-1],u=t.lng||this.language,f=t.appendNamespaceToCIMode||this.options.appendNamespaceToCIMode;if((u==null?void 0:u.toLowerCase())==="cimode"){if(f){const S=t.nsSeparator||this.options.nsSeparator;return s?{res:`${o}${S}${a}`,usedKey:a,exactUsedKey:a,usedLng:u,usedNS:o,usedParams:this.getUsedParamsDetails(t)}:`${o}${S}${a}`}return s?{res:a,usedKey:a,exactUsedKey:a,usedLng:u,usedNS:o,usedParams:this.getUsedParamsDetails(t)}:a}const d=this.resolve(e,t);let c=d==null?void 0:d.res;const m=(d==null?void 0:d.usedKey)||a,g=(d==null?void 0:d.exactUsedKey)||a,h=["[object Number]","[object Function]","[object RegExp]"],x=t.joinArrays!==void 0?t.joinArrays:this.options.joinArrays,O=!this.i18nFormat||this.i18nFormat.handleAsObject,b=t.count!==void 0&&!p(t.count),L=X.hasDefaultValue(t),k=b?this.pluralResolver.getSuffix(u,t.count,t):"",I=t.ordinal&&b?this.pluralResolver.getSuffix(u,t.count,{ordinal:!1}):"",A=b&&!t.ordinal&&t.count===0,w=A&&t[`defaultValue${this.options.pluralSeparator}zero`]||t[`defaultValue${k}`]||t[`defaultValue${I}`]||t.defaultValue;let y=c;O&&!c&&L&&(y=w);const v=xe(y),C=Object.prototype.toString.apply(y);if(O&&y&&v&&h.indexOf(C)<0&&!(p(x)&&Array.isArray(y))){if(!t.returnObjects&&!this.options.returnObjects){this.options.returnedObjectHandler||this.logger.warn("accessing an object - but returnObjects options is not enabled!");const S=this.options.returnedObjectHandler?this.options.returnedObjectHandler(m,y,{...t,ns:l}):`key '${a} (${this.language})' returned an object instead of string.`;return s?(d.res=S,d.usedParams=this.getUsedParamsDetails(t),d):S}if(i){const S=Array.isArray(y),$=S?[]:{},re=S?g:m;for(const E in y)if(Object.prototype.hasOwnProperty.call(y,E)){const T=`${re}${i}${E}`;L&&!c?$[E]=this.translate(T,{...t,defaultValue:xe(w)?w[E]:void 0,joinArrays:!1,ns:l}):$[E]=this.translate(T,{...t,joinArrays:!1,ns:l}),$[E]===T&&($[E]=y[E])}c=$}}else if(O&&p(x)&&Array.isArray(c))c=c.join(x),c&&(c=this.extendTranslation(c,e,t,n));else{let S=!1,$=!1;!this.isValidLookup(c)&&L&&(S=!0,c=w),this.isValidLookup(c)||($=!0,c=a);const E=(t.missingKeyNoValueFallbackToKey||this.options.missingKeyNoValueFallbackToKey)&&$?void 0:c,T=L&&w!==c&&this.options.updateMissing;if($||S||T){if(this.logger.log(T?"updateKey":"missingKey",u,o,a,T?w:c),i){const R=this.resolve(a,{...t,keySeparator:!1});R&&R.res&&this.logger.warn("Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.")}let H=[];const Q=this.languageUtils.getFallbackCodes(this.options.fallbackLng,t.lng||this.language);if(this.options.saveMissingTo==="fallback"&&Q&&Q[0])for(let R=0;R<Q.length;R++)H.push(Q[R]);else this.options.saveMissingTo==="all"?H=this.languageUtils.toResolveHierarchy(t.lng||this.language):H.push(t.lng||this.language);const ae=(R,D,z)=>{var le;const oe=L&&z!==c?z:E;this.options.missingKeyHandler?this.options.missingKeyHandler(R,o,D,oe,T,t):(le=this.backendConnector)!=null&&le.saveMissing&&this.backendConnector.saveMissing(R,o,D,oe,T,t),this.emit("missingKey",R,o,D,c)};this.options.saveMissing&&(this.options.saveMissingPlurals&&b?H.forEach(R=>{const D=this.pluralResolver.getSuffixes(R,t);A&&t[`defaultValue${this.options.pluralSeparator}zero`]&&D.indexOf(`${this.options.pluralSeparator}zero`)<0&&D.push(`${this.options.pluralSeparator}zero`),D.forEach(z=>{ae([R],a+z,t[`defaultValue${z}`]||w)})}):ae(H,a,w))}c=this.extendTranslation(c,e,t,d,n),$&&c===a&&this.options.appendNamespaceToMissingKey&&(c=`${o}:${a}`),($||S)&&this.options.parseMissingKeyHandler&&(c=this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey?`${o}:${a}`:a,S?c:void 0))}return s?(d.res=c,d.usedParams=this.getUsedParamsDetails(t),d):c}extendTranslation(e,t,n,s,i){var u,f;var a=this;if((u=this.i18nFormat)!=null&&u.parse)e=this.i18nFormat.parse(e,{...this.options.interpolation.defaultVariables,...n},n.lng||this.language||s.usedLng,s.usedNS,s.usedKey,{resolved:s});else if(!n.skipInterpolation){n.interpolation&&this.interpolator.init({...n,interpolation:{...this.options.interpolation,...n.interpolation}});const d=p(e)&&(((f=n==null?void 0:n.interpolation)==null?void 0:f.skipOnVariables)!==void 0?n.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables);let c;if(d){const g=e.match(this.interpolator.nestingRegexp);c=g&&g.length}let m=n.replace&&!p(n.replace)?n.replace:n;if(this.options.interpolation.defaultVariables&&(m={...this.options.interpolation.defaultVariables,...m}),e=this.interpolator.interpolate(e,m,n.lng||this.language||s.usedLng,n),d){const g=e.match(this.interpolator.nestingRegexp),h=g&&g.length;c<h&&(n.nest=!1)}!n.lng&&s&&s.res&&(n.lng=this.language||s.usedLng),n.nest!==!1&&(e=this.interpolator.nest(e,function(){for(var g=arguments.length,h=new Array(g),x=0;x<g;x++)h[x]=arguments[x];return(i==null?void 0:i[0])===h[0]&&!n.context?(a.logger.warn(`It seems you are nesting recursively key: ${h[0]} in key: ${t[0]}`),null):a.translate(...h,t)},n)),n.interpolation&&this.interpolator.reset()}const l=n.postProcess||this.options.postProcess,o=p(l)?[l]:l;return e!=null&&(o!=null&&o.length)&&n.applyPostProcessor!==!1&&(e=Te.handle(o,e,t,this.options&&this.options.postProcessPassResolved?{i18nResolved:{...s,usedParams:this.getUsedParamsDetails(n)},...n}:n,this)),e}resolve(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n,s,i,a,l;return p(e)&&(e=[e]),e.forEach(o=>{if(this.isValidLookup(n))return;const u=this.extractFromKey(o,t),f=u.key;s=f;let d=u.namespaces;this.options.fallbackNS&&(d=d.concat(this.options.fallbackNS));const c=t.count!==void 0&&!p(t.count),m=c&&!t.ordinal&&t.count===0,g=t.context!==void 0&&(p(t.context)||typeof t.context=="number")&&t.context!=="",h=t.lngs?t.lngs:this.languageUtils.toResolveHierarchy(t.lng||this.language,t.fallbackLng);d.forEach(x=>{var O,b;this.isValidLookup(n)||(l=x,!me[`${h[0]}-${x}`]&&((O=this.utils)!=null&&O.hasLoadedNamespace)&&!((b=this.utils)!=null&&b.hasLoadedNamespace(l))&&(me[`${h[0]}-${x}`]=!0,this.logger.warn(`key "${s}" for languages "${h.join(", ")}" won't get resolved as namespace "${l}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!")),h.forEach(L=>{var A;if(this.isValidLookup(n))return;a=L;const k=[f];if((A=this.i18nFormat)!=null&&A.addLookupKeys)this.i18nFormat.addLookupKeys(k,f,L,x,t);else{let w;c&&(w=this.pluralResolver.getSuffix(L,t.count,t));const y=`${this.options.pluralSeparator}zero`,v=`${this.options.pluralSeparator}ordinal${this.options.pluralSeparator}`;if(c&&(k.push(f+w),t.ordinal&&w.indexOf(v)===0&&k.push(f+w.replace(v,this.options.pluralSeparator)),m&&k.push(f+y)),g){const C=`${f}${this.options.contextSeparator}${t.context}`;k.push(C),c&&(k.push(C+w),t.ordinal&&w.indexOf(v)===0&&k.push(C+w.replace(v,this.options.pluralSeparator)),m&&k.push(C+y))}}let I;for(;I=k.pop();)this.isValidLookup(n)||(i=I,n=this.getResource(L,x,I,t))}))})}),{res:n,usedKey:s,exactUsedKey:i,usedLng:a,usedNS:l}}isValidLookup(e){return e!==void 0&&!(!this.options.returnNull&&e===null)&&!(!this.options.returnEmptyString&&e==="")}getResource(e,t,n){var i;let s=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{};return(i=this.i18nFormat)!=null&&i.getResource?this.i18nFormat.getResource(e,t,n,s):this.resourceStore.getResource(e,t,n,s)}getUsedParamsDetails(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};const t=["defaultValue","ordinal","context","replace","lng","lngs","fallbackLng","ns","keySeparator","nsSeparator","returnObjects","returnDetails","joinArrays","postProcess","interpolation"],n=e.replace&&!p(e.replace);let s=n?e.replace:e;if(n&&typeof e.count<"u"&&(s.count=e.count),this.options.interpolation.defaultVariables&&(s={...this.options.interpolation.defaultVariables,...s}),!n){s={...s};for(const i of t)delete s[i]}return s}static hasDefaultValue(e){const t="defaultValue";for(const n in e)if(Object.prototype.hasOwnProperty.call(e,n)&&t===n.substring(0,t.length)&&e[n]!==void 0)return!0;return!1}}class ye{constructor(e){this.options=e,this.supportedLngs=this.options.supportedLngs||!1,this.logger=F.create("languageUtils")}getScriptPartFromCode(e){if(e=q(e),!e||e.indexOf("-")<0)return null;const t=e.split("-");return t.length===2||(t.pop(),t[t.length-1].toLowerCase()==="x")?null:this.formatLanguageCode(t.join("-"))}getLanguagePartFromCode(e){if(e=q(e),!e||e.indexOf("-")<0)return e;const t=e.split("-");return this.formatLanguageCode(t[0])}formatLanguageCode(e){if(p(e)&&e.indexOf("-")>-1){let t;try{t=Intl.getCanonicalLocales(e)[0]}catch{}return t&&this.options.lowerCaseLng&&(t=t.toLowerCase()),t||(this.options.lowerCaseLng?e.toLowerCase():e)}return this.options.cleanCode||this.options.lowerCaseLng?e.toLowerCase():e}isSupportedCode(e){return(this.options.load==="languageOnly"||this.options.nonExplicitSupportedLngs)&&(e=this.getLanguagePartFromCode(e)),!this.supportedLngs||!this.supportedLngs.length||this.supportedLngs.indexOf(e)>-1}getBestMatchFromCodes(e){if(!e)return null;let t;return e.forEach(n=>{if(t)return;const s=this.formatLanguageCode(n);(!this.options.supportedLngs||this.isSupportedCode(s))&&(t=s)}),!t&&this.options.supportedLngs&&e.forEach(n=>{if(t)return;const s=this.getLanguagePartFromCode(n);if(this.isSupportedCode(s))return t=s;t=this.options.supportedLngs.find(i=>{if(i===s)return i;if(!(i.indexOf("-")<0&&s.indexOf("-")<0)&&(i.indexOf("-")>0&&s.indexOf("-")<0&&i.substring(0,i.indexOf("-"))===s||i.indexOf(s)===0&&s.length>1))return i})}),t||(t=this.getFallbackCodes(this.options.fallbackLng)[0]),t}getFallbackCodes(e,t){if(!e)return[];if(typeof e=="function"&&(e=e(t)),p(e)&&(e=[e]),Array.isArray(e))return e;if(!t)return e.default||[];let n=e[t];return n||(n=e[this.getScriptPartFromCode(t)]),n||(n=e[this.formatLanguageCode(t)]),n||(n=e[this.getLanguagePartFromCode(t)]),n||(n=e.default),n||[]}toResolveHierarchy(e,t){const n=this.getFallbackCodes(t||this.options.fallbackLng||[],e),s=[],i=a=>{a&&(this.isSupportedCode(a)?s.push(a):this.logger.warn(`rejecting language code not found in supportedLngs: ${a}`))};return p(e)&&(e.indexOf("-")>-1||e.indexOf("_")>-1)?(this.options.load!=="languageOnly"&&i(this.formatLanguageCode(e)),this.options.load!=="languageOnly"&&this.options.load!=="currentOnly"&&i(this.getScriptPartFromCode(e)),this.options.load!=="currentOnly"&&i(this.getLanguagePartFromCode(e))):p(e)&&i(this.formatLanguageCode(e)),n.forEach(a=>{s.indexOf(a)<0&&i(this.formatLanguageCode(a))}),s}}const Se={zero:0,one:1,two:2,few:3,many:4,other:5},be={select:r=>r===1?"one":"other",resolvedOptions:()=>({pluralCategories:["one","other"]})};class ot{constructor(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.languageUtils=e,this.options=t,this.logger=F.create("pluralResolver"),this.pluralRulesCache={}}addRule(e,t){this.rules[e]=t}clearCache(){this.pluralRulesCache={}}getRule(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};const n=q(e==="dev"?"en":e),s=t.ordinal?"ordinal":"cardinal",i=JSON.stringify({cleanedCode:n,type:s});if(i in this.pluralRulesCache)return this.pluralRulesCache[i];let a;try{a=new Intl.PluralRules(n,{type:s})}catch{if(!Intl)return this.logger.error("No Intl support, please use an Intl polyfill!"),be;if(!e.match(/-|_/))return be;const o=this.languageUtils.getLanguagePartFromCode(e);a=this.getRule(o,t)}return this.pluralRulesCache[i]=a,a}needsPlural(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=this.getRule(e,t);return n||(n=this.getRule("dev",t)),(n==null?void 0:n.resolvedOptions().pluralCategories.length)>1}getPluralFormsOfKey(e,t){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return this.getSuffixes(e,n).map(s=>`${t}${s}`)}getSuffixes(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=this.getRule(e,t);return n||(n=this.getRule("dev",t)),n?n.resolvedOptions().pluralCategories.sort((s,i)=>Se[s]-Se[i]).map(s=>`${this.options.prepend}${t.ordinal?`ordinal${this.options.prepend}`:""}${s}`):[]}getSuffix(e,t){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};const s=this.getRule(e,n);return s?`${this.options.prepend}${n.ordinal?`ordinal${this.options.prepend}`:""}${s.select(t)}`:(this.logger.warn(`no plural rule found for: ${e}`),this.getSuffix("dev",t,n))}}const we=function(r,e,t){let n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:".",s=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!0,i=_e(r,e,t);return!i&&s&&p(t)&&(i=ie(r,t,n),i===void 0&&(i=ie(e,t,n))),i},ee=r=>r.replace(/\$/g,"$$$$");class lt{constructor(){var t;let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};this.logger=F.create("interpolator"),this.options=e,this.format=((t=e==null?void 0:e.interpolation)==null?void 0:t.format)||(n=>n),this.init(e)}init(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};e.interpolation||(e.interpolation={escapeValue:!0});const{escape:t,escapeValue:n,useRawValueToEscape:s,prefix:i,prefixEscaped:a,suffix:l,suffixEscaped:o,formatSeparator:u,unescapeSuffix:f,unescapePrefix:d,nestingPrefix:c,nestingPrefixEscaped:m,nestingSuffix:g,nestingSuffixEscaped:h,nestingOptionsSeparator:x,maxReplaces:O,alwaysFormat:b}=e.interpolation;this.escape=t!==void 0?t:tt,this.escapeValue=n!==void 0?n:!0,this.useRawValueToEscape=s!==void 0?s:!1,this.prefix=i?U(i):a||"{{",this.suffix=l?U(l):o||"}}",this.formatSeparator=u||",",this.unescapePrefix=f?"":d||"-",this.unescapeSuffix=this.unescapePrefix?"":f||"",this.nestingPrefix=c?U(c):m||U("$t("),this.nestingSuffix=g?U(g):h||U(")"),this.nestingOptionsSeparator=x||",",this.maxReplaces=O||1e3,this.alwaysFormat=b!==void 0?b:!1,this.resetRegExp()}reset(){this.options&&this.init(this.options)}resetRegExp(){const e=(t,n)=>(t==null?void 0:t.source)===n?(t.lastIndex=0,t):new RegExp(n,"g");this.regexp=e(this.regexp,`${this.prefix}(.+?)${this.suffix}`),this.regexpUnescape=e(this.regexpUnescape,`${this.prefix}${this.unescapePrefix}(.+?)${this.unescapeSuffix}${this.suffix}`),this.nestingRegexp=e(this.nestingRegexp,`${this.nestingPrefix}(.+?)${this.nestingSuffix}`)}interpolate(e,t,n,s){var m;let i,a,l;const o=this.options&&this.options.interpolation&&this.options.interpolation.defaultVariables||{},u=g=>{if(g.indexOf(this.formatSeparator)<0){const b=we(t,o,g,this.options.keySeparator,this.options.ignoreJSONStructure);return this.alwaysFormat?this.format(b,void 0,n,{...s,...t,interpolationkey:g}):b}const h=g.split(this.formatSeparator),x=h.shift().trim(),O=h.join(this.formatSeparator).trim();return this.format(we(t,o,x,this.options.keySeparator,this.options.ignoreJSONStructure),O,n,{...s,...t,interpolationkey:x})};this.resetRegExp();const f=(s==null?void 0:s.missingInterpolationHandler)||this.options.missingInterpolationHandler,d=((m=s==null?void 0:s.interpolation)==null?void 0:m.skipOnVariables)!==void 0?s.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables;return[{regex:this.regexpUnescape,safeValue:g=>ee(g)},{regex:this.regexp,safeValue:g=>this.escapeValue?ee(this.escape(g)):ee(g)}].forEach(g=>{for(l=0;i=g.regex.exec(e);){const h=i[1].trim();if(a=u(h),a===void 0)if(typeof f=="function"){const O=f(e,i,s);a=p(O)?O:""}else if(s&&Object.prototype.hasOwnProperty.call(s,h))a="";else if(d){a=i[0];continue}else this.logger.warn(`missed to pass in variable ${h} for interpolating ${e}`),a="";else!p(a)&&!this.useRawValueToEscape&&(a=ce(a));const x=g.safeValue(a);if(e=e.replace(i[0],x),d?(g.regex.lastIndex+=a.length,g.regex.lastIndex-=i[0].length):g.regex.lastIndex=0,l++,l>=this.maxReplaces)break}}),e}nest(e,t){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},s,i,a;const l=(o,u)=>{const f=this.nestingOptionsSeparator;if(o.indexOf(f)<0)return o;const d=o.split(new RegExp(`${f}[ ]*{`));let c=`{${d[1]}`;o=d[0],c=this.interpolate(c,a);const m=c.match(/'/g),g=c.match(/"/g);(((m==null?void 0:m.length)??0)%2===0&&!g||g.length%2!==0)&&(c=c.replace(/'/g,'"'));try{a=JSON.parse(c),u&&(a={...u,...a})}catch(h){return this.logger.warn(`failed parsing options string in nesting for key ${o}`,h),`${o}${f}${c}`}return a.defaultValue&&a.defaultValue.indexOf(this.prefix)>-1&&delete a.defaultValue,o};for(;s=this.nestingRegexp.exec(e);){let o=[];a={...n},a=a.replace&&!p(a.replace)?a.replace:a,a.applyPostProcessor=!1,delete a.defaultValue;let u=!1;if(s[0].indexOf(this.formatSeparator)!==-1&&!/{.*}/.test(s[1])){const f=s[1].split(this.formatSeparator).map(d=>d.trim());s[1]=f.shift(),o=f,u=!0}if(i=t(l.call(this,s[1].trim(),a),a),i&&s[0]===e&&!p(i))return i;p(i)||(i=ce(i)),i||(this.logger.warn(`missed to resolve ${s[1]} for nesting ${e}`),i=""),u&&(i=o.reduce((f,d)=>this.format(f,d,n.lng,{...n,interpolationkey:s[1].trim()}),i.trim())),e=e.replace(s[0],i),this.regexp.lastIndex=0}return e}}const ut=r=>{let e=r.toLowerCase().trim();const t={};if(r.indexOf("(")>-1){const n=r.split("(");e=n[0].toLowerCase().trim();const s=n[1].substring(0,n[1].length-1);e==="currency"&&s.indexOf(":")<0?t.currency||(t.currency=s.trim()):e==="relativetime"&&s.indexOf(":")<0?t.range||(t.range=s.trim()):s.split(";").forEach(a=>{if(a){const[l,...o]=a.split(":"),u=o.join(":").trim().replace(/^'+|'+$/g,""),f=l.trim();t[f]||(t[f]=u),u==="false"&&(t[f]=!1),u==="true"&&(t[f]=!0),isNaN(u)||(t[f]=parseInt(u,10))}})}return{formatName:e,formatOptions:t}},V=r=>{const e={};return(t,n,s)=>{let i=s;s&&s.interpolationkey&&s.formatParams&&s.formatParams[s.interpolationkey]&&s[s.interpolationkey]&&(i={...i,[s.interpolationkey]:void 0});const a=n+JSON.stringify(i);let l=e[a];return l||(l=r(q(n),s),e[a]=l),l(t)}};class ft{constructor(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};this.logger=F.create("formatter"),this.options=e,this.formats={number:V((t,n)=>{const s=new Intl.NumberFormat(t,{...n});return i=>s.format(i)}),currency:V((t,n)=>{const s=new Intl.NumberFormat(t,{...n,style:"currency"});return i=>s.format(i)}),datetime:V((t,n)=>{const s=new Intl.DateTimeFormat(t,{...n});return i=>s.format(i)}),relativetime:V((t,n)=>{const s=new Intl.RelativeTimeFormat(t,{...n});return i=>s.format(i,n.range||"day")}),list:V((t,n)=>{const s=new Intl.ListFormat(t,{...n});return i=>s.format(i)})},this.init(e)}init(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{interpolation:{}};this.formatSeparator=t.interpolation.formatSeparator||","}add(e,t){this.formats[e.toLowerCase().trim()]=t}addCached(e,t){this.formats[e.toLowerCase().trim()]=V(t)}format(e,t,n){let s=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{};const i=t.split(this.formatSeparator);if(i.length>1&&i[0].indexOf("(")>1&&i[0].indexOf(")")<0&&i.find(l=>l.indexOf(")")>-1)){const l=i.findIndex(o=>o.indexOf(")")>-1);i[0]=[i[0],...i.splice(1,l)].join(this.formatSeparator)}return i.reduce((l,o)=>{var d;const{formatName:u,formatOptions:f}=ut(o);if(this.formats[u]){let c=l;try{const m=((d=s==null?void 0:s.formatParams)==null?void 0:d[s.interpolationkey])||{},g=m.locale||m.lng||s.locale||s.lng||n;c=this.formats[u](l,g,{...f,...s,...m})}catch(m){this.logger.warn(m)}return c}else this.logger.warn(`there was no format function for ${u}`);return l},e)}}const ct=(r,e)=>{r.pending[e]!==void 0&&(delete r.pending[e],r.pendingCount--)};class dt extends _{constructor(e,t,n){var i,a;let s=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{};super(),this.backend=e,this.store=t,this.services=n,this.languageUtils=n.languageUtils,this.options=s,this.logger=F.create("backendConnector"),this.waitingReads=[],this.maxParallelReads=s.maxParallelReads||10,this.readingCalls=0,this.maxRetries=s.maxRetries>=0?s.maxRetries:5,this.retryTimeout=s.retryTimeout>=1?s.retryTimeout:350,this.state={},this.queue=[],(a=(i=this.backend)==null?void 0:i.init)==null||a.call(i,n,s.backend,s)}queueLoad(e,t,n,s){const i={},a={},l={},o={};return e.forEach(u=>{let f=!0;t.forEach(d=>{const c=`${u}|${d}`;!n.reload&&this.store.hasResourceBundle(u,d)?this.state[c]=2:this.state[c]<0||(this.state[c]===1?a[c]===void 0&&(a[c]=!0):(this.state[c]=1,f=!1,a[c]===void 0&&(a[c]=!0),i[c]===void 0&&(i[c]=!0),o[d]===void 0&&(o[d]=!0)))}),f||(l[u]=!0)}),(Object.keys(i).length||Object.keys(a).length)&&this.queue.push({pending:a,pendingCount:Object.keys(a).length,loaded:{},errors:[],callback:s}),{toLoad:Object.keys(i),pending:Object.keys(a),toLoadLanguages:Object.keys(l),toLoadNamespaces:Object.keys(o)}}loaded(e,t,n){const s=e.split("|"),i=s[0],a=s[1];t&&this.emit("failedLoading",i,a,t),!t&&n&&this.store.addResourceBundle(i,a,n,void 0,void 0,{skipCopy:!0}),this.state[e]=t?-1:2,t&&n&&(this.state[e]=0);const l={};this.queue.forEach(o=>{Xe(o.loaded,[i],a),ct(o,e),t&&o.errors.push(t),o.pendingCount===0&&!o.done&&(Object.keys(o.loaded).forEach(u=>{l[u]||(l[u]={});const f=o.loaded[u];f.length&&f.forEach(d=>{l[u][d]===void 0&&(l[u][d]=!0)})}),o.done=!0,o.errors.length?o.callback(o.errors):o.callback())}),this.emit("loaded",l),this.queue=this.queue.filter(o=>!o.done)}read(e,t,n){let s=arguments.length>3&&arguments[3]!==void 0?arguments[3]:0,i=arguments.length>4&&arguments[4]!==void 0?arguments[4]:this.retryTimeout,a=arguments.length>5?arguments[5]:void 0;if(!e.length)return a(null,{});if(this.readingCalls>=this.maxParallelReads){this.waitingReads.push({lng:e,ns:t,fcName:n,tried:s,wait:i,callback:a});return}this.readingCalls++;const l=(u,f)=>{if(this.readingCalls--,this.waitingReads.length>0){const d=this.waitingReads.shift();this.read(d.lng,d.ns,d.fcName,d.tried,d.wait,d.callback)}if(u&&f&&s<this.maxRetries){setTimeout(()=>{this.read.call(this,e,t,n,s+1,i*2,a)},i);return}a(u,f)},o=this.backend[n].bind(this.backend);if(o.length===2){try{const u=o(e,t);u&&typeof u.then=="function"?u.then(f=>l(null,f)).catch(l):l(null,u)}catch(u){l(u)}return}return o(e,t,l)}prepareLoading(e,t){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},s=arguments.length>3?arguments[3]:void 0;if(!this.backend)return this.logger.warn("No backend was added via i18next.use. Will not load resources."),s&&s();p(e)&&(e=this.languageUtils.toResolveHierarchy(e)),p(t)&&(t=[t]);const i=this.queueLoad(e,t,n,s);if(!i.toLoad.length)return i.pending.length||s(),null;i.toLoad.forEach(a=>{this.loadOne(a)})}load(e,t,n){this.prepareLoading(e,t,{},n)}reload(e,t,n){this.prepareLoading(e,t,{reload:!0},n)}loadOne(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"";const n=e.split("|"),s=n[0],i=n[1];this.read(s,i,"read",void 0,void 0,(a,l)=>{a&&this.logger.warn(`${t}loading namespace ${i} for language ${s} failed`,a),!a&&l&&this.logger.log(`${t}loaded namespace ${i} for language ${s}`,l),this.loaded(e,a,l)})}saveMissing(e,t,n,s,i){var o,u,f,d,c;let a=arguments.length>5&&arguments[5]!==void 0?arguments[5]:{},l=arguments.length>6&&arguments[6]!==void 0?arguments[6]:()=>{};if((u=(o=this.services)==null?void 0:o.utils)!=null&&u.hasLoadedNamespace&&!((d=(f=this.services)==null?void 0:f.utils)!=null&&d.hasLoadedNamespace(t))){this.logger.warn(`did not save key "${n}" as the namespace "${t}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!");return}if(!(n==null||n==="")){if((c=this.backend)!=null&&c.create){const m={...a,isUpdate:i},g=this.backend.create.bind(this.backend);if(g.length<6)try{let h;g.length===5?h=g(e,t,n,s,m):h=g(e,t,n,s),h&&typeof h.then=="function"?h.then(x=>l(null,x)).catch(l):l(null,h)}catch(h){l(h)}else g(e,t,n,s,l,m)}!e||!e[0]||this.store.addResource(e[0],t,n,s)}}}const Oe=()=>({debug:!1,initAsync:!0,ns:["translation"],defaultNS:["translation"],fallbackLng:["dev"],fallbackNS:!1,supportedLngs:!1,nonExplicitSupportedLngs:!1,load:"all",preload:!1,simplifyPluralSuffix:!0,keySeparator:".",nsSeparator:":",pluralSeparator:"_",contextSeparator:"_",partialBundledLanguages:!1,saveMissing:!1,updateMissing:!1,saveMissingTo:"fallback",saveMissingPlurals:!0,missingKeyHandler:!1,missingInterpolationHandler:!1,postProcess:!1,postProcessPassResolved:!1,returnNull:!1,returnEmptyString:!0,returnObjects:!1,joinArrays:!1,returnedObjectHandler:!1,parseMissingKeyHandler:!1,appendNamespaceToMissingKey:!1,appendNamespaceToCIMode:!1,overloadTranslationOptionHandler:r=>{let e={};if(typeof r[1]=="object"&&(e=r[1]),p(r[1])&&(e.defaultValue=r[1]),p(r[2])&&(e.tDescription=r[2]),typeof r[2]=="object"||typeof r[3]=="object"){const t=r[3]||r[2];Object.keys(t).forEach(n=>{e[n]=t[n]})}return e},interpolation:{escapeValue:!0,format:r=>r,prefix:"{{",suffix:"}}",formatSeparator:",",unescapePrefix:"-",nestingPrefix:"$t(",nestingSuffix:")",nestingOptionsSeparator:",",maxReplaces:1e3,skipOnVariables:!0}}),Le=r=>{var e,t;return p(r.ns)&&(r.ns=[r.ns]),p(r.fallbackLng)&&(r.fallbackLng=[r.fallbackLng]),p(r.fallbackNS)&&(r.fallbackNS=[r.fallbackNS]),((t=(e=r.supportedLngs)==null?void 0:e.indexOf)==null?void 0:t.call(e,"cimode"))<0&&(r.supportedLngs=r.supportedLngs.concat(["cimode"])),typeof r.initImmediate=="boolean"&&(r.initAsync=r.initImmediate),r},G=()=>{},gt=r=>{Object.getOwnPropertyNames(Object.getPrototypeOf(r)).forEach(t=>{typeof r[t]=="function"&&(r[t]=r[t].bind(r))})};class W extends _{constructor(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;if(super(),this.options=Le(e),this.services={},this.logger=F,this.modules={external:[]},gt(this),t&&!this.isInitialized&&!e.isClone){if(!this.options.initAsync)return this.init(e,t),this;setTimeout(()=>{this.init(e,t)},0)}}init(){var e=this;let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},n=arguments.length>1?arguments[1]:void 0;this.isInitializing=!0,typeof t=="function"&&(n=t,t={}),t.defaultNS==null&&t.ns&&(p(t.ns)?t.defaultNS=t.ns:t.ns.indexOf("translation")<0&&(t.defaultNS=t.ns[0]));const s=Oe();this.options={...s,...this.options,...Le(t)},this.options.interpolation={...s.interpolation,...this.options.interpolation},t.keySeparator!==void 0&&(this.options.userDefinedKeySeparator=t.keySeparator),t.nsSeparator!==void 0&&(this.options.userDefinedNsSeparator=t.nsSeparator);const i=f=>f?typeof f=="function"?new f:f:null;if(!this.options.isClone){this.modules.logger?F.init(i(this.modules.logger),this.options):F.init(null,this.options);let f;this.modules.formatter?f=this.modules.formatter:f=ft;const d=new ye(this.options);this.store=new pe(this.options.resources,this.options);const c=this.services;c.logger=F,c.resourceStore=this.store,c.languageUtils=d,c.pluralResolver=new ot(d,{prepend:this.options.pluralSeparator,simplifyPluralSuffix:this.options.simplifyPluralSuffix}),f&&(!this.options.interpolation.format||this.options.interpolation.format===s.interpolation.format)&&(c.formatter=i(f),c.formatter.init(c,this.options),this.options.interpolation.format=c.formatter.format.bind(c.formatter)),c.interpolator=new lt(this.options),c.utils={hasLoadedNamespace:this.hasLoadedNamespace.bind(this)},c.backendConnector=new dt(i(this.modules.backend),c.resourceStore,c,this.options),c.backendConnector.on("*",function(m){for(var g=arguments.length,h=new Array(g>1?g-1:0),x=1;x<g;x++)h[x-1]=arguments[x];e.emit(m,...h)}),this.modules.languageDetector&&(c.languageDetector=i(this.modules.languageDetector),c.languageDetector.init&&c.languageDetector.init(c,this.options.detection,this.options)),this.modules.i18nFormat&&(c.i18nFormat=i(this.modules.i18nFormat),c.i18nFormat.init&&c.i18nFormat.init(this)),this.translator=new X(this.services,this.options),this.translator.on("*",function(m){for(var g=arguments.length,h=new Array(g>1?g-1:0),x=1;x<g;x++)h[x-1]=arguments[x];e.emit(m,...h)}),this.modules.external.forEach(m=>{m.init&&m.init(this)})}if(this.format=this.options.interpolation.format,n||(n=G),this.options.fallbackLng&&!this.services.languageDetector&&!this.options.lng){const f=this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);f.length>0&&f[0]!=="dev"&&(this.options.lng=f[0])}!this.services.languageDetector&&!this.options.lng&&this.logger.warn("init: no languageDetector is used and no lng is defined"),["getResource","hasResourceBundle","getResourceBundle","getDataByLanguage"].forEach(f=>{this[f]=function(){return e.store[f](...arguments)}}),["addResource","addResources","addResourceBundle","removeResourceBundle"].forEach(f=>{this[f]=function(){return e.store[f](...arguments),e}});const o=B(),u=()=>{const f=(d,c)=>{this.isInitializing=!1,this.isInitialized&&!this.initializedStoreOnce&&this.logger.warn("init: i18next is already initialized. You should call init just once!"),this.isInitialized=!0,this.options.isClone||this.logger.log("initialized",this.options),this.emit("initialized",this.options),o.resolve(c),n(d,c)};if(this.languages&&!this.isInitialized)return f(null,this.t.bind(this));this.changeLanguage(this.options.lng,f)};return this.options.resources||!this.options.initAsync?u():setTimeout(u,0),o}loadResources(e){var i,a;let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:G;const s=p(e)?e:this.language;if(typeof e=="function"&&(n=e),!this.options.resources||this.options.partialBundledLanguages){if((s==null?void 0:s.toLowerCase())==="cimode"&&(!this.options.preload||this.options.preload.length===0))return n();const l=[],o=u=>{if(!u||u==="cimode")return;this.services.languageUtils.toResolveHierarchy(u).forEach(d=>{d!=="cimode"&&l.indexOf(d)<0&&l.push(d)})};s?o(s):this.services.languageUtils.getFallbackCodes(this.options.fallbackLng).forEach(f=>o(f)),(a=(i=this.options.preload)==null?void 0:i.forEach)==null||a.call(i,u=>o(u)),this.services.backendConnector.load(l,this.options.ns,u=>{!u&&!this.resolvedLanguage&&this.language&&this.setResolvedLanguage(this.language),n(u)})}else n(null)}reloadResources(e,t,n){const s=B();return typeof e=="function"&&(n=e,e=void 0),typeof t=="function"&&(n=t,t=void 0),e||(e=this.languages),t||(t=this.options.ns),n||(n=G),this.services.backendConnector.reload(e,t,i=>{s.resolve(),n(i)}),s}use(e){if(!e)throw new Error("You are passing an undefined module! Please check the object you are passing to i18next.use()");if(!e.type)throw new Error("You are passing a wrong module! Please check the object you are passing to i18next.use()");return e.type==="backend"&&(this.modules.backend=e),(e.type==="logger"||e.log&&e.warn&&e.error)&&(this.modules.logger=e),e.type==="languageDetector"&&(this.modules.languageDetector=e),e.type==="i18nFormat"&&(this.modules.i18nFormat=e),e.type==="postProcessor"&&Te.addPostProcessor(e),e.type==="formatter"&&(this.modules.formatter=e),e.type==="3rdParty"&&this.modules.external.push(e),this}setResolvedLanguage(e){if(!(!e||!this.languages)&&!(["cimode","dev"].indexOf(e)>-1))for(let t=0;t<this.languages.length;t++){const n=this.languages[t];if(!(["cimode","dev"].indexOf(n)>-1)&&this.store.hasLanguageSomeTranslations(n)){this.resolvedLanguage=n;break}}}changeLanguage(e,t){var n=this;this.isLanguageChangingTo=e;const s=B();this.emit("languageChanging",e);const i=o=>{this.language=o,this.languages=this.services.languageUtils.toResolveHierarchy(o),this.resolvedLanguage=void 0,this.setResolvedLanguage(o)},a=(o,u)=>{u?(i(u),this.translator.changeLanguage(u),this.isLanguageChangingTo=void 0,this.emit("languageChanged",u),this.logger.log("languageChanged",u)):this.isLanguageChangingTo=void 0,s.resolve(function(){return n.t(...arguments)}),t&&t(o,function(){return n.t(...arguments)})},l=o=>{var f,d;!e&&!o&&this.services.languageDetector&&(o=[]);const u=p(o)?o:this.services.languageUtils.getBestMatchFromCodes(o);u&&(this.language||i(u),this.translator.language||this.translator.changeLanguage(u),(d=(f=this.services.languageDetector)==null?void 0:f.cacheUserLanguage)==null||d.call(f,u)),this.loadResources(u,c=>{a(c,u)})};return!e&&this.services.languageDetector&&!this.services.languageDetector.async?l(this.services.languageDetector.detect()):!e&&this.services.languageDetector&&this.services.languageDetector.async?this.services.languageDetector.detect.length===0?this.services.languageDetector.detect().then(l):this.services.languageDetector.detect(l):l(e),s}getFixedT(e,t,n){var s=this;const i=function(a,l){let o;if(typeof l!="object"){for(var u=arguments.length,f=new Array(u>2?u-2:0),d=2;d<u;d++)f[d-2]=arguments[d];o=s.options.overloadTranslationOptionHandler([a,l].concat(f))}else o={...l};o.lng=o.lng||i.lng,o.lngs=o.lngs||i.lngs,o.ns=o.ns||i.ns,o.keyPrefix!==""&&(o.keyPrefix=o.keyPrefix||n||i.keyPrefix);const c=s.options.keySeparator||".";let m;return o.keyPrefix&&Array.isArray(a)?m=a.map(g=>`${o.keyPrefix}${c}${g}`):m=o.keyPrefix?`${o.keyPrefix}${c}${a}`:a,s.t(m,o)};return p(e)?i.lng=e:i.lngs=e,i.ns=t,i.keyPrefix=n,i}t(){var s;for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return(s=this.translator)==null?void 0:s.translate(...t)}exists(){var s;for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return(s=this.translator)==null?void 0:s.exists(...t)}setDefaultNamespace(e){this.options.defaultNS=e}hasLoadedNamespace(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(!this.isInitialized)return this.logger.warn("hasLoadedNamespace: i18next was not initialized",this.languages),!1;if(!this.languages||!this.languages.length)return this.logger.warn("hasLoadedNamespace: i18n.languages were undefined or empty",this.languages),!1;const n=t.lng||this.resolvedLanguage||this.languages[0],s=this.options?this.options.fallbackLng:!1,i=this.languages[this.languages.length-1];if(n.toLowerCase()==="cimode")return!0;const a=(l,o)=>{const u=this.services.backendConnector.state[`${l}|${o}`];return u===-1||u===0||u===2};if(t.precheck){const l=t.precheck(this,a);if(l!==void 0)return l}return!!(this.hasResourceBundle(n,e)||!this.services.backendConnector.backend||this.options.resources&&!this.options.partialBundledLanguages||a(n,e)&&(!s||a(i,e)))}loadNamespaces(e,t){const n=B();return this.options.ns?(p(e)&&(e=[e]),e.forEach(s=>{this.options.ns.indexOf(s)<0&&this.options.ns.push(s)}),this.loadResources(s=>{n.resolve(),t&&t(s)}),n):(t&&t(),Promise.resolve())}loadLanguages(e,t){const n=B();p(e)&&(e=[e]);const s=this.options.preload||[],i=e.filter(a=>s.indexOf(a)<0&&this.services.languageUtils.isSupportedCode(a));return i.length?(this.options.preload=s.concat(i),this.loadResources(a=>{n.resolve(),t&&t(a)}),n):(t&&t(),Promise.resolve())}dir(e){var s,i;if(e||(e=this.resolvedLanguage||(((s=this.languages)==null?void 0:s.length)>0?this.languages[0]:this.language)),!e)return"rtl";const t=["ar","shu","sqr","ssh","xaa","yhd","yud","aao","abh","abv","acm","acq","acw","acx","acy","adf","ads","aeb","aec","afb","ajp","apc","apd","arb","arq","ars","ary","arz","auz","avl","ayh","ayl","ayn","ayp","bbz","pga","he","iw","ps","pbt","pbu","pst","prp","prd","ug","ur","ydd","yds","yih","ji","yi","hbo","men","xmn","fa","jpr","peo","pes","prs","dv","sam","ckb"],n=((i=this.services)==null?void 0:i.languageUtils)||new ye(Oe());return t.indexOf(n.getLanguagePartFromCode(e))>-1||e.toLowerCase().indexOf("-arab")>1?"rtl":"ltr"}static createInstance(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;return new W(e,t)}cloneInstance(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:G;const n=e.forkResourceStore;n&&delete e.forkResourceStore;const s={...this.options,...e,isClone:!0},i=new W(s);if((e.debug!==void 0||e.prefix!==void 0)&&(i.logger=i.logger.clone(e)),["store","services","language"].forEach(l=>{i[l]=this[l]}),i.services={...this.services},i.services.utils={hasLoadedNamespace:i.hasLoadedNamespace.bind(i)},n){const l=Object.keys(this.store.data).reduce((o,u)=>(o[u]={...this.store.data[u]},Object.keys(o[u]).reduce((f,d)=>(f[d]={...o[u][d]},f),{})),{});i.store=new pe(l,s),i.services.resourceStore=i.store}return i.translator=new X(i.services,s),i.translator.on("*",function(l){for(var o=arguments.length,u=new Array(o>1?o-1:0),f=1;f<o;f++)u[f-1]=arguments[f];i.emit(l,...u)}),i.init(s,t),i.translator.options=s,i.translator.backendConnector.services.utils={hasLoadedNamespace:i.hasLoadedNamespace.bind(i)},i}toJSON(){return{options:this.options,store:this.store,language:this.language,languages:this.languages,resolvedLanguage:this.resolvedLanguage}}}const N=W.createInstance();N.createInstance=W.createInstance;N.createInstance;N.dir;N.init;N.loadResources;N.reloadResources;N.use;N.changeLanguage;N.getFixedT;const Ft=N.t;N.exists;N.setDefaultNamespace;N.hasLoadedNamespace;N.loadNamespaces;N.loadLanguages;const{slice:ht,forEach:pt}=[];function mt(r){return pt.call(ht.call(arguments,1),e=>{if(e)for(const t in e)r[t]===void 0&&(r[t]=e[t])}),r}const ve=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/,xt=function(r,e){const n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{path:"/"},s=encodeURIComponent(e);let i=`${r}=${s}`;if(n.maxAge>0){const a=n.maxAge-0;if(Number.isNaN(a))throw new Error("maxAge should be a Number");i+=`; Max-Age=${Math.floor(a)}`}if(n.domain){if(!ve.test(n.domain))throw new TypeError("option domain is invalid");i+=`; Domain=${n.domain}`}if(n.path){if(!ve.test(n.path))throw new TypeError("option path is invalid");i+=`; Path=${n.path}`}if(n.expires){if(typeof n.expires.toUTCString!="function")throw new TypeError("option expires is invalid");i+=`; Expires=${n.expires.toUTCString()}`}if(n.httpOnly&&(i+="; HttpOnly"),n.secure&&(i+="; Secure"),n.sameSite)switch(typeof n.sameSite=="string"?n.sameSite.toLowerCase():n.sameSite){case!0:i+="; SameSite=Strict";break;case"lax":i+="; SameSite=Lax";break;case"strict":i+="; SameSite=Strict";break;case"none":i+="; SameSite=None";break;default:throw new TypeError("option sameSite is invalid")}return i},Ce={create(r,e,t,n){let s=arguments.length>4&&arguments[4]!==void 0?arguments[4]:{path:"/",sameSite:"strict"};t&&(s.expires=new Date,s.expires.setTime(s.expires.getTime()+t*60*1e3)),n&&(s.domain=n),document.cookie=xt(r,encodeURIComponent(e),s)},read(r){const e=`${r}=`,t=document.cookie.split(";");for(let n=0;n<t.length;n++){let s=t[n];for(;s.charAt(0)===" ";)s=s.substring(1,s.length);if(s.indexOf(e)===0)return s.substring(e.length,s.length)}return null},remove(r){this.create(r,"",-1)}};var yt={name:"cookie",lookup(r){let{lookupCookie:e}=r;if(e&&typeof document<"u")return Ce.read(e)||void 0},cacheUserLanguage(r,e){let{lookupCookie:t,cookieMinutes:n,cookieDomain:s,cookieOptions:i}=e;t&&typeof document<"u"&&Ce.create(t,r,n,s,i)}},St={name:"querystring",lookup(r){var n;let{lookupQuerystring:e}=r,t;if(typeof window<"u"){let{search:s}=window.location;!window.location.search&&((n=window.location.hash)==null?void 0:n.indexOf("?"))>-1&&(s=window.location.hash.substring(window.location.hash.indexOf("?")));const a=s.substring(1).split("&");for(let l=0;l<a.length;l++){const o=a[l].indexOf("=");o>0&&a[l].substring(0,o)===e&&(t=a[l].substring(o+1))}}return t}};let K=null;const Ne=()=>{if(K!==null)return K;try{if(K=typeof window<"u"&&window.localStorage!==null,!K)return!1;const r="i18next.translate.boo";window.localStorage.setItem(r,"foo"),window.localStorage.removeItem(r)}catch{K=!1}return K};var bt={name:"localStorage",lookup(r){let{lookupLocalStorage:e}=r;if(e&&Ne())return window.localStorage.getItem(e)||void 0},cacheUserLanguage(r,e){let{lookupLocalStorage:t}=e;t&&Ne()&&window.localStorage.setItem(t,r)}};let M=null;const ke=()=>{if(M!==null)return M;try{if(M=typeof window<"u"&&window.sessionStorage!==null,!M)return!1;const r="i18next.translate.boo";window.sessionStorage.setItem(r,"foo"),window.sessionStorage.removeItem(r)}catch{M=!1}return M};var wt={name:"sessionStorage",lookup(r){let{lookupSessionStorage:e}=r;if(e&&ke())return window.sessionStorage.getItem(e)||void 0},cacheUserLanguage(r,e){let{lookupSessionStorage:t}=e;t&&ke()&&window.sessionStorage.setItem(t,r)}},Ot={name:"navigator",lookup(r){const e=[];if(typeof navigator<"u"){const{languages:t,userLanguage:n,language:s}=navigator;if(t)for(let i=0;i<t.length;i++)e.push(t[i]);n&&e.push(n),s&&e.push(s)}return e.length>0?e:void 0}},Lt={name:"htmlTag",lookup(r){let{htmlTag:e}=r,t;const n=e||(typeof document<"u"?document.documentElement:null);return n&&typeof n.getAttribute=="function"&&(t=n.getAttribute("lang")),t}},vt={name:"path",lookup(r){var s;let{lookupFromPathIndex:e}=r;if(typeof window>"u")return;const t=window.location.pathname.match(/\/([a-zA-Z-]*)/g);return Array.isArray(t)?(s=t[typeof e=="number"?e:0])==null?void 0:s.replace("/",""):void 0}},Ct={name:"subdomain",lookup(r){var s,i;let{lookupFromSubdomainIndex:e}=r;const t=typeof e=="number"?e+1:1,n=typeof window<"u"&&((i=(s=window.location)==null?void 0:s.hostname)==null?void 0:i.match(/^(\w{2,5})\.(([a-z0-9-]{1,63}\.[a-z]{2,6})|localhost)/i));if(n)return n[t]}};let Ie=!1;try{document.cookie,Ie=!0}catch{}const Ae=["querystring","cookie","localStorage","sessionStorage","navigator","htmlTag"];Ie||Ae.splice(1,1);const Nt=()=>({order:Ae,lookupQuerystring:"lng",lookupCookie:"i18next",lookupLocalStorage:"i18nextLng",lookupSessionStorage:"i18nextLng",caches:["localStorage"],excludeCacheFor:["cimode"],convertDetectedLanguage:r=>r});class kt{constructor(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.type="languageDetector",this.detectors={},this.init(e,t)}init(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{languageUtils:{}},t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};this.services=e,this.options=mt(t,this.options||{},Nt()),typeof this.options.convertDetectedLanguage=="string"&&this.options.convertDetectedLanguage.indexOf("15897")>-1&&(this.options.convertDetectedLanguage=s=>s.replace("-","_")),this.options.lookupFromUrlIndex&&(this.options.lookupFromPathIndex=this.options.lookupFromUrlIndex),this.i18nOptions=n,this.addDetector(yt),this.addDetector(St),this.addDetector(bt),this.addDetector(wt),this.addDetector(Ot),this.addDetector(Lt),this.addDetector(vt),this.addDetector(Ct)}addDetector(e){return this.detectors[e.name]=e,this}detect(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.options.order,t=[];return e.forEach(n=>{if(this.detectors[n]){let s=this.detectors[n].lookup(this.options);s&&typeof s=="string"&&(s=[s]),s&&(t=t.concat(s))}}),t=t.map(n=>this.options.convertDetectedLanguage(n)),this.services&&this.services.languageUtils&&this.services.languageUtils.getBestMatchFromCodes?t:t.length>0?t[0]:null}cacheUserLanguage(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.options.caches;t&&(this.options.excludeCacheFor&&this.options.excludeCacheFor.indexOf(e)>-1||t.forEach(n=>{this.detectors[n]&&this.detectors[n].cacheUserLanguage(e,this.options)}))}}kt.type="languageDetector";export{kt as B,Et as I,$t as a,N as i,Ft as t,Pt as u};
