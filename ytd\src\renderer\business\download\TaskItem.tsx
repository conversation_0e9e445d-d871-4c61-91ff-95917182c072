import { DOWNLOAD_STATUS_ENUM } from '@common/constants/download';
import { StoredDownloadTask } from '@common/types/download';
import { SimpleResult } from '@common/types/electron-bridge';
import classNames from 'classnames';
import { Tooltip } from 'flowbite-react';
import { memo, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import {
  FaClock,
  FaFile,
  FaHeadphones,
  FaMusic,
  FaVideo,
} from 'react-icons/fa';
import { FaDisplay } from 'react-icons/fa6';
import { HiOutlineRefresh, HiOutlineTrash } from 'react-icons/hi';
import useSWR from 'swr';

import TaskOpenFile from '@/business/download/TaskOpenFile';
import {
  MediaInfoItem,
  MediaListItem,
  MediaShowResolution,
} from '@/client/media-compose';
import ImageSecureLoad from '@/components/image/SecureLoad';
import Popconfirm from '@/components/Popconfirm';
import { AUDIO_FORMATS } from '@/constants/media';
import { useSnapany } from '@/hooks/snapany';
import { getFileInfo } from '@/service/render';
import { calcDownloadByteVo } from '@/utils/download';
import { formatDuration, formatFileSize } from '@/utils/video';

const ERROR_STATUSES: string[] = [
  DOWNLOAD_STATUS_ENUM.DownloadError,
  DOWNLOAD_STATUS_ENUM.ParseError,
  DOWNLOAD_STATUS_ENUM.ParseSignError,
  DOWNLOAD_STATUS_ENUM.DownloadSignError,
  DOWNLOAD_STATUS_ENUM.UnsupportedUrl,
];

const DOWNLOADING_STATUS = [
  DOWNLOAD_STATUS_ENUM.Parsing,
  DOWNLOAD_STATUS_ENUM.DownloadingAudio,
  DOWNLOAD_STATUS_ENUM.DownloadingVideo,
];

export interface TaskItemProps {
  task: StoredDownloadTask;
  onDelete?: (task: TaskItemProps['task']) => void;
  onRetry?: (task: TaskItemProps['task']) => void;
  onOpen?: (task: TaskItemProps['task']) => Promise<SimpleResult>;
  onAuth?: (task: TaskItemProps['task']) => void;
}

function TaskItem({ task, onDelete, onOpen, onRetry, onAuth }: TaskItemProps) {
  const { t } = useTranslation();
  const { settings } = useSnapany();

  const downloadPath = settings?.defaultDownloadPath;
  const fileName = task?.finalFilename;
  const videoInfo = task.videoInfo;
  const status = task?.status ?? '';

  const isCompleted = status === DOWNLOAD_STATUS_ENUM.Completed;

  const { data: fileInfo } = useSWR(
    isCompleted && fileName && downloadPath ? `getFileInfo/${fileName}` : null,
    () => getFileInfo(`${downloadPath}/${fileName}`),
    {
      shouldRetryOnError: false,
    },
  );

  const isErrorStatus = ERROR_STATUSES.includes(status);

  const progress = useMemo(() => {
    if (isCompleted) {
      return 100;
    }
    // ! 兜底
    const progress = Number(task?.progress);
    return progress > 0 && progress < 100 ? progress : 0;
  }, [isCompleted, task?.progress]);

  const statusNode = useMemo(() => {
    if (!status) {
      return;
    }

    switch (status) {
      case DOWNLOAD_STATUS_ENUM.Parsing:
        return t('taskStatus.retrievingInformation');
      case DOWNLOAD_STATUS_ENUM.DownloadingAudio:
      case DOWNLOAD_STATUS_ENUM.DownloadingVideo:
        return t('taskStatus.downloading');
      case DOWNLOAD_STATUS_ENUM.Merging:
        return t('taskStatus.converting');
      case DOWNLOAD_STATUS_ENUM.DownloadError:
        return (
          task.error ||
          // downloadItem.videoInfo?.error ||
          t('taskStatus.downloadFailed')
        );
      case DOWNLOAD_STATUS_ENUM.ParseError:
      case DOWNLOAD_STATUS_ENUM.UnsupportedUrl:
        return (
          task.error ||
          // downloadItem.videoInfo?.error ||
          t('taskStatus.parseFailed')
        );
      case DOWNLOAD_STATUS_ENUM.DownloadSignError:
      case DOWNLOAD_STATUS_ENUM.ParseSignError:
        return (
          <p>
            {t('errors.needLoginToDownload')}
            <span className="px-1" onClick={() => onAuth?.(task)}>
              {t('auth.logIn')}
            </span>
          </p>
        );
      case DOWNLOAD_STATUS_ENUM.Cancelled:
        return t('taskStatus.cancelled');
      default:
        return t('taskStatus.preparingToDownload');
    }
  }, [status, t, task, onAuth]);

  const renderCompletedDescription = () => {
    if (!fileInfo) {
      return;
    }
    const {
      filesize,
      format,
      resolutionWidth,
      resolutionHeight,
      duration,
      audioBitrate,
    } = fileInfo;
    const isMusic = AUDIO_FORMATS.map(({ value }) => value as string).includes(
      format,
    );

    const commonValidator = (value: string) => !['Unknown', ''].includes(value);

    return (
      <>
        <MediaInfoItem
          icon={isMusic ? <FaMusic /> : <FaVideo />}
          content={format.toUpperCase()}
        />

        {Number(duration) > 0 && (
          <MediaInfoItem
            icon={<FaClock />}
            content={formatDuration(duration)}
          />
        )}

        {isMusic ? (
          commonValidator(audioBitrate) && (
            <MediaInfoItem icon={<FaHeadphones />} content={audioBitrate} />
          )
        ) : (
          <MediaInfoItem
            icon={<FaDisplay />}
            content={
              <MediaShowResolution
                width={resolutionWidth}
                height={resolutionHeight}
              />
            }
          />
        )}

        <MediaInfoItem icon={<FaFile />} content={formatFileSize(filesize)} />
      </>
    );
  };

  const renderUncompletedDescription = () => {
    if (!task) {
      return;
    }
    const { speed, eta, downloadSize, status } = task;

    return (
      <div
        className={classNames({
          'text-[#ff4d4f]': isErrorStatus,
        })}
      >
        <div className="flex gap-3 flex-wrap">
          <span>{statusNode}</span>
          {!isErrorStatus && status !== DOWNLOAD_STATUS_ENUM.Merging && (
            <>
              {speed && (
                <span>
                  {t('taskActions.speed')}:{' '}
                  {calcDownloadByteVo(Number(speed), '/s')}
                </span>
              )}
              {eta && (
                <span>
                  {t('taskActions.timeLeft')}: {eta}
                </span>
              )}
              {Number(downloadSize) > 0 && (
                <span>
                  {t('taskActions.fileSize')}:{' '}
                  {calcDownloadByteVo(Number(downloadSize))}
                </span>
              )}
            </>
          )}
        </div>
      </div>
    );
  };

  const renderAction = () => {
    const iconStyle = 'text-3xl cursor-pointer';
    const deletePopDisabled = !DOWNLOADING_STATUS.includes(status);
    return (
      <>
        {isCompleted ? (
          <TaskOpenFile
            task={task}
            onOpen={onOpen}
            onDelete={onDelete}
            className={iconStyle}
          />
        ) : (
          isErrorStatus &&
          status !== DOWNLOAD_STATUS_ENUM.UnsupportedUrl && (
            <Tooltip content={t('taskActions.retry')}>
              <HiOutlineRefresh
                className={iconStyle}
                onClick={() => onRetry?.(task)}
              />
            </Tooltip>
          )
        )}
        <Popconfirm
          disabled={deletePopDisabled}
          title={t('download.popconfirm.downloadingDeleteTitle')}
          onConfirm={() => onDelete?.(task)}
          okButtonProps={{
            color: 'failure',
          }}
          okText={t('download.popconfirm.deleteText')}
        >
          <Tooltip content={t('taskActions.delete')}>
            <HiOutlineTrash
              className={iconStyle}
              onClick={() => deletePopDisabled && onDelete?.(task)}
            />
          </Tooltip>
        </Popconfirm>
      </>
    );
  };

  return (
    <MediaListItem key={videoInfo.id} progress={progress}>
      <main className="flex gap-8 items-center">
        <ImageSecureLoad
          url={videoInfo.thumbnail}
          headers={videoInfo.thumbnailHeaders}
          loading={status === DOWNLOAD_STATUS_ENUM.Parsing}
        />
        <div className="flex flex-col justify-between h-full gap-y-1">
          <section className="table table-fixed w-full">
            <p className="text-sm text-ellipsis overflow-hidden whitespace-nowrap">
              {videoInfo.title || task.url}
            </p>
          </section>
          <section className="flex gap-x-[39px] text-[#666] text-sm flex-wrap">
            {isCompleted
              ? renderCompletedDescription()
              : renderUncompletedDescription()}
          </section>
        </div>
      </main>
      <aside className="flex items-center gap-6">{renderAction()}</aside>
    </MediaListItem>
  );
}

export default memo(TaskItem);
