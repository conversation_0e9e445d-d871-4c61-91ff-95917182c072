import * as Sentry from '@sentry/electron/renderer';
import { Button, Modal, Progress } from 'flowbite-react';
import i18n from 'i18next';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Notice } from '@/components/Notice';
import {
  checkLocalInstaller,
  downloadUpdate,
  onUpdateDownloadProgress,
  quitAndInstall,
  removeUpdateProgressListener,
} from '@/service/render';

interface UpdateViewProps {
  version: string;
  updateType: 'force' | 'optional';
  upgradeContent: Record<string, string>;
  downloadUrl: string;
  onClose?: () => void;
}

const UpdateModal: React.FC<UpdateViewProps> = ({
  version,
  updateType,
  upgradeContent,
  downloadUrl,
  onClose,
}) => {
  const [downloading, setDownloading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [downloadedFilePath, setDownloadedFilePath] = useState<string | null>(
    null,
  );
  const [hasLocalInstaller, setHasLocalInstaller] = useState(false);
  const minimizedRef = React.useRef(
    localStorage.getItem(`update_minimized_${version}`) === 'true',
  );
  const [minimized, setMinimized] = useState(minimizedRef.current);
  const { t } = useTranslation();

  const handleInstall = async (filePath: string) => {
    try {
      await quitAndInstall(filePath);
    } catch (err) {
      Sentry.captureException('安装失败:' + err);
      setError(err instanceof Error ? err.message : '安装失败');
    }
  };

  useEffect(() => {
    // 检查本地是否有安装包
    const run = async () => {
      const result = await checkLocalInstaller(version);
      if (result.exists && result.path) {
        setHasLocalInstaller(true);
        setDownloadedFilePath(result.path);
      }
    };
    run();

    // 只监听下载进度
    onUpdateDownloadProgress((downloadProgress) => {
      setProgress(downloadProgress);
    });

    return () => {
      removeUpdateProgressListener();
    };
  }, [version]);

  useEffect(() => {
    // 组件卸载时的清理函数
    return () => {
      // 如果正在下载，保存当前的 minimized 状态
      if (downloading) {
        localStorage.setItem(
          `update_minimized_${version}`,
          JSON.stringify(minimized),
        );
      }
    };
  }, [downloading, minimized, version]);

  const handleUpdate = async () => {
    if (hasLocalInstaller && downloadedFilePath) {
      await handleInstall(downloadedFilePath);
      return;
    }

    try {
      setDownloading(true);
      setError(null);

      const result = await downloadUpdate(downloadUrl);

      if (result.success && result.filePath) {
        setDownloadedFilePath(result.filePath);
        if (minimizedRef.current) {
          Notice.success(
            <div>
              <p>{t('update.newVersionReady')}</p>
              <p>{t('update.newVersionReady')}</p>
              <div className="space-x-3">
                <Button size="sm">{t('update.remindLater')}</Button>
                <Button
                  size="sm"
                  onClick={() =>
                    result.filePath && handleInstall(result.filePath)
                  }
                >
                  {t('update.installNow')}
                </Button>
              </div>
            </div>,
          );
        } else {
          handleInstall(result.filePath);
        }
      } else {
        setError(result.error || '下载更新失败');
        setDownloading(false);
      }
    } catch (err) {
      Sentry.captureException('下载更新失败:' + err);
      setError(err instanceof Error ? err.message : '下载更新失败');
      setDownloading(false);
    }
  };

  const handleMinimize = () => {
    minimizedRef.current = true;
    setMinimized(true);
    localStorage.setItem(`update_minimized_${version}`, 'true');
    console.log('minimizedRef.current', minimizedRef.current);
    console.log('minimized', minimized);
    if (onClose) onClose();
  };

  if (minimized) return null;

  return (
    <Modal
      show
      // ! HACK 因为 flowbite-react bug
      className="bg-gray-900/50 dar:bg-gray-900/80"
    >
      <Modal.Body>
        <h4>
          {t('update.newVersionAvailable')}: {version}
        </h4>

        <div>
          <p>{t('update.whatsNew')}</p>
          <div>
            {upgradeContent[i18n.language] ||
              upgradeContent['en'] ||
              Object.values(upgradeContent)[0]}
          </div>
        </div>
        {downloading && (
          <div>
            <Progress
              progress={Math.round(progress)}
              // status={error ? 'exception' : 'active'}
            />
            <div>
              <p>{t('update.downloading')}...</p>
              {updateType !== 'force' && (
                <Button onClick={handleMinimize}>
                  {t('update.remindAfterDownload')}
                </Button>
              )}
            </div>
          </div>
        )}

        {error && <p className="text-red-600 py-4">{error}</p>}
        {!downloading && (
          <div className="flex justify-end gap-3">
            {!downloading && updateType !== 'force' && (
              <Button onClick={onClose} color="light">
                {t('update.remindLater')}
              </Button>
            )}
            {!downloading && (
              <Button onClick={handleUpdate} color="blue">
                {hasLocalInstaller
                  ? t('update.installNow')
                  : t('update.upgradeNow')}
              </Button>
            )}
          </div>
        )}
      </Modal.Body>
    </Modal>
  );
};

export default UpdateModal;
