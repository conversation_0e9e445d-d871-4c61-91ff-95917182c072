type StartDownloadDTO = import('@common/types/download').StartDownloadDTO;
type RendererFunction =
  import('@common/types/electron-bridge').RendererFunction;
type SimpleResult = import('@common/types/electron-bridge').SimpleResult;
type VideoInfo = import('@common/types/download').VideoInfo;
type Setting = import('@common/types/setting').Setting;
type FileInfo = import('@common/types/download').FileInfo;
type AppPathType = import('@common/types/electron-bridge').AppPathType;
type AuthSite = import('@common/types/setting').AuthSite;
type StoredDownloadTask = import('@common/types/download').StoredDownloadTask;

interface IpcRenderer {
  on(channel: string, func: RendererFunction): void;
  removeListener(channel: string, func: RendererFunction): void;
  onError(callback: RendererFunction): void;
}

interface ElectronAPI {
  download: (
    dto: StartDownloadDTO,
  ) => Promise<{ success: boolean; taskId: string; path: string }>;
  resumeDownload: (
    taskId: string,
  ) => Promise<{ success: boolean; path: string }>;
  cancelDownload: (taskId: string) => Promise<{ success: boolean }>;
  getDownloadVideoInfo: (
    url: string,
    taskId: string,
  ) => Promise<{
    success: boolean;
  }>;
  getSettings: () => Promise<Setting>;
  saveSettings: (settings: Setting) => Promise<{ success: boolean }>;
  selectDirectory: () => Promise<{ success: boolean; path: string }>;
  openFileLocation: (taskId: string) => Promise<SimpleResult>;
  openPathLocation: (path: string) => Promise<SimpleResult>;
  getFileInfo: (finalFilename: string) => Promise<FileInfo>;
  fetchImage: (
    url: string,
    headers?: HeadersInit,
  ) => Promise<{
    success: boolean;
    dataBase64?: string;
    error?: string;
  }>;
  openExternal: (url: string) => Promise<{
    success: boolean;
    error?: string;
  }>;
  getAppVersion: () => Promise<string>;
  logError: (error: {
    message: string;
    stack?: string;
    name?: string;
    context?: string;
    severity?: 'low' | 'medium' | 'high' | 'critical';
    category?: 'network' | 'parsing' | 'download' | 'system' | 'user';
  }) => Promise<void>;
  updateBrowserCookies: (browser: string) => Promise<{
    success: boolean;
    error?: string;
  }>;
  openAuthWindow: (
    url: string,
    siteKey: string,
  ) => Promise<{
    success: boolean;
    error?: string;
  }>;
  removeAuth: (url: string) => Promise<SimpleResult>;
  getPath: (name: AppPathType) => string;
  checkAuthStatus: (url: string) => Promise<boolean>;
  getSavedSites: () => Promise<importAuthSite[]>;
  saveSites: (sites: AuthSite[]) => Promise<SimpleResult>;
  clearJsonTasks: (taskId: string) => Promise<SimpleResult>;
  saveDownloadTask: (task: StoredDownloadTask) => Promise<SimpleResult>;
  getDownloadTask: (
    taskId: string,
  ) => Promise<StoredDownloadTask[] | undefined>;
  getDownloadTasks: () => Promise<StoredDownloadTask[]>;
  resumeDownload: (taskId: string) => Promise<void>;
  checkFileExists: (filename: string) => Promise<string>;
  checkForUpdates: () => Promise<{
    hasUpdate: boolean;
    version?: string;
    downloadUrl?: string;
    updateType?: 'force' | 'optional';
    upgradeContent?: { [key: string]: string };
    error?: string;
  }>;
  downloadUpdate: (url: string) => Promise<{
    success: boolean;
    filePath?: string;
    error?: string;
  }>;
  quitAndInstall: (filePath: string) => Promise<void>;
  onUpdateDownloadProgress: (callback: (progress: number) => void) => void;
  removeUpdateProgressListener: () => void;
  checkLocalInstaller: (version: string) => Promise<{
    exists: boolean;
    path?: string;
  }>;
  onUpdateDownloaded: (callback: (filePath: string) => void) => void;
  removeUpdateDownloadedListener: () => void;
  platform: string;
  changeLanguage: (lang: string) => Promise<SimpleResult>;
  getSystemLanguage: () => Promise<string>;
}

declare global {
  interface Window {
    electron: {
      ipcRenderer: IpcRenderer;
    };
    electronAPI: ElectronAPI;
  }
}
export {};
