var xs=Object.defineProperty;var Ts=(e,t,n)=>t in e?xs(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var Br=(e,t,n)=>Ts(e,typeof t!="symbol"?t+"":t,n);import{G as H,j as d,T as dn,a as Kr,B as Pe,L as zn,b as _t,c as un,S as pi,D as De,M as to,C as Gr,P as ks,d as _s,e as As,f as Cs}from"./ui-CwomyVM7.js";import{d as Is,g as Ps,a as h,b as Ds,u as Ls,e as Fs,f as Rs,r as Ns,R as Nt,N as Os,h as Ms,H as js}from"./vendor-DjSYeWVf.js";import{u as W,t as qr,i as cr,a as Us,B as $s,I as Vs}from"./i18n-BHlLMDo8.js";(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const r of document.querySelectorAll('link[rel="modulepreload"]'))o(r);new MutationObserver(r=>{for(const a of r)if(a.type==="childList")for(const i of a.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&o(i)}).observe(document,{childList:!0,subtree:!0});function n(r){const a={};return r.integrity&&(a.integrity=r.integrity),r.referrerPolicy&&(a.referrerPolicy=r.referrerPolicy),r.crossOrigin==="use-credentials"?a.credentials="include":r.crossOrigin==="anonymous"?a.credentials="omit":a.credentials="same-origin",a}function o(r){if(r.ep)return;r.ep=!0;const a=n(r);fetch(r.href,a)}})();const F=typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__,Ye="8.51.0",I=globalThis;function Bn(e,t,n){const o=I,r=o.__SENTRY__=o.__SENTRY__||{},a=r[Ye]=r[Ye]||{};return a[e]||(a[e]=t())}const nt=typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__,Hs="Sentry Logger ",Ao=["debug","info","warn","error","log","assert","trace"],pn={};function ot(e){if(!("console"in I))return e();const t=I.console,n={},o=Object.keys(pn);o.forEach(r=>{const a=pn[r];n[r]=t[r],t[r]=a});try{return e()}finally{o.forEach(r=>{t[r]=n[r]})}}function zs(){let e=!1;const t={enable:()=>{e=!0},disable:()=>{e=!1},isEnabled:()=>e};return nt?Ao.forEach(n=>{t[n]=(...o)=>{e&&ot(()=>{I.console[n](`${Hs}[${n}]:`,...o)})}}):Ao.forEach(n=>{t[n]=()=>{}}),t}const S=Bn("logger",zs),fi=50,He="?",Wr=/\(error: (.*)\)/,Yr=/captureMessage|captureException/;function mi(...e){const t=e.sort((n,o)=>n[0]-o[0]).map(n=>n[1]);return(n,o=0,r=0)=>{const a=[],i=n.split(`
`);for(let s=o;s<i.length;s++){const l=i[s];if(l.length>1024)continue;const c=Wr.test(l)?l.replace(Wr,"$1"):l;if(!c.match(/\S*Error: /)){for(const u of t){const p=u(c);if(p){a.push(p);break}}if(a.length>=fi+r)break}}return hi(a.slice(r))}}function Bs(e){return Array.isArray(e)?mi(...e):e}function hi(e){if(!e.length)return[];const t=Array.from(e);return/sentryWrapped/.test(Kt(t).function||"")&&t.pop(),t.reverse(),Yr.test(Kt(t).function||"")&&(t.pop(),Yr.test(Kt(t).function||"")&&t.pop()),t.slice(0,fi).map(n=>({...n,filename:n.filename||Kt(t).filename,function:n.function||He}))}function Kt(e){return e[e.length-1]||{}}const no="<anonymous>";function ze(e){try{return!e||typeof e!="function"?no:e.name||no}catch{return no}}function Qr(e){const t=e.exception;if(t){const n=[];try{return t.values.forEach(o=>{o.stacktrace.frames&&n.push(...o.stacktrace.frames)}),n}catch{return}}}const sn={},Xr={};function rt(e,t){sn[e]=sn[e]||[],sn[e].push(t)}function at(e,t){if(!Xr[e]){Xr[e]=!0;try{t()}catch(n){nt&&S.error(`Error while instrumenting ${e}`,n)}}}function ue(e,t){const n=e&&sn[e];if(n)for(const o of n)try{o(t)}catch(r){nt&&S.error(`Error while triggering instrumentation handler.
Type: ${e}
Name: ${ze(o)}
Error:`,r)}}let oo=null;function Ks(e){const t="error";rt(t,e),at(t,Gs)}function Gs(){oo=I.onerror,I.onerror=function(e,t,n,o,r){return ue("error",{column:o,error:r,line:n,msg:e,url:t}),oo?oo.apply(this,arguments):!1},I.onerror.__SENTRY_INSTRUMENTED__=!0}let ro=null;function qs(e){const t="unhandledrejection";rt(t,e),at(t,Ws)}function Ws(){ro=I.onunhandledrejection,I.onunhandledrejection=function(e){return ue("unhandledrejection",e),ro?ro.apply(this,arguments):!0},I.onunhandledrejection.__SENTRY_INSTRUMENTED__=!0}function Kn(){return dr(I),I}function dr(e){const t=e.__SENTRY__=e.__SENTRY__||{};return t.version=t.version||Ye,t[Ye]=t[Ye]||{}}const gi=Object.prototype.toString;function ur(e){switch(gi.call(e)){case"[object Error]":case"[object Exception]":case"[object DOMException]":case"[object WebAssembly.Exception]":return!0;default:return Qe(e,Error)}}function St(e,t){return gi.call(e)===`[object ${t}]`}function yi(e){return St(e,"ErrorEvent")}function Jr(e){return St(e,"DOMError")}function Ys(e){return St(e,"DOMException")}function Le(e){return St(e,"String")}function pr(e){return typeof e=="object"&&e!==null&&"__sentry_template_string__"in e&&"__sentry_template_values__"in e}function fr(e){return e===null||pr(e)||typeof e!="object"&&typeof e!="function"}function ht(e){return St(e,"Object")}function Gn(e){return typeof Event<"u"&&Qe(e,Event)}function Qs(e){return typeof Element<"u"&&Qe(e,Element)}function Xs(e){return St(e,"RegExp")}function qn(e){return!!(e&&e.then&&typeof e.then=="function")}function Js(e){return ht(e)&&"nativeEvent"in e&&"preventDefault"in e&&"stopPropagation"in e}function Qe(e,t){try{return e instanceof t}catch{return!1}}function vi(e){return!!(typeof e=="object"&&e!==null&&(e.__isVue||e._isVue))}const mr=I,Zs=80;function wi(e,t={}){if(!e)return"<unknown>";try{let n=e;const o=5,r=[];let a=0,i=0;const s=" > ",l=s.length;let c;const u=Array.isArray(t)?t:t.keyAttrs,p=!Array.isArray(t)&&t.maxStringLength||Zs;for(;n&&a++<o&&(c=el(n,u),!(c==="html"||a>1&&i+r.length*l+c.length>=p));)r.push(c),i+=c.length,n=n.parentNode;return r.reverse().join(s)}catch{return"<unknown>"}}function el(e,t){const n=e,o=[];if(!n||!n.tagName)return"";if(mr.HTMLElement&&n instanceof HTMLElement&&n.dataset){if(n.dataset.sentryComponent)return n.dataset.sentryComponent;if(n.dataset.sentryElement)return n.dataset.sentryElement}o.push(n.tagName.toLowerCase());const r=t&&t.length?t.filter(i=>n.getAttribute(i)).map(i=>[i,n.getAttribute(i)]):null;if(r&&r.length)r.forEach(i=>{o.push(`[${i[0]}="${i[1]}"]`)});else{n.id&&o.push(`#${n.id}`);const i=n.className;if(i&&Le(i)){const s=i.split(/\s+/);for(const l of s)o.push(`.${l}`)}}const a=["aria-label","type","name","title","alt"];for(const i of a){const s=n.getAttribute(i);s&&o.push(`[${i}="${s}"]`)}return o.join("")}function tl(){try{return mr.document.location.href}catch{return""}}function nl(e){if(!mr.HTMLElement)return null;let t=e;const n=5;for(let o=0;o<n;o++){if(!t)return null;if(t instanceof HTMLElement){if(t.dataset.sentryComponent)return t.dataset.sentryComponent;if(t.dataset.sentryElement)return t.dataset.sentryElement}t=t.parentNode}return null}function ft(e,t=0){return typeof e!="string"||t===0||e.length<=t?e:`${e.slice(0,t)}...`}function Zr(e,t){if(!Array.isArray(e))return"";const n=[];for(let o=0;o<e.length;o++){const r=e[o];try{vi(r)?n.push("[VueViewModel]"):n.push(String(r))}catch{n.push("[value cannot be serialized]")}}return n.join(t)}function ol(e,t,n=!1){return Le(e)?Xs(t)?t.test(e):Le(t)?n?e===t:e.includes(t):!1:!1}function Wn(e,t=[],n=!1){return t.some(o=>ol(e,o,n))}function oe(e,t,n){if(!(t in e))return;const o=e[t],r=n(o);typeof r=="function"&&bi(r,o);try{e[t]=r}catch{nt&&S.log(`Failed to replace method "${t}" in object`,e)}}function Xe(e,t,n){try{Object.defineProperty(e,t,{value:n,writable:!0,configurable:!0})}catch{nt&&S.log(`Failed to add non-enumerable property "${t}" to object`,e)}}function bi(e,t){try{const n=t.prototype||{};e.prototype=t.prototype=n,Xe(e,"__sentry_original__",t)}catch{}}function hr(e){return e.__sentry_original__}function Si(e){if(ur(e))return{message:e.message,name:e.name,stack:e.stack,...ta(e)};if(Gn(e)){const t={type:e.type,target:ea(e.target),currentTarget:ea(e.currentTarget),...ta(e)};return typeof CustomEvent<"u"&&Qe(e,CustomEvent)&&(t.detail=e.detail),t}else return e}function ea(e){try{return Qs(e)?wi(e):Object.prototype.toString.call(e)}catch{return"<unknown>"}}function ta(e){if(typeof e=="object"&&e!==null){const t={};for(const n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t}else return{}}function rl(e,t=40){const n=Object.keys(Si(e));n.sort();const o=n[0];if(!o)return"[object has no keys]";if(o.length>=t)return ft(o,t);for(let r=n.length;r>0;r--){const a=n.slice(0,r).join(", ");if(!(a.length>t))return r===n.length?a:ft(a,t)}return""}function re(e){return Co(e,new Map)}function Co(e,t){if(al(e)){const n=t.get(e);if(n!==void 0)return n;const o={};t.set(e,o);for(const r of Object.getOwnPropertyNames(e))typeof e[r]<"u"&&(o[r]=Co(e[r],t));return o}if(Array.isArray(e)){const n=t.get(e);if(n!==void 0)return n;const o=[];return t.set(e,o),e.forEach(r=>{o.push(Co(r,t))}),o}return e}function al(e){if(!ht(e))return!1;try{const t=Object.getPrototypeOf(e).constructor.name;return!t||t==="Object"}catch{return!0}}const Ei=1e3;function Ut(){return Date.now()/Ei}function il(){const{performance:e}=I;if(!e||!e.now)return Ut;const t=Date.now()-e.now(),n=e.timeOrigin==null?t:e.timeOrigin;return()=>(n+e.now())/Ei}const Fe=il();(()=>{const{performance:e}=I;if(!e||!e.now)return;const t=3600*1e3,n=e.now(),o=Date.now(),r=e.timeOrigin?Math.abs(e.timeOrigin+n-o):t,a=r<t,i=e.timing&&e.timing.navigationStart,l=typeof i=="number"?Math.abs(i+n-o):t,c=l<t;return a||c?r<=l?e.timeOrigin:i:o})();function ae(){const e=I,t=e.crypto||e.msCrypto;let n=()=>Math.random()*16;try{if(t&&t.randomUUID)return t.randomUUID().replace(/-/g,"");t&&t.getRandomValues&&(n=()=>{const o=new Uint8Array(1);return t.getRandomValues(o),o[0]})}catch{}return("10000000100040008000"+1e11).replace(/[018]/g,o=>(o^(n()&15)>>o/4).toString(16))}function xi(e){return e.exception&&e.exception.values?e.exception.values[0]:void 0}function Me(e){const{message:t,event_id:n}=e;if(t)return t;const o=xi(e);return o?o.type&&o.value?`${o.type}: ${o.value}`:o.type||o.value||n||"<unknown>":n||"<unknown>"}function Io(e,t,n){const o=e.exception=e.exception||{},r=o.values=o.values||[],a=r[0]=r[0]||{};a.value||(a.value=t||""),a.type||(a.type="Error")}function gt(e,t){const n=xi(e);if(!n)return;const o={type:"generic",handled:!0},r=n.mechanism;if(n.mechanism={...o,...r,...t},t&&"data"in t){const a={...r&&r.data,...t.data};n.mechanism.data=a}}function na(e){if(sl(e))return!0;try{Xe(e,"__sentry_captured__",!0)}catch{}return!1}function sl(e){try{return e.__sentry_captured__}catch{}}var Ce;(function(e){e[e.PENDING=0]="PENDING";const n=1;e[e.RESOLVED=n]="RESOLVED";const o=2;e[e.REJECTED=o]="REJECTED"})(Ce||(Ce={}));function Je(e){return new ce(t=>{t(e)})}function fn(e){return new ce((t,n)=>{n(e)})}class ce{constructor(t){ce.prototype.__init.call(this),ce.prototype.__init2.call(this),ce.prototype.__init3.call(this),ce.prototype.__init4.call(this),this._state=Ce.PENDING,this._handlers=[];try{t(this._resolve,this._reject)}catch(n){this._reject(n)}}then(t,n){return new ce((o,r)=>{this._handlers.push([!1,a=>{if(!t)o(a);else try{o(t(a))}catch(i){r(i)}},a=>{if(!n)r(a);else try{o(n(a))}catch(i){r(i)}}]),this._executeHandlers()})}catch(t){return this.then(n=>n,t)}finally(t){return new ce((n,o)=>{let r,a;return this.then(i=>{a=!1,r=i,t&&t()},i=>{a=!0,r=i,t&&t()}).then(()=>{if(a){o(r);return}n(r)})})}__init(){this._resolve=t=>{this._setResult(Ce.RESOLVED,t)}}__init2(){this._reject=t=>{this._setResult(Ce.REJECTED,t)}}__init3(){this._setResult=(t,n)=>{if(this._state===Ce.PENDING){if(qn(n)){n.then(this._resolve,this._reject);return}this._state=t,this._value=n,this._executeHandlers()}}}__init4(){this._executeHandlers=()=>{if(this._state===Ce.PENDING)return;const t=this._handlers.slice();this._handlers=[],t.forEach(n=>{n[0]||(this._state===Ce.RESOLVED&&n[1](this._value),this._state===Ce.REJECTED&&n[2](this._value),n[0]=!0)})}}}function ll(e){const t=Fe(),n={sid:ae(),init:!0,timestamp:t,started:t,duration:0,status:"ok",errors:0,ignoreDuration:!1,toJSON:()=>dl(n)};return e&&yt(n,e),n}function yt(e,t={}){if(t.user&&(!e.ipAddress&&t.user.ip_address&&(e.ipAddress=t.user.ip_address),!e.did&&!t.did&&(e.did=t.user.id||t.user.email||t.user.username)),e.timestamp=t.timestamp||Fe(),t.abnormal_mechanism&&(e.abnormal_mechanism=t.abnormal_mechanism),t.ignoreDuration&&(e.ignoreDuration=t.ignoreDuration),t.sid&&(e.sid=t.sid.length===32?t.sid:ae()),t.init!==void 0&&(e.init=t.init),!e.did&&t.did&&(e.did=`${t.did}`),typeof t.started=="number"&&(e.started=t.started),e.ignoreDuration)e.duration=void 0;else if(typeof t.duration=="number")e.duration=t.duration;else{const n=e.timestamp-e.started;e.duration=n>=0?n:0}t.release&&(e.release=t.release),t.environment&&(e.environment=t.environment),!e.ipAddress&&t.ipAddress&&(e.ipAddress=t.ipAddress),!e.userAgent&&t.userAgent&&(e.userAgent=t.userAgent),typeof t.errors=="number"&&(e.errors=t.errors),t.status&&(e.status=t.status)}function cl(e,t){let n={};e.status==="ok"&&(n={status:"exited"}),yt(e,n)}function dl(e){return re({sid:`${e.sid}`,init:e.init,started:new Date(e.started*1e3).toISOString(),timestamp:new Date(e.timestamp*1e3).toISOString(),status:e.status,errors:e.errors,did:typeof e.did=="number"||typeof e.did=="string"?`${e.did}`:void 0,duration:e.duration,abnormal_mechanism:e.abnormal_mechanism,attrs:{release:e.release,environment:e.environment,ip_address:e.ipAddress,user_agent:e.userAgent}})}function oa(){return ae()}function Po(){return ae().substring(16)}function Yn(e,t,n=2){if(!t||typeof t!="object"||n<=0)return t;if(e&&t&&Object.keys(t).length===0)return e;const o={...e};for(const r in t)Object.prototype.hasOwnProperty.call(t,r)&&(o[r]=Yn(o[r],t[r],n-1));return o}const Do="_sentrySpan";function ra(e,t){t?Xe(e,Do,t):delete e[Do]}function aa(e){return e[Do]}const ul=100;class gr{constructor(){this._notifyingListeners=!1,this._scopeListeners=[],this._eventProcessors=[],this._breadcrumbs=[],this._attachments=[],this._user={},this._tags={},this._extra={},this._contexts={},this._sdkProcessingMetadata={},this._propagationContext={traceId:oa(),spanId:Po()}}clone(){const t=new gr;return t._breadcrumbs=[...this._breadcrumbs],t._tags={...this._tags},t._extra={...this._extra},t._contexts={...this._contexts},this._contexts.flags&&(t._contexts.flags={values:[...this._contexts.flags.values]}),t._user=this._user,t._level=this._level,t._session=this._session,t._transactionName=this._transactionName,t._fingerprint=this._fingerprint,t._eventProcessors=[...this._eventProcessors],t._requestSession=this._requestSession,t._attachments=[...this._attachments],t._sdkProcessingMetadata={...this._sdkProcessingMetadata},t._propagationContext={...this._propagationContext},t._client=this._client,t._lastEventId=this._lastEventId,ra(t,aa(this)),t}setClient(t){this._client=t}setLastEventId(t){this._lastEventId=t}getClient(){return this._client}lastEventId(){return this._lastEventId}addScopeListener(t){this._scopeListeners.push(t)}addEventProcessor(t){return this._eventProcessors.push(t),this}setUser(t){return this._user=t||{email:void 0,id:void 0,ip_address:void 0,username:void 0},this._session&&yt(this._session,{user:t}),this._notifyScopeListeners(),this}getUser(){return this._user}getRequestSession(){return this._requestSession}setRequestSession(t){return this._requestSession=t,this}setTags(t){return this._tags={...this._tags,...t},this._notifyScopeListeners(),this}setTag(t,n){return this._tags={...this._tags,[t]:n},this._notifyScopeListeners(),this}setExtras(t){return this._extra={...this._extra,...t},this._notifyScopeListeners(),this}setExtra(t,n){return this._extra={...this._extra,[t]:n},this._notifyScopeListeners(),this}setFingerprint(t){return this._fingerprint=t,this._notifyScopeListeners(),this}setLevel(t){return this._level=t,this._notifyScopeListeners(),this}setTransactionName(t){return this._transactionName=t,this._notifyScopeListeners(),this}setContext(t,n){return n===null?delete this._contexts[t]:this._contexts[t]=n,this._notifyScopeListeners(),this}setSession(t){return t?this._session=t:delete this._session,this._notifyScopeListeners(),this}getSession(){return this._session}update(t){if(!t)return this;const n=typeof t=="function"?t(this):t,[o,r]=n instanceof Ze?[n.getScopeData(),n.getRequestSession()]:ht(n)?[t,t.requestSession]:[],{tags:a,extra:i,user:s,contexts:l,level:c,fingerprint:u=[],propagationContext:p}=o||{};return this._tags={...this._tags,...a},this._extra={...this._extra,...i},this._contexts={...this._contexts,...l},s&&Object.keys(s).length&&(this._user=s),c&&(this._level=c),u.length&&(this._fingerprint=u),p&&(this._propagationContext=p),r&&(this._requestSession=r),this}clear(){return this._breadcrumbs=[],this._tags={},this._extra={},this._user={},this._contexts={},this._level=void 0,this._transactionName=void 0,this._fingerprint=void 0,this._requestSession=void 0,this._session=void 0,ra(this,void 0),this._attachments=[],this.setPropagationContext({traceId:oa()}),this._notifyScopeListeners(),this}addBreadcrumb(t,n){const o=typeof n=="number"?n:ul;if(o<=0)return this;const r={timestamp:Ut(),...t},a=this._breadcrumbs;return a.push(r),this._breadcrumbs=a.length>o?a.slice(-o):a,this._notifyScopeListeners(),this}getLastBreadcrumb(){return this._breadcrumbs[this._breadcrumbs.length-1]}clearBreadcrumbs(){return this._breadcrumbs=[],this._notifyScopeListeners(),this}addAttachment(t){return this._attachments.push(t),this}clearAttachments(){return this._attachments=[],this}getScopeData(){return{breadcrumbs:this._breadcrumbs,attachments:this._attachments,contexts:this._contexts,tags:this._tags,extra:this._extra,user:this._user,level:this._level,fingerprint:this._fingerprint||[],eventProcessors:this._eventProcessors,propagationContext:this._propagationContext,sdkProcessingMetadata:this._sdkProcessingMetadata,transactionName:this._transactionName,span:aa(this)}}setSDKProcessingMetadata(t){return this._sdkProcessingMetadata=Yn(this._sdkProcessingMetadata,t,2),this}setPropagationContext(t){return this._propagationContext={spanId:Po(),...t},this}getPropagationContext(){return this._propagationContext}captureException(t,n){const o=n&&n.event_id?n.event_id:ae();if(!this._client)return S.warn("No client configured on scope - will not capture exception!"),o;const r=new Error("Sentry syntheticException");return this._client.captureException(t,{originalException:t,syntheticException:r,...n,event_id:o},this),o}captureMessage(t,n,o){const r=o&&o.event_id?o.event_id:ae();if(!this._client)return S.warn("No client configured on scope - will not capture message!"),r;const a=new Error(t);return this._client.captureMessage(t,n,{originalException:t,syntheticException:a,...o,event_id:r},this),r}captureEvent(t,n){const o=n&&n.event_id?n.event_id:ae();return this._client?(this._client.captureEvent(t,{...n,event_id:o},this),o):(S.warn("No client configured on scope - will not capture event!"),o)}_notifyScopeListeners(){this._notifyingListeners||(this._notifyingListeners=!0,this._scopeListeners.forEach(t=>{t(this)}),this._notifyingListeners=!1)}}const Ze=gr;function pl(){return Bn("defaultCurrentScope",()=>new Ze)}function fl(){return Bn("defaultIsolationScope",()=>new Ze)}class ml{constructor(t,n){let o;t?o=t:o=new Ze;let r;n?r=n:r=new Ze,this._stack=[{scope:o}],this._isolationScope=r}withScope(t){const n=this._pushScope();let o;try{o=t(n)}catch(r){throw this._popScope(),r}return qn(o)?o.then(r=>(this._popScope(),r),r=>{throw this._popScope(),r}):(this._popScope(),o)}getClient(){return this.getStackTop().client}getScope(){return this.getStackTop().scope}getIsolationScope(){return this._isolationScope}getStackTop(){return this._stack[this._stack.length-1]}_pushScope(){const t=this.getScope().clone();return this._stack.push({client:this.getClient(),scope:t}),t}_popScope(){return this._stack.length<=1?!1:!!this._stack.pop()}}function vt(){const e=Kn(),t=dr(e);return t.stack=t.stack||new ml(pl(),fl())}function hl(e){return vt().withScope(e)}function gl(e,t){const n=vt();return n.withScope(()=>(n.getStackTop().scope=e,t(e)))}function ia(e){return vt().withScope(()=>e(vt().getIsolationScope()))}function yl(){return{withIsolationScope:ia,withScope:hl,withSetScope:gl,withSetIsolationScope:(e,t)=>ia(t),getCurrentScope:()=>vt().getScope(),getIsolationScope:()=>vt().getIsolationScope()}}function yr(e){const t=dr(e);return t.acs?t.acs:yl()}function pe(){const e=Kn();return yr(e).getCurrentScope()}function it(){const e=Kn();return yr(e).getIsolationScope()}function vr(){return Bn("globalScope",()=>new Ze)}function vl(...e){const t=Kn(),n=yr(t);if(e.length===2){const[o,r]=e;return o?n.withSetScope(o,r):n.withScope(r)}return n.withScope(e[0])}function Y(){return pe().getClient()}function wl(e){const t=e.getPropagationContext(),{traceId:n,spanId:o,parentSpanId:r}=t;return re({trace_id:n,span_id:o,parent_span_id:r})}const bl="_sentryMetrics";function Sl(e){const t=e[bl];if(!t)return;const n={};for(const[,[o,r]]of t)(n[o]||(n[o]=[])).push(re(r));return n}const El="sentry.source",xl="sentry.sample_rate",Tl="sentry.op",kl="sentry.origin",_l=0,Al=1,Cl="sentry-",Il=/^sentry-/;function Pl(e){const t=Dl(e);if(!t)return;const n=Object.entries(t).reduce((o,[r,a])=>{if(r.match(Il)){const i=r.slice(Cl.length);o[i]=a}return o},{});if(Object.keys(n).length>0)return n}function Dl(e){if(!(!e||!Le(e)&&!Array.isArray(e)))return Array.isArray(e)?e.reduce((t,n)=>{const o=sa(n);return Object.entries(o).forEach(([r,a])=>{t[r]=a}),t},{}):sa(e)}function sa(e){return e.split(",").map(t=>t.split("=").map(n=>decodeURIComponent(n.trim()))).reduce((t,[n,o])=>(n&&o&&(t[n]=o),t),{})}const Ll=1;let la=!1;function Fl(e){const{spanId:t,traceId:n,isRemote:o}=e.spanContext(),r=o?t:wr(e).parent_span_id,a=o?Po():t;return re({parent_span_id:r,span_id:a,trace_id:n})}function ca(e){return typeof e=="number"?da(e):Array.isArray(e)?e[0]+e[1]/1e9:e instanceof Date?da(e.getTime()):Fe()}function da(e){return e>9999999999?e/1e3:e}function wr(e){if(Nl(e))return e.getSpanJSON();try{const{spanId:t,traceId:n}=e.spanContext();if(Rl(e)){const{attributes:o,startTime:r,name:a,endTime:i,parentSpanId:s,status:l}=e;return re({span_id:t,trace_id:n,data:o,description:a,parent_span_id:s,start_timestamp:ca(r),timestamp:ca(i)||void 0,status:Ml(l),op:o[Tl],origin:o[kl],_metrics_summary:Sl(e)})}return{span_id:t,trace_id:n}}catch{return{}}}function Rl(e){const t=e;return!!t.attributes&&!!t.startTime&&!!t.name&&!!t.endTime&&!!t.status}function Nl(e){return typeof e.getSpanJSON=="function"}function Ol(e){const{traceFlags:t}=e.spanContext();return t===Ll}function Ml(e){if(!(!e||e.code===_l))return e.code===Al?"ok":e.message||"unknown_error"}const jl="_sentryRootSpan";function Ti(e){return e[jl]||e}function Ul(){la||(ot(()=>{console.warn("[Sentry] Deprecation warning: Returning null from `beforeSendSpan` will be disallowed from SDK version 9.0.0 onwards. The callback will only support mutating spans. To drop certain spans, configure the respective integrations directly.")}),la=!0)}function $l(e){if(typeof __SENTRY_TRACING__=="boolean"&&!__SENTRY_TRACING__)return!1;const t=Y(),n=t&&t.getOptions();return!!n&&(n.enableTracing||"tracesSampleRate"in n||"tracesSampler"in n)}const br="production",Vl="_frozenDsc";function ki(e,t){const n=t.getOptions(),{publicKey:o}=t.getDsn()||{},r=re({environment:n.environment||br,release:n.release,public_key:o,trace_id:e});return t.emit("createDsc",r),r}function Hl(e,t){const n=t.getPropagationContext();return n.dsc||ki(n.traceId,e)}function zl(e){const t=Y();if(!t)return{};const n=Ti(e),o=n[Vl];if(o)return o;const r=n.spanContext().traceState,a=r&&r.get("sentry.dsc"),i=a&&Pl(a);if(i)return i;const s=ki(e.spanContext().traceId,t),l=wr(n),c=l.data||{},u=c[xl];u!=null&&(s.sample_rate=`${u}`);const p=c[El],f=l.description;return p!=="url"&&f&&(s.transaction=f),$l()&&(s.sampled=String(Ol(n))),t.emit("createDsc",s,n),s}function Bl(e){if(typeof e=="boolean")return Number(e);const t=typeof e=="string"?parseFloat(e):e;if(typeof t!="number"||isNaN(t)||t<0||t>1){F&&S.warn(`[Tracing] Given sample rate is invalid. Sample rate must be a boolean or a number between 0 and 1. Got ${JSON.stringify(e)} of type ${JSON.stringify(typeof e)}.`);return}return t}const Kl=/^(?:(\w+):)\/\/(?:(\w+)(?::(\w+)?)?@)([\w.-]+)(?::(\d+))?\/(.+)/;function Gl(e){return e==="http"||e==="https"}function Qn(e,t=!1){const{host:n,path:o,pass:r,port:a,projectId:i,protocol:s,publicKey:l}=e;return`${s}://${l}${t&&r?`:${r}`:""}@${n}${a?`:${a}`:""}/${o&&`${o}/`}${i}`}function ql(e){const t=Kl.exec(e);if(!t){ot(()=>{console.error(`Invalid Sentry Dsn: ${e}`)});return}const[n,o,r="",a="",i="",s=""]=t.slice(1);let l="",c=s;const u=c.split("/");if(u.length>1&&(l=u.slice(0,-1).join("/"),c=u.pop()),c){const p=c.match(/^\d+/);p&&(c=p[0])}return _i({host:a,pass:r,path:l,projectId:c,port:i,protocol:n,publicKey:o})}function _i(e){return{protocol:e.protocol,publicKey:e.publicKey||"",pass:e.pass||"",host:e.host,port:e.port||"",path:e.path||"",projectId:e.projectId}}function Wl(e){if(!nt)return!0;const{port:t,projectId:n,protocol:o}=e;return["protocol","publicKey","host","projectId"].find(i=>e[i]?!1:(S.error(`Invalid Sentry Dsn: ${i} missing`),!0))?!1:n.match(/^\d+$/)?Gl(o)?t&&isNaN(parseInt(t,10))?(S.error(`Invalid Sentry Dsn: Invalid port ${t}`),!1):!0:(S.error(`Invalid Sentry Dsn: Invalid protocol ${o}`),!1):(S.error(`Invalid Sentry Dsn: Invalid projectId ${n}`),!1)}function Yl(e){const t=typeof e=="string"?ql(e):_i(e);if(!(!t||!Wl(t)))return t}function Ql(){const e=typeof WeakSet=="function",t=e?new WeakSet:[];function n(r){if(e)return t.has(r)?!0:(t.add(r),!1);for(let a=0;a<t.length;a++)if(t[a]===r)return!0;return t.push(r),!1}function o(r){if(e)t.delete(r);else for(let a=0;a<t.length;a++)if(t[a]===r){t.splice(a,1);break}}return[n,o]}function ye(e,t=100,n=1/0){try{return Lo("",e,t,n)}catch(o){return{ERROR:`**non-serializable** (${o})`}}}function Ai(e,t=3,n=100*1024){const o=ye(e,t);return ec(o)>n?Ai(e,t-1,n):o}function Lo(e,t,n=1/0,o=1/0,r=Ql()){const[a,i]=r;if(t==null||["boolean","string"].includes(typeof t)||typeof t=="number"&&Number.isFinite(t))return t;const s=Xl(e,t);if(!s.startsWith("[object "))return s;if(t.__sentry_skip_normalization__)return t;const l=typeof t.__sentry_override_normalization_depth__=="number"?t.__sentry_override_normalization_depth__:n;if(l===0)return s.replace("object ","");if(a(t))return"[Circular ~]";const c=t;if(c&&typeof c.toJSON=="function")try{const m=c.toJSON();return Lo("",m,l-1,o,r)}catch{}const u=Array.isArray(t)?[]:{};let p=0;const f=Si(t);for(const m in f){if(!Object.prototype.hasOwnProperty.call(f,m))continue;if(p>=o){u[m]="[MaxProperties ~]";break}const v=f[m];u[m]=Lo(m,v,l-1,o,r),p++}return i(t),u}function Xl(e,t){try{if(e==="domain"&&t&&typeof t=="object"&&t._events)return"[Domain]";if(e==="domainEmitter")return"[DomainEmitter]";if(typeof global<"u"&&t===global)return"[Global]";if(typeof window<"u"&&t===window)return"[Window]";if(typeof document<"u"&&t===document)return"[Document]";if(vi(t))return"[VueViewModel]";if(Js(t))return"[SyntheticEvent]";if(typeof t=="number"&&!Number.isFinite(t))return`[${t}]`;if(typeof t=="function")return`[Function: ${ze(t)}]`;if(typeof t=="symbol")return`[${String(t)}]`;if(typeof t=="bigint")return`[BigInt: ${String(t)}]`;const n=Jl(t);return/^HTML(\w*)Element$/.test(n)?`[HTMLElement: ${n}]`:`[object ${n}]`}catch(n){return`**non-serializable** (${n})`}}function Jl(e){const t=Object.getPrototypeOf(e);return t?t.constructor.name:"null prototype"}function Zl(e){return~-encodeURI(e).split(/%..|./).length}function ec(e){return Zl(JSON.stringify(e))}function $t(e,t=[]){return[e,t]}function tc(e,t){const[n,o]=e;return[n,[...o,t]]}function ua(e,t){const n=e[1];for(const o of n){const r=o[0].type;if(t(o,r))return!0}return!1}function Fo(e){return I.__SENTRY__&&I.__SENTRY__.encodePolyfill?I.__SENTRY__.encodePolyfill(e):new TextEncoder().encode(e)}function nc(e){const[t,n]=e;let o=JSON.stringify(t);function r(a){typeof o=="string"?o=typeof a=="string"?o+a:[Fo(o),a]:o.push(typeof a=="string"?Fo(a):a)}for(const a of n){const[i,s]=a;if(r(`
${JSON.stringify(i)}
`),typeof s=="string"||s instanceof Uint8Array)r(s);else{let l;try{l=JSON.stringify(s)}catch{l=JSON.stringify(ye(s))}r(l)}}return typeof o=="string"?o:oc(o)}function oc(e){const t=e.reduce((r,a)=>r+a.length,0),n=new Uint8Array(t);let o=0;for(const r of e)n.set(r,o),o+=r.length;return n}function rc(e){const t=typeof e.data=="string"?Fo(e.data):e.data;return[re({type:"attachment",length:t.length,filename:e.filename,content_type:e.contentType,attachment_type:e.attachmentType}),t]}const ac={session:"session",sessions:"session",attachment:"attachment",transaction:"transaction",event:"error",client_report:"internal",user_report:"default",profile:"profile",profile_chunk:"profile",replay_event:"replay",replay_recording:"replay",check_in:"monitor",feedback:"feedback",span:"span",statsd:"metric_bucket",raw_security:"security"};function pa(e){return ac[e]}function Ci(e){if(!e||!e.sdk)return;const{name:t,version:n}=e.sdk;return{name:t,version:n}}function ic(e,t,n,o){const r=e.sdkProcessingMetadata&&e.sdkProcessingMetadata.dynamicSamplingContext;return{event_id:e.event_id,sent_at:new Date().toISOString(),...t&&{sdk:t},...!!n&&o&&{dsn:Qn(o)},...r&&{trace:re({...r})}}}function sc(e,t){return t&&(e.sdk=e.sdk||{},e.sdk.name=e.sdk.name||t.name,e.sdk.version=e.sdk.version||t.version,e.sdk.integrations=[...e.sdk.integrations||[],...t.integrations||[]],e.sdk.packages=[...e.sdk.packages||[],...t.packages||[]]),e}function lc(e,t,n,o){const r=Ci(n),a={sent_at:new Date().toISOString(),...r&&{sdk:r},...!!o&&t&&{dsn:Qn(t)}},i="aggregates"in e?[{type:"sessions"},e]:[{type:"session"},e.toJSON()];return $t(a,[i])}function cc(e,t,n,o){const r=Ci(n),a=e.type&&e.type!=="replay_event"?e.type:"event";sc(e,n&&n.sdk);const i=ic(e,r,o,t);return delete e.sdkProcessingMetadata,$t(i,[[{type:a},e]])}function Ro(e,t,n,o=0){return new ce((r,a)=>{const i=e[o];if(t===null||typeof i!="function")r(t);else{const s=i({...t},n);F&&i.id&&s===null&&S.log(`Event processor "${i.id}" dropped event`),qn(s)?s.then(l=>Ro(e,l,n,o+1).then(r)).then(null,a):Ro(e,s,n,o+1).then(r).then(null,a)}})}let Gt,fa,qt;function dc(e){const t=I._sentryDebugIds;if(!t)return{};const n=Object.keys(t);return qt&&n.length===fa||(fa=n.length,qt=n.reduce((o,r)=>{Gt||(Gt={});const a=Gt[r];if(a)o[a[0]]=a[1];else{const i=e(r);for(let s=i.length-1;s>=0;s--){const l=i[s],c=l&&l.filename,u=t[r];if(c&&u){o[c]=u,Gt[r]=[c,u];break}}}return o},{})),qt}function uc(e,t){const{fingerprint:n,span:o,breadcrumbs:r,sdkProcessingMetadata:a}=t;pc(e,t),o&&hc(e,o),gc(e,n),fc(e,r),mc(e,a)}function mn(e,t){const{extra:n,tags:o,user:r,contexts:a,level:i,sdkProcessingMetadata:s,breadcrumbs:l,fingerprint:c,eventProcessors:u,attachments:p,propagationContext:f,transactionName:m,span:v}=t;Wt(e,"extra",n),Wt(e,"tags",o),Wt(e,"user",r),Wt(e,"contexts",a),e.sdkProcessingMetadata=Yn(e.sdkProcessingMetadata,s,2),i&&(e.level=i),m&&(e.transactionName=m),v&&(e.span=v),l.length&&(e.breadcrumbs=[...e.breadcrumbs,...l]),c.length&&(e.fingerprint=[...e.fingerprint,...c]),u.length&&(e.eventProcessors=[...e.eventProcessors,...u]),p.length&&(e.attachments=[...e.attachments,...p]),e.propagationContext={...e.propagationContext,...f}}function Wt(e,t,n){e[t]=Yn(e[t],n,1)}function pc(e,t){const{extra:n,tags:o,user:r,contexts:a,level:i,transactionName:s}=t,l=re(n);l&&Object.keys(l).length&&(e.extra={...l,...e.extra});const c=re(o);c&&Object.keys(c).length&&(e.tags={...c,...e.tags});const u=re(r);u&&Object.keys(u).length&&(e.user={...u,...e.user});const p=re(a);p&&Object.keys(p).length&&(e.contexts={...p,...e.contexts}),i&&(e.level=i),s&&e.type!=="transaction"&&(e.transaction=s)}function fc(e,t){const n=[...e.breadcrumbs||[],...t];e.breadcrumbs=n.length?n:void 0}function mc(e,t){e.sdkProcessingMetadata={...e.sdkProcessingMetadata,...t}}function hc(e,t){e.contexts={trace:Fl(t),...e.contexts},e.sdkProcessingMetadata={dynamicSamplingContext:zl(t),...e.sdkProcessingMetadata};const n=Ti(t),o=wr(n).description;o&&!e.transaction&&e.type==="transaction"&&(e.transaction=o)}function gc(e,t){e.fingerprint=e.fingerprint?Array.isArray(e.fingerprint)?e.fingerprint:[e.fingerprint]:[],t&&(e.fingerprint=e.fingerprint.concat(t)),e.fingerprint&&!e.fingerprint.length&&delete e.fingerprint}function yc(e,t,n,o,r,a){const{normalizeDepth:i=3,normalizeMaxBreadth:s=1e3}=e,l={...t,event_id:t.event_id||n.event_id||ae(),timestamp:t.timestamp||Ut()},c=n.integrations||e.integrations.map(b=>b.name);vc(l,e),Sc(l,c),r&&r.emit("applyFrameMetadata",t),t.type===void 0&&wc(l,e.stackParser);const u=xc(o,n.captureContext);n.mechanism&&gt(l,n.mechanism);const p=r?r.getEventProcessors():[],f=vr().getScopeData();if(a){const b=a.getScopeData();mn(f,b)}if(u){const b=u.getScopeData();mn(f,b)}const m=[...n.attachments||[],...f.attachments];m.length&&(n.attachments=m),uc(l,f);const v=[...p,...f.eventProcessors];return Ro(v,l,n).then(b=>(b&&bc(b),typeof i=="number"&&i>0?Ec(b,i,s):b))}function vc(e,t){const{environment:n,release:o,dist:r,maxValueLength:a=250}=t;e.environment=e.environment||n||br,!e.release&&o&&(e.release=o),!e.dist&&r&&(e.dist=r),e.message&&(e.message=ft(e.message,a));const i=e.exception&&e.exception.values&&e.exception.values[0];i&&i.value&&(i.value=ft(i.value,a));const s=e.request;s&&s.url&&(s.url=ft(s.url,a))}function wc(e,t){const n=dc(t);try{e.exception.values.forEach(o=>{o.stacktrace.frames.forEach(r=>{n&&r.filename&&(r.debug_id=n[r.filename])})})}catch{}}function bc(e){const t={};try{e.exception.values.forEach(o=>{o.stacktrace.frames.forEach(r=>{r.debug_id&&(r.abs_path?t[r.abs_path]=r.debug_id:r.filename&&(t[r.filename]=r.debug_id),delete r.debug_id)})})}catch{}if(Object.keys(t).length===0)return;e.debug_meta=e.debug_meta||{},e.debug_meta.images=e.debug_meta.images||[];const n=e.debug_meta.images;Object.entries(t).forEach(([o,r])=>{n.push({type:"sourcemap",code_file:o,debug_id:r})})}function Sc(e,t){t.length>0&&(e.sdk=e.sdk||{},e.sdk.integrations=[...e.sdk.integrations||[],...t])}function Ec(e,t,n){if(!e)return null;const o={...e,...e.breadcrumbs&&{breadcrumbs:e.breadcrumbs.map(r=>({...r,...r.data&&{data:ye(r.data,t,n)}}))},...e.user&&{user:ye(e.user,t,n)},...e.contexts&&{contexts:ye(e.contexts,t,n)},...e.extra&&{extra:ye(e.extra,t,n)}};return e.contexts&&e.contexts.trace&&o.contexts&&(o.contexts.trace=e.contexts.trace,e.contexts.trace.data&&(o.contexts.trace.data=ye(e.contexts.trace.data,t,n))),e.spans&&(o.spans=e.spans.map(r=>({...r,...r.data&&{data:ye(r.data,t,n)}}))),e.contexts&&e.contexts.flags&&o.contexts&&(o.contexts.flags=ye(e.contexts.flags,3,n)),o}function xc(e,t){if(!t)return e;const n=e?e.clone():new Ze;return n.update(t),n}function hn(e,t){return pe().captureException(e,void 0)}function Ii(e,t){return pe().captureEvent(e,t)}function ma(e){const t=Y(),n=it(),o=pe(),{release:r,environment:a=br}=t&&t.getOptions()||{},{userAgent:i}=I.navigator||{},s=ll({release:r,environment:a,user:o.getUser()||n.getUser(),...i&&{userAgent:i},...e}),l=n.getSession();return l&&l.status==="ok"&&yt(l,{status:"exited"}),Pi(),n.setSession(s),o.setSession(s),s}function Pi(){const e=it(),t=pe(),n=t.getSession()||e.getSession();n&&cl(n),Di(),e.setSession(),t.setSession()}function Di(){const e=it(),t=pe(),n=Y(),o=t.getSession()||e.getSession();o&&n&&n.captureSession(o)}function ha(e=!1){if(e){Pi();return}Di()}const Tc="7";function kc(e){const t=e.protocol?`${e.protocol}:`:"",n=e.port?`:${e.port}`:"";return`${t}//${e.host}${n}${e.path?`/${e.path}`:""}/api/`}function _c(e){return`${kc(e)}${e.projectId}/envelope/`}function Ac(e,t){const n={sentry_version:Tc};return e.publicKey&&(n.sentry_key=e.publicKey),t&&(n.sentry_client=`${t.name}/${t.version}`),new URLSearchParams(n).toString()}function Cc(e,t,n){return t||`${_c(e)}?${Ac(e,n)}`}const ga=[];function Ic(e){const t={};return e.forEach(n=>{const{name:o}=n,r=t[o];r&&!r.isDefaultInstance&&n.isDefaultInstance||(t[o]=n)}),Object.values(t)}function Pc(e){const t=e.defaultIntegrations||[],n=e.integrations;t.forEach(i=>{i.isDefaultInstance=!0});let o;if(Array.isArray(n))o=[...t,...n];else if(typeof n=="function"){const i=n(t);o=Array.isArray(i)?i:[i]}else o=t;const r=Ic(o),a=r.findIndex(i=>i.name==="Debug");if(a>-1){const[i]=r.splice(a,1);r.push(i)}return r}function Dc(e,t){const n={};return t.forEach(o=>{o&&Li(e,o,n)}),n}function ya(e,t){for(const n of t)n&&n.afterAllSetup&&n.afterAllSetup(e)}function Li(e,t,n){if(n[t.name]){F&&S.log(`Integration skipped because it was already installed: ${t.name}`);return}if(n[t.name]=t,ga.indexOf(t.name)===-1&&typeof t.setupOnce=="function"&&(t.setupOnce(),ga.push(t.name)),t.setup&&typeof t.setup=="function"&&t.setup(e),typeof t.preprocessEvent=="function"){const o=t.preprocessEvent.bind(t);e.on("preprocessEvent",(r,a)=>o(r,a,e))}if(typeof t.processEvent=="function"){const o=t.processEvent.bind(t),r=Object.assign((a,i)=>o(a,i,e),{id:t.name});e.addEventProcessor(r)}F&&S.log(`Integration installed: ${t.name}`)}function Lc(e,t,n){const o=[{type:"client_report"},{timestamp:Ut(),discarded_events:e}];return $t(t?{dsn:t}:{},[o])}class ve extends Error{constructor(t,n="warn"){super(t),this.message=t,this.name=new.target.prototype.constructor.name,Object.setPrototypeOf(this,new.target.prototype),this.logLevel=n}}const va="Not capturing exception because it's already been captured.";class Fc{constructor(t){if(this._options=t,this._integrations={},this._numProcessing=0,this._outcomes={},this._hooks={},this._eventProcessors=[],t.dsn?this._dsn=Yl(t.dsn):F&&S.warn("No DSN provided, client will not send events."),this._dsn){const r=Cc(this._dsn,t.tunnel,t._metadata?t._metadata.sdk:void 0);this._transport=t.transport({tunnel:this._options.tunnel,recordDroppedEvent:this.recordDroppedEvent.bind(this),...t.transportOptions,url:r})}const o=["enableTracing","tracesSampleRate","tracesSampler"].find(r=>r in t&&t[r]==null);o&&ot(()=>{console.warn(`[Sentry] Deprecation warning: \`${o}\` is set to undefined, which leads to tracing being enabled. In v9, a value of \`undefined\` will result in tracing being disabled.`)})}captureException(t,n,o){const r=ae();if(na(t))return F&&S.log(va),r;const a={event_id:r,...n};return this._process(this.eventFromException(t,a).then(i=>this._captureEvent(i,a,o))),a.event_id}captureMessage(t,n,o,r){const a={event_id:ae(),...o},i=pr(t)?t:String(t),s=fr(t)?this.eventFromMessage(i,n,a):this.eventFromException(t,a);return this._process(s.then(l=>this._captureEvent(l,a,r))),a.event_id}captureEvent(t,n,o){const r=ae();if(n&&n.originalException&&na(n.originalException))return F&&S.log(va),r;const a={event_id:r,...n},s=(t.sdkProcessingMetadata||{}).capturedSpanScope;return this._process(this._captureEvent(t,a,s||o)),a.event_id}captureSession(t){typeof t.release!="string"?F&&S.warn("Discarded session because of missing or non-string release"):(this.sendSession(t),yt(t,{init:!1}))}getDsn(){return this._dsn}getOptions(){return this._options}getSdkMetadata(){return this._options._metadata}getTransport(){return this._transport}flush(t){const n=this._transport;return n?(this.emit("flush"),this._isClientDoneProcessing(t).then(o=>n.flush(t).then(r=>o&&r))):Je(!0)}close(t){return this.flush(t).then(n=>(this.getOptions().enabled=!1,this.emit("close"),n))}getEventProcessors(){return this._eventProcessors}addEventProcessor(t){this._eventProcessors.push(t)}init(){(this._isEnabled()||this._options.integrations.some(({name:t})=>t.startsWith("Spotlight")))&&this._setupIntegrations()}getIntegrationByName(t){return this._integrations[t]}addIntegration(t){const n=this._integrations[t.name];Li(this,t,this._integrations),n||ya(this,[t])}sendEvent(t,n={}){this.emit("beforeSendEvent",t,n);let o=cc(t,this._dsn,this._options._metadata,this._options.tunnel);for(const a of n.attachments||[])o=tc(o,rc(a));const r=this.sendEnvelope(o);r&&r.then(a=>this.emit("afterSendEvent",t,a),null)}sendSession(t){const n=lc(t,this._dsn,this._options._metadata,this._options.tunnel);this.sendEnvelope(n)}recordDroppedEvent(t,n,o){if(this._options.sendClientReports){const r=typeof o=="number"?o:1,a=`${t}:${n}`;F&&S.log(`Recording outcome: "${a}"${r>1?` (${r} times)`:""}`),this._outcomes[a]=(this._outcomes[a]||0)+r}}on(t,n){const o=this._hooks[t]=this._hooks[t]||[];return o.push(n),()=>{const r=o.indexOf(n);r>-1&&o.splice(r,1)}}emit(t,...n){const o=this._hooks[t];o&&o.forEach(r=>r(...n))}sendEnvelope(t){return this.emit("beforeEnvelope",t),this._isEnabled()&&this._transport?this._transport.send(t).then(null,n=>(F&&S.error("Error while sending envelope:",n),n)):(F&&S.error("Transport disabled"),Je({}))}_setupIntegrations(){const{integrations:t}=this._options;this._integrations=Dc(this,t),ya(this,t)}_updateSessionFromEvent(t,n){let o=n.level==="fatal",r=!1;const a=n.exception&&n.exception.values;if(a){r=!0;for(const l of a){const c=l.mechanism;if(c&&c.handled===!1){o=!0;break}}}const i=t.status==="ok";(i&&t.errors===0||i&&o)&&(yt(t,{...o&&{status:"crashed"},errors:t.errors||Number(r||o)}),this.captureSession(t))}_isClientDoneProcessing(t){return new ce(n=>{let o=0;const r=1,a=setInterval(()=>{this._numProcessing==0?(clearInterval(a),n(!0)):(o+=r,t&&o>=t&&(clearInterval(a),n(!1)))},r)})}_isEnabled(){return this.getOptions().enabled!==!1&&this._transport!==void 0}_prepareEvent(t,n,o=pe(),r=it()){const a=this.getOptions(),i=Object.keys(this._integrations);return!n.integrations&&i.length>0&&(n.integrations=i),this.emit("preprocessEvent",t,n),t.type||r.setLastEventId(t.event_id||n.event_id),yc(a,t,n,o,this,r).then(s=>{if(s===null)return s;s.contexts={trace:wl(o),...s.contexts};const l=Hl(this,o);return s.sdkProcessingMetadata={dynamicSamplingContext:l,...s.sdkProcessingMetadata},s})}_captureEvent(t,n={},o){return this._processEvent(t,n,o).then(r=>r.event_id,r=>{if(F){const a=r;a.logLevel==="log"?S.log(a.message):S.warn(a)}})}_processEvent(t,n,o){const r=this.getOptions(),{sampleRate:a}=r,i=Ri(t),s=Fi(t),l=t.type||"error",c=`before send for type \`${l}\``,u=typeof a>"u"?void 0:Bl(a);if(s&&typeof u=="number"&&Math.random()>u)return this.recordDroppedEvent("sample_rate","error",t),fn(new ve(`Discarding event because it's not included in the random sample (sampling rate = ${a})`,"log"));const p=l==="replay_event"?"replay":l,m=(t.sdkProcessingMetadata||{}).capturedSpanIsolationScope;return this._prepareEvent(t,n,o,m).then(v=>{if(v===null)throw this.recordDroppedEvent("event_processor",p,t),new ve("An event processor returned `null`, will not send event.","log");if(n.data&&n.data.__sentry__===!0)return v;const b=Nc(this,r,v,n);return Rc(b,c)}).then(v=>{if(v===null){if(this.recordDroppedEvent("before_send",p,t),i){const T=1+(t.spans||[]).length;this.recordDroppedEvent("before_send","span",T)}throw new ve(`${c} returned \`null\`, will not send event.`,"log")}const w=o&&o.getSession();if(!i&&w&&this._updateSessionFromEvent(w,v),i){const g=v.sdkProcessingMetadata&&v.sdkProcessingMetadata.spanCountBeforeProcessing||0,T=v.spans?v.spans.length:0,P=g-T;P>0&&this.recordDroppedEvent("before_send","span",P)}const b=v.transaction_info;if(i&&b&&v.transaction!==t.transaction){const g="custom";v.transaction_info={...b,source:g}}return this.sendEvent(v,n),v}).then(null,v=>{throw v instanceof ve?v:(this.captureException(v,{data:{__sentry__:!0},originalException:v}),new ve(`Event processing pipeline threw an error, original event will not be sent. Details have been sent as a new event.
Reason: ${v}`))})}_process(t){this._numProcessing++,t.then(n=>(this._numProcessing--,n),n=>(this._numProcessing--,n))}_clearOutcomes(){const t=this._outcomes;return this._outcomes={},Object.entries(t).map(([n,o])=>{const[r,a]=n.split(":");return{reason:r,category:a,quantity:o}})}_flushOutcomes(){F&&S.log("Flushing outcomes...");const t=this._clearOutcomes();if(t.length===0){F&&S.log("No outcomes to send");return}if(!this._dsn){F&&S.log("No dsn provided, will not send outcomes");return}F&&S.log("Sending outcomes:",t);const n=Lc(t,this._options.tunnel&&Qn(this._dsn));this.sendEnvelope(n)}}function Rc(e,t){const n=`${t} must return \`null\` or a valid event.`;if(qn(e))return e.then(o=>{if(!ht(o)&&o!==null)throw new ve(n);return o},o=>{throw new ve(`${t} rejected with ${o}`)});if(!ht(e)&&e!==null)throw new ve(n);return e}function Nc(e,t,n,o){const{beforeSend:r,beforeSendTransaction:a,beforeSendSpan:i}=t;if(Fi(n)&&r)return r(n,o);if(Ri(n)){if(n.spans&&i){const s=[];for(const l of n.spans){const c=i(l);c?s.push(c):(Ul(),e.recordDroppedEvent("before_send","span"))}n.spans=s}if(a){if(n.spans){const s=n.spans.length;n.sdkProcessingMetadata={...n.sdkProcessingMetadata,spanCountBeforeProcessing:s}}return a(n,o)}}return n}function Fi(e){return e.type===void 0}function Ri(e){return e.type==="transaction"}function Oc(e,t){t.debug===!0&&(F?S.enable():ot(()=>{console.warn("[Sentry] Cannot initialize SDK with `debug` option using a non-debug bundle.")})),pe().update(t.initialScope);const o=new e(t);return Mc(o),o.init(),o}function Mc(e){pe().setClient(e)}function jc(e){const t=[];function n(){return e===void 0||t.length<e}function o(i){return t.splice(t.indexOf(i),1)[0]||Promise.resolve(void 0)}function r(i){if(!n())return fn(new ve("Not adding Promise because buffer limit was reached."));const s=i();return t.indexOf(s)===-1&&t.push(s),s.then(()=>o(s)).then(null,()=>o(s).then(null,()=>{})),s}function a(i){return new ce((s,l)=>{let c=t.length;if(!c)return s(!0);const u=setTimeout(()=>{i&&i>0&&s(!1)},i);t.forEach(p=>{Je(p).then(()=>{--c||(clearTimeout(u),s(!0))},l)})})}return{$:t,add:r,drain:a}}const Uc=60*1e3;function $c(e,t=Date.now()){const n=parseInt(`${e}`,10);if(!isNaN(n))return n*1e3;const o=Date.parse(`${e}`);return isNaN(o)?Uc:o-t}function Vc(e,t){return e[t]||e.all||0}function Hc(e,t,n=Date.now()){return Vc(e,t)>n}function zc(e,{statusCode:t,headers:n},o=Date.now()){const r={...e},a=n&&n["x-sentry-rate-limits"],i=n&&n["retry-after"];if(a)for(const s of a.trim().split(",")){const[l,c,,,u]=s.split(":",5),p=parseInt(l,10),f=(isNaN(p)?60:p)*1e3;if(!c)r.all=o+f;else for(const m of c.split(";"))m==="metric_bucket"?(!u||u.split(";").includes("custom"))&&(r[m]=o+f):r[m]=o+f}else i?r.all=o+$c(i,o):t===429&&(r.all=o+60*1e3);return r}const Bc=64;function Ni(e,t,n=jc(e.bufferSize||Bc)){let o={};const r=i=>n.drain(i);function a(i){const s=[];if(ua(i,(p,f)=>{const m=pa(f);if(Hc(o,m)){const v=wa(p,f);e.recordDroppedEvent("ratelimit_backoff",m,v)}else s.push(p)}),s.length===0)return Je({});const l=$t(i[0],s),c=p=>{ua(l,(f,m)=>{const v=wa(f,m);e.recordDroppedEvent(p,pa(m),v)})},u=()=>t({body:nc(l)}).then(p=>(p.statusCode!==void 0&&(p.statusCode<200||p.statusCode>=300)&&F&&S.warn(`Sentry responded with status code ${p.statusCode} to sent event.`),o=zc(o,p),p),p=>{throw c("network_error"),p});return n.add(u).then(p=>p,p=>{if(p instanceof ve)return F&&S.error("Skipped sending event because buffer is full."),c("queue_overflow"),Je({});throw p})}return{send:a,flush:r}}function wa(e,t){if(!(t!=="event"&&t!=="transaction"))return Array.isArray(e)?e[1]:void 0}function Kc(e,t,n=[t],o="npm"){const r=e._metadata||{};r.sdk||(r.sdk={name:`sentry.javascript.${t}`,packages:n.map(a=>({name:`${o}:@sentry/${a}`,version:Ye})),version:Ye}),e._metadata=r}const Gc=100;function et(e,t){const n=Y(),o=it();if(!n)return;const{beforeBreadcrumb:r=null,maxBreadcrumbs:a=Gc}=n.getOptions();if(a<=0)return;const s={timestamp:Ut(),...e},l=r?ot(()=>r(s,t)):s;l!==null&&(n.emit&&n.emit("beforeAddBreadcrumb",l,t),o.addBreadcrumb(l,a))}let ba;const qc="FunctionToString",Sa=new WeakMap,Wc=()=>({name:qc,setupOnce(){ba=Function.prototype.toString;try{Function.prototype.toString=function(...e){const t=hr(this),n=Sa.has(Y())&&t!==void 0?t:this;return ba.apply(n,e)}}catch{}},setup(e){Sa.set(e,!0)}}),Yc=Wc,Qc=[/^Script error\.?$/,/^Javascript error: Script error\.? on line 0$/,/^ResizeObserver loop completed with undelivered notifications.$/,/^Cannot redefine property: googletag$/,"undefined is not an object (evaluating 'a.L')",`can't redefine non-configurable property "solana"`,"vv().getRestrictions is not a function. (In 'vv().getRestrictions(1,a)', 'vv().getRestrictions' is undefined)","Can't find variable: _AutofillCallbackHandler",/^Non-Error promise rejection captured with value: Object Not Found Matching Id:\d+, MethodName:simulateEvent, ParamCount:\d+$/],Xc="InboundFilters",Jc=(e={})=>({name:Xc,processEvent(t,n,o){const r=o.getOptions(),a=ed(e,r);return td(t,a)?null:t}}),Zc=Jc;function ed(e={},t={}){return{allowUrls:[...e.allowUrls||[],...t.allowUrls||[]],denyUrls:[...e.denyUrls||[],...t.denyUrls||[]],ignoreErrors:[...e.ignoreErrors||[],...t.ignoreErrors||[],...e.disableErrorDefaults?[]:Qc],ignoreTransactions:[...e.ignoreTransactions||[],...t.ignoreTransactions||[]],ignoreInternal:e.ignoreInternal!==void 0?e.ignoreInternal:!0}}function td(e,t){return t.ignoreInternal&&sd(e)?(F&&S.warn(`Event dropped due to being internal Sentry Error.
Event: ${Me(e)}`),!0):nd(e,t.ignoreErrors)?(F&&S.warn(`Event dropped due to being matched by \`ignoreErrors\` option.
Event: ${Me(e)}`),!0):cd(e)?(F&&S.warn(`Event dropped due to not having an error message, error type or stacktrace.
Event: ${Me(e)}`),!0):od(e,t.ignoreTransactions)?(F&&S.warn(`Event dropped due to being matched by \`ignoreTransactions\` option.
Event: ${Me(e)}`),!0):rd(e,t.denyUrls)?(F&&S.warn(`Event dropped due to being matched by \`denyUrls\` option.
Event: ${Me(e)}.
Url: ${gn(e)}`),!0):ad(e,t.allowUrls)?!1:(F&&S.warn(`Event dropped due to not being matched by \`allowUrls\` option.
Event: ${Me(e)}.
Url: ${gn(e)}`),!0)}function nd(e,t){return e.type||!t||!t.length?!1:id(e).some(n=>Wn(n,t))}function od(e,t){if(e.type!=="transaction"||!t||!t.length)return!1;const n=e.transaction;return n?Wn(n,t):!1}function rd(e,t){if(!t||!t.length)return!1;const n=gn(e);return n?Wn(n,t):!1}function ad(e,t){if(!t||!t.length)return!0;const n=gn(e);return n?Wn(n,t):!0}function id(e){const t=[];e.message&&t.push(e.message);let n;try{n=e.exception.values[e.exception.values.length-1]}catch{}return n&&n.value&&(t.push(n.value),n.type&&t.push(`${n.type}: ${n.value}`)),t}function sd(e){try{return e.exception.values[0].type==="SentryError"}catch{}return!1}function ld(e=[]){for(let t=e.length-1;t>=0;t--){const n=e[t];if(n&&n.filename!=="<anonymous>"&&n.filename!=="[native code]")return n.filename||null}return null}function gn(e){try{let t;try{t=e.exception.values[0].stacktrace.frames}catch{}return t?ld(t):null}catch{return F&&S.error(`Cannot extract url for event ${Me(e)}`),null}}function cd(e){return e.type||!e.exception||!e.exception.values||e.exception.values.length===0?!1:!e.message&&!e.exception.values.some(t=>t.stacktrace||t.type&&t.type!=="Error"||t.value)}function dd(e,t,n=250,o,r,a,i){if(!a.exception||!a.exception.values||!i||!Qe(i.originalException,Error))return;const s=a.exception.values.length>0?a.exception.values[a.exception.values.length-1]:void 0;s&&(a.exception.values=ud(No(e,t,r,i.originalException,o,a.exception.values,s,0),n))}function No(e,t,n,o,r,a,i,s){if(a.length>=n+1)return a;let l=[...a];if(Qe(o[r],Error)){Ea(i,s);const c=e(t,o[r]),u=l.length;xa(c,r,u,s),l=No(e,t,n,o[r],r,[c,...l],c,u)}return Array.isArray(o.errors)&&o.errors.forEach((c,u)=>{if(Qe(c,Error)){Ea(i,s);const p=e(t,c),f=l.length;xa(p,`errors[${u}]`,f,s),l=No(e,t,n,c,r,[p,...l],p,f)}}),l}function Ea(e,t){e.mechanism=e.mechanism||{type:"generic",handled:!0},e.mechanism={...e.mechanism,...e.type==="AggregateError"&&{is_exception_group:!0},exception_id:t}}function xa(e,t,n,o){e.mechanism=e.mechanism||{type:"generic",handled:!0},e.mechanism={...e.mechanism,type:"chained",source:t,exception_id:n,parent_id:o}}function ud(e,t){return e.map(n=>(n.value&&(n.value=ft(n.value,t)),n))}function ao(e){if(!e)return{};const t=e.match(/^(([^:/?#]+):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?$/);if(!t)return{};const n=t[6]||"",o=t[8]||"";return{host:t[4],path:t[5],protocol:t[2],search:n,hash:o,relative:t[5]+n+o}}function pd(e){const t="console";rt(t,e),at(t,fd)}function fd(){"console"in I&&Ao.forEach(function(e){e in I.console&&oe(I.console,e,function(t){return pn[e]=t,function(...n){ue("console",{args:n,level:e});const r=pn[e];r&&r.apply(I.console,n)}})})}function md(e){return e==="warn"?"warning":["fatal","error","warning","log","info","debug"].includes(e)?e:"log"}const hd="Dedupe",gd=()=>{let e;return{name:hd,processEvent(t){if(t.type)return t;try{if(vd(t,e))return F&&S.warn("Event dropped due to being a duplicate of previously captured event."),null}catch{}return e=t}}},yd=gd;function vd(e,t){return t?!!(wd(e,t)||bd(e,t)):!1}function wd(e,t){const n=e.message,o=t.message;return!(!n&&!o||n&&!o||!n&&o||n!==o||!Mi(e,t)||!Oi(e,t))}function bd(e,t){const n=Ta(t),o=Ta(e);return!(!n||!o||n.type!==o.type||n.value!==o.value||!Mi(e,t)||!Oi(e,t))}function Oi(e,t){let n=Qr(e),o=Qr(t);if(!n&&!o)return!0;if(n&&!o||!n&&o||(n=n,o=o,o.length!==n.length))return!1;for(let r=0;r<o.length;r++){const a=o[r],i=n[r];if(a.filename!==i.filename||a.lineno!==i.lineno||a.colno!==i.colno||a.function!==i.function)return!1}return!0}function Mi(e,t){let n=e.fingerprint,o=t.fingerprint;if(!n&&!o)return!0;if(n&&!o||!n&&o)return!1;n=n,o=o;try{return n.join("")===o.join("")}catch{return!1}}function Ta(e){return e.exception&&e.exception.values&&e.exception.values[0]}function ji(e){if(e!==void 0)return e>=400&&e<500?"warning":e>=500?"error":void 0}const Oo=I;function Ui(){if(!("fetch"in Oo))return!1;try{return new Headers,new Request("http://www.example.com"),new Response,!0}catch{return!1}}function Mo(e){return e&&/^function\s+\w+\(\)\s+\{\s+\[native code\]\s+\}$/.test(e.toString())}function Sd(){if(typeof EdgeRuntime=="string")return!0;if(!Ui())return!1;if(Mo(Oo.fetch))return!0;let e=!1;const t=Oo.document;if(t&&typeof t.createElement=="function")try{const n=t.createElement("iframe");n.hidden=!0,t.head.appendChild(n),n.contentWindow&&n.contentWindow.fetch&&(e=Mo(n.contentWindow.fetch)),t.head.removeChild(n)}catch(n){nt&&S.warn("Could not create sandbox iframe for pure fetch check, bailing to window.fetch: ",n)}return e}function Ed(e,t){const n="fetch";rt(n,e),at(n,()=>xd(void 0,t))}function xd(e,t=!1){t&&!Sd()||oe(I,"fetch",function(n){return function(...o){const r=new Error,{method:a,url:i}=Td(o),s={args:o,fetchData:{method:a,url:i},startTimestamp:Fe()*1e3,virtualError:r};return ue("fetch",{...s}),n.apply(I,o).then(async l=>(ue("fetch",{...s,endTimestamp:Fe()*1e3,response:l}),l),l=>{throw ue("fetch",{...s,endTimestamp:Fe()*1e3,error:l}),ur(l)&&l.stack===void 0&&(l.stack=r.stack,Xe(l,"framesToPop",1)),l})}})}function jo(e,t){return!!e&&typeof e=="object"&&!!e[t]}function ka(e){return typeof e=="string"?e:e?jo(e,"url")?e.url:e.toString?e.toString():"":""}function Td(e){if(e.length===0)return{method:"GET",url:""};if(e.length===2){const[n,o]=e;return{url:ka(n),method:jo(o,"method")?String(o.method).toUpperCase():"GET"}}const t=e[0];return{url:ka(t),method:jo(t,"method")?String(t.method).toUpperCase():"GET"}}function kd(){return"npm"}function _d(e,t=!1){return!(t||e&&!e.startsWith("/")&&!e.match(/^[A-Z]:/)&&!e.startsWith(".")&&!e.match(/^[a-zA-Z]([a-zA-Z0-9.\-+])*:\/\//))&&e!==void 0&&!e.includes("node_modules/")}function Ad(e){const t=/^\s*[-]{4,}$/,n=/at (?:async )?(?:(.+?)\s+\()?(?:(.+):(\d+):(\d+)?|([^)]+))\)?/;return o=>{const r=o.match(n);if(r){let a,i,s,l,c;if(r[1]){s=r[1];let f=s.lastIndexOf(".");if(s[f-1]==="."&&f--,f>0){a=s.slice(0,f),i=s.slice(f+1);const m=a.indexOf(".Module");m>0&&(s=s.slice(m+1),a=a.slice(0,m))}l=void 0}i&&(l=a,c=i),i==="<anonymous>"&&(c=void 0,s=void 0),s===void 0&&(c=c||He,s=l?`${l}.${c}`:c);let u=r[2]&&r[2].startsWith("file://")?r[2].slice(7):r[2];const p=r[5]==="native";return u&&u.match(/\/[A-Z]:/)&&(u=u.slice(1)),!u&&r[5]&&!p&&(u=r[5]),{filename:u?decodeURI(u):void 0,module:void 0,function:s,lineno:_a(r[3]),colno:_a(r[4]),in_app:_d(u||"",p)}}if(o.match(t))return{filename:o}}}function Cd(e){return[90,Ad()]}function _a(e){return parseInt(e||"",10)||void 0}const Yt=I;function Id(){const e=Yt.chrome,t=e&&e.app&&e.app.runtime,n="history"in Yt&&!!Yt.history.pushState&&!!Yt.history.replaceState;return!t&&n}const R=I;let Uo=0;function $i(){return Uo>0}function Pd(){Uo++,setTimeout(()=>{Uo--})}function wt(e,t={}){function n(r){return typeof r=="function"}if(!n(e))return e;try{const r=e.__sentry_wrapped__;if(r)return typeof r=="function"?r:e;if(hr(e))return e}catch{return e}const o=function(...r){try{const a=r.map(i=>wt(i,t));return e.apply(this,a)}catch(a){throw Pd(),vl(i=>{i.addEventProcessor(s=>(t.mechanism&&(Io(s,void 0),gt(s,t.mechanism)),s.extra={...s.extra,arguments:r},s)),hn(a)}),a}};try{for(const r in e)Object.prototype.hasOwnProperty.call(e,r)&&(o[r]=e[r])}catch{}bi(o,e),Xe(e,"__sentry_wrapped__",o);try{Object.getOwnPropertyDescriptor(o,"name").configurable&&Object.defineProperty(o,"name",{get(){return e.name}})}catch{}return o}const Vt=typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__;function Sr(e,t){const n=Er(e,t),o={type:Nd(t),value:Od(t)};return n.length&&(o.stacktrace={frames:n}),o.type===void 0&&o.value===""&&(o.value="Unrecoverable error caught"),o}function Dd(e,t,n,o){const r=Y(),a=r&&r.getOptions().normalizeDepth,i=Vd(t),s={__serialized__:Ai(t,a)};if(i)return{exception:{values:[Sr(e,i)]},extra:s};const l={exception:{values:[{type:Gn(t)?t.constructor.name:o?"UnhandledRejection":"Error",value:Ud(t,{isUnhandledRejection:o})}]},extra:s};if(n){const c=Er(e,n);c.length&&(l.exception.values[0].stacktrace={frames:c})}return l}function io(e,t){return{exception:{values:[Sr(e,t)]}}}function Er(e,t){const n=t.stacktrace||t.stack||"",o=Fd(t),r=Rd(t);try{return e(n,o,r)}catch{}return[]}const Ld=/Minified React error #\d+;/i;function Fd(e){return e&&Ld.test(e.message)?1:0}function Rd(e){return typeof e.framesToPop=="number"?e.framesToPop:0}function Vi(e){return typeof WebAssembly<"u"&&typeof WebAssembly.Exception<"u"?e instanceof WebAssembly.Exception:!1}function Nd(e){const t=e&&e.name;return!t&&Vi(e)?e.message&&Array.isArray(e.message)&&e.message.length==2?e.message[0]:"WebAssembly.Exception":t}function Od(e){const t=e&&e.message;return t?t.error&&typeof t.error.message=="string"?t.error.message:Vi(e)&&Array.isArray(e.message)&&e.message.length==2?e.message[1]:t:"No error message"}function Md(e,t,n,o){const r=n&&n.syntheticException||void 0,a=xr(e,t,r,o);return gt(a),a.level="error",n&&n.event_id&&(a.event_id=n.event_id),Je(a)}function jd(e,t,n="info",o,r){const a=o&&o.syntheticException||void 0,i=$o(e,t,a,r);return i.level=n,o&&o.event_id&&(i.event_id=o.event_id),Je(i)}function xr(e,t,n,o,r){let a;if(yi(t)&&t.error)return io(e,t.error);if(Jr(t)||Ys(t)){const i=t;if("stack"in t)a=io(e,t);else{const s=i.name||(Jr(i)?"DOMError":"DOMException"),l=i.message?`${s}: ${i.message}`:s;a=$o(e,l,n,o),Io(a,l)}return"code"in i&&(a.tags={...a.tags,"DOMException.code":`${i.code}`}),a}return ur(t)?io(e,t):ht(t)||Gn(t)?(a=Dd(e,t,n,r),gt(a,{synthetic:!0}),a):(a=$o(e,t,n,o),Io(a,`${t}`),gt(a,{synthetic:!0}),a)}function $o(e,t,n,o){const r={};if(o&&n){const a=Er(e,n);a.length&&(r.exception={values:[{value:t,stacktrace:{frames:a}}]}),gt(r,{synthetic:!0})}if(pr(t)){const{__sentry_template_string__:a,__sentry_template_values__:i}=t;return r.logentry={message:a,params:i},r}return r.message=t,r}function Ud(e,{isUnhandledRejection:t}){const n=rl(e),o=t?"promise rejection":"exception";return yi(e)?`Event \`ErrorEvent\` captured as ${o} with message \`${e.message}\``:Gn(e)?`Event \`${$d(e)}\` (type=${e.type}) captured as ${o}`:`Object captured as ${o} with keys: ${n}`}function $d(e){try{const t=Object.getPrototypeOf(e);return t?t.constructor.name:void 0}catch{}}function Vd(e){for(const t in e)if(Object.prototype.hasOwnProperty.call(e,t)){const n=e[t];if(n instanceof Error)return n}}function Hd(e,{metadata:t,tunnel:n,dsn:o}){const r={event_id:e.event_id,sent_at:new Date().toISOString(),...t&&t.sdk&&{sdk:{name:t.sdk.name,version:t.sdk.version}},...!!n&&!!o&&{dsn:Qn(o)}},a=zd(e);return $t(r,[a])}function zd(e){return[{type:"user_report"},e]}class Bd extends Fc{constructor(t){const n={parentSpanIsAlwaysRootSpan:!0,...t},o=R.SENTRY_SDK_SOURCE||kd();Kc(n,"browser",["browser"],o),super(n),n.sendClientReports&&R.document&&R.document.addEventListener("visibilitychange",()=>{R.document.visibilityState==="hidden"&&this._flushOutcomes()})}eventFromException(t,n){return Md(this._options.stackParser,t,n,this._options.attachStacktrace)}eventFromMessage(t,n="info",o){return jd(this._options.stackParser,t,n,o,this._options.attachStacktrace)}captureUserFeedback(t){if(!this._isEnabled()){Vt&&S.warn("SDK not enabled, will not capture user feedback.");return}const n=Hd(t,{metadata:this.getSdkMetadata(),dsn:this.getDsn(),tunnel:this.getOptions().tunnel});this.sendEnvelope(n)}_prepareEvent(t,n,o){return t.platform=t.platform||"javascript",super._prepareEvent(t,n,o)}}const Kd=typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__,J=I,Gd=1e3;let Aa,Vo,Ho;function qd(e){const t="dom";rt(t,e),at(t,Wd)}function Wd(){if(!J.document)return;const e=ue.bind(null,"dom"),t=Ca(e,!0);J.document.addEventListener("click",t,!1),J.document.addEventListener("keypress",t,!1),["EventTarget","Node"].forEach(n=>{const r=J[n],a=r&&r.prototype;!a||!a.hasOwnProperty||!a.hasOwnProperty("addEventListener")||(oe(a,"addEventListener",function(i){return function(s,l,c){if(s==="click"||s=="keypress")try{const u=this.__sentry_instrumentation_handlers__=this.__sentry_instrumentation_handlers__||{},p=u[s]=u[s]||{refCount:0};if(!p.handler){const f=Ca(e);p.handler=f,i.call(this,s,f,c)}p.refCount++}catch{}return i.call(this,s,l,c)}}),oe(a,"removeEventListener",function(i){return function(s,l,c){if(s==="click"||s=="keypress")try{const u=this.__sentry_instrumentation_handlers__||{},p=u[s];p&&(p.refCount--,p.refCount<=0&&(i.call(this,s,p.handler,c),p.handler=void 0,delete u[s]),Object.keys(u).length===0&&delete this.__sentry_instrumentation_handlers__)}catch{}return i.call(this,s,l,c)}}))})}function Yd(e){if(e.type!==Vo)return!1;try{if(!e.target||e.target._sentryId!==Ho)return!1}catch{}return!0}function Qd(e,t){return e!=="keypress"?!1:!t||!t.tagName?!0:!(t.tagName==="INPUT"||t.tagName==="TEXTAREA"||t.isContentEditable)}function Ca(e,t=!1){return n=>{if(!n||n._sentryCaptured)return;const o=Xd(n);if(Qd(n.type,o))return;Xe(n,"_sentryCaptured",!0),o&&!o._sentryId&&Xe(o,"_sentryId",ae());const r=n.type==="keypress"?"input":n.type;Yd(n)||(e({event:n,name:r,global:t}),Vo=n.type,Ho=o?o._sentryId:void 0),clearTimeout(Aa),Aa=J.setTimeout(()=>{Ho=void 0,Vo=void 0},Gd)}}function Xd(e){try{return e.target}catch{return null}}let Qt;function Hi(e){const t="history";rt(t,e),at(t,Jd)}function Jd(){if(!Id())return;const e=J.onpopstate;J.onpopstate=function(...n){const o=J.location.href,r=Qt;if(Qt=o,ue("history",{from:r,to:o}),e)try{return e.apply(this,n)}catch{}};function t(n){return function(...o){const r=o.length>2?o[2]:void 0;if(r){const a=Qt,i=String(r);Qt=i,ue("history",{from:a,to:i})}return n.apply(this,o)}}oe(J.history,"pushState",t),oe(J.history,"replaceState",t)}const ln={};function Zd(e){const t=ln[e];if(t)return t;let n=J[e];if(Mo(n))return ln[e]=n.bind(J);const o=J.document;if(o&&typeof o.createElement=="function")try{const r=o.createElement("iframe");r.hidden=!0,o.head.appendChild(r);const a=r.contentWindow;a&&a[e]&&(n=a[e]),o.head.removeChild(r)}catch(r){Kd&&S.warn(`Could not create sandbox iframe for ${e} check, bailing to window.${e}: `,r)}return n&&(ln[e]=n.bind(J))}function Ia(e){ln[e]=void 0}const At="__sentry_xhr_v3__";function eu(e){const t="xhr";rt(t,e),at(t,tu)}function tu(){if(!J.XMLHttpRequest)return;const e=XMLHttpRequest.prototype;e.open=new Proxy(e.open,{apply(t,n,o){const r=new Error,a=Fe()*1e3,i=Le(o[0])?o[0].toUpperCase():void 0,s=nu(o[1]);if(!i||!s)return t.apply(n,o);n[At]={method:i,url:s,request_headers:{}},i==="POST"&&s.match(/sentry_key/)&&(n.__sentry_own_request__=!0);const l=()=>{const c=n[At];if(c&&n.readyState===4){try{c.status_code=n.status}catch{}const u={endTimestamp:Fe()*1e3,startTimestamp:a,xhr:n,virtualError:r};ue("xhr",u)}};return"onreadystatechange"in n&&typeof n.onreadystatechange=="function"?n.onreadystatechange=new Proxy(n.onreadystatechange,{apply(c,u,p){return l(),c.apply(u,p)}}):n.addEventListener("readystatechange",l),n.setRequestHeader=new Proxy(n.setRequestHeader,{apply(c,u,p){const[f,m]=p,v=u[At];return v&&Le(f)&&Le(m)&&(v.request_headers[f.toLowerCase()]=m),c.apply(u,p)}}),t.apply(n,o)}}),e.send=new Proxy(e.send,{apply(t,n,o){const r=n[At];if(!r)return t.apply(n,o);o[0]!==void 0&&(r.body=o[0]);const a={startTimestamp:Fe()*1e3,xhr:n};return ue("xhr",a),t.apply(n,o)}})}function nu(e){if(Le(e))return e;try{return e.toString()}catch{}}function ou(e,t=Zd("fetch")){let n=0,o=0;function r(a){const i=a.body.length;n+=i,o++;const s={body:a.body,method:"POST",referrerPolicy:"origin",headers:e.headers,keepalive:n<=6e4&&o<15,...e.fetchOptions};if(!t)return Ia("fetch"),fn("No fetch implementation available");try{return t(e.url,s).then(l=>(n-=i,o--,{statusCode:l.status,headers:{"x-sentry-rate-limits":l.headers.get("X-Sentry-Rate-Limits"),"retry-after":l.headers.get("Retry-After")}}))}catch(l){return Ia("fetch"),n-=i,o--,fn(l)}}return Ni(e,r)}const ru=30,au=50;function zo(e,t,n,o){const r={filename:e,function:t==="<anonymous>"?He:t,in_app:!0};return n!==void 0&&(r.lineno=n),o!==void 0&&(r.colno=o),r}const iu=/^\s*at (\S+?)(?::(\d+))(?::(\d+))\s*$/i,su=/^\s*at (?:(.+?\)(?: \[.+\])?|.*?) ?\((?:address at )?)?(?:async )?((?:<anonymous>|[-a-z]+:|.*bundle|\/)?.*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,lu=/\((\S*)(?::(\d+))(?::(\d+))\)/,cu=e=>{const t=iu.exec(e);if(t){const[,o,r,a]=t;return zo(o,He,+r,+a)}const n=su.exec(e);if(n){if(n[2]&&n[2].indexOf("eval")===0){const i=lu.exec(n[2]);i&&(n[2]=i[1],n[3]=i[2],n[4]=i[3])}const[r,a]=Bi(n[1]||He,n[2]);return zo(a,r,n[3]?+n[3]:void 0,n[4]?+n[4]:void 0)}},zi=[ru,cu],du=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)?((?:[-a-z]+)?:\/.*?|\[native code\]|[^@]*(?:bundle|\d+\.js)|\/[\w\-. /=]+)(?::(\d+))?(?::(\d+))?\s*$/i,uu=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i,pu=e=>{const t=du.exec(e);if(t){if(t[3]&&t[3].indexOf(" > eval")>-1){const a=uu.exec(t[3]);a&&(t[1]=t[1]||"eval",t[3]=a[1],t[4]=a[2],t[5]="")}let o=t[3],r=t[1]||He;return[r,o]=Bi(r,o),zo(o,r,t[4]?+t[4]:void 0,t[5]?+t[5]:void 0)}},fu=[au,pu],mu=[zi,fu],hu=mi(...mu),Bi=(e,t)=>{const n=e.indexOf("safari-extension")!==-1,o=e.indexOf("safari-web-extension")!==-1;return n||o?[e.indexOf("@")!==-1?e.split("@")[0]:He,n?`safari-extension:${t}`:`safari-web-extension:${t}`]:[e,t]},Xt=1024,gu="Breadcrumbs",yu=(e={})=>{const t={console:!0,dom:!0,fetch:!0,history:!0,sentry:!0,xhr:!0,...e};return{name:gu,setup(n){t.console&&pd(Su(n)),t.dom&&qd(bu(n,t.dom)),t.xhr&&eu(Eu(n)),t.fetch&&Ed(xu(n)),t.history&&Hi(Tu(n)),t.sentry&&n.on("beforeSendEvent",wu(n))}}},vu=yu;function wu(e){return function(n){Y()===e&&et({category:`sentry.${n.type==="transaction"?"transaction":"event"}`,event_id:n.event_id,level:n.level,message:Me(n)},{event:n})}}function bu(e,t){return function(o){if(Y()!==e)return;let r,a,i=typeof t=="object"?t.serializeAttribute:void 0,s=typeof t=="object"&&typeof t.maxStringLength=="number"?t.maxStringLength:void 0;s&&s>Xt&&(Vt&&S.warn(`\`dom.maxStringLength\` cannot exceed ${Xt}, but a value of ${s} was configured. Sentry will use ${Xt} instead.`),s=Xt),typeof i=="string"&&(i=[i]);try{const c=o.event,u=ku(c)?c.target:c;r=wi(u,{keyAttrs:i,maxStringLength:s}),a=nl(u)}catch{r="<unknown>"}if(r.length===0)return;const l={category:`ui.${o.name}`,message:r};a&&(l.data={"ui.component_name":a}),et(l,{event:o.event,name:o.name,global:o.global})}}function Su(e){return function(n){if(Y()!==e)return;const o={category:"console",data:{arguments:n.args,logger:"console"},level:md(n.level),message:Zr(n.args," ")};if(n.level==="assert")if(n.args[0]===!1)o.message=`Assertion failed: ${Zr(n.args.slice(1)," ")||"console.assert"}`,o.data.arguments=n.args.slice(1);else return;et(o,{input:n.args,level:n.level})}}function Eu(e){return function(n){if(Y()!==e)return;const{startTimestamp:o,endTimestamp:r}=n,a=n.xhr[At];if(!o||!r||!a)return;const{method:i,url:s,status_code:l,body:c}=a,u={method:i,url:s,status_code:l},p={xhr:n.xhr,input:c,startTimestamp:o,endTimestamp:r},f=ji(l);et({category:"xhr",data:u,type:"http",level:f},p)}}function xu(e){return function(n){if(Y()!==e)return;const{startTimestamp:o,endTimestamp:r}=n;if(r&&!(n.fetchData.url.match(/sentry_key/)&&n.fetchData.method==="POST"))if(n.error){const a=n.fetchData,i={data:n.error,input:n.args,startTimestamp:o,endTimestamp:r};et({category:"fetch",data:a,level:"error",type:"http"},i)}else{const a=n.response,i={...n.fetchData,status_code:a&&a.status},s={input:n.args,response:a,startTimestamp:o,endTimestamp:r},l=ji(i.status_code);et({category:"fetch",data:i,type:"http",level:l},s)}}}function Tu(e){return function(n){if(Y()!==e)return;let o=n.from,r=n.to;const a=ao(R.location.href);let i=o?ao(o):void 0;const s=ao(r);(!i||!i.path)&&(i=a),a.protocol===s.protocol&&a.host===s.host&&(r=s.relative),a.protocol===i.protocol&&a.host===i.host&&(o=i.relative),et({category:"navigation",data:{from:o,to:r}})}}function ku(e){return!!e&&!!e.target}const _u=["EventTarget","Window","Node","ApplicationCache","AudioTrackList","BroadcastChannel","ChannelMergerNode","CryptoOperation","EventSource","FileReader","HTMLUnknownElement","IDBDatabase","IDBRequest","IDBTransaction","KeyOperation","MediaController","MessagePort","ModalWindow","Notification","SVGElementInstance","Screen","SharedWorker","TextTrack","TextTrackCue","TextTrackList","WebSocket","WebSocketWorker","Worker","XMLHttpRequest","XMLHttpRequestEventTarget","XMLHttpRequestUpload"],Au="BrowserApiErrors",Cu=(e={})=>{const t={XMLHttpRequest:!0,eventTarget:!0,requestAnimationFrame:!0,setInterval:!0,setTimeout:!0,...e};return{name:Au,setupOnce(){t.setTimeout&&oe(R,"setTimeout",Pa),t.setInterval&&oe(R,"setInterval",Pa),t.requestAnimationFrame&&oe(R,"requestAnimationFrame",Pu),t.XMLHttpRequest&&"XMLHttpRequest"in R&&oe(XMLHttpRequest.prototype,"send",Du);const n=t.eventTarget;n&&(Array.isArray(n)?n:_u).forEach(Lu)}}},Iu=Cu;function Pa(e){return function(...t){const n=t[0];return t[0]=wt(n,{mechanism:{data:{function:ze(e)},handled:!1,type:"instrument"}}),e.apply(this,t)}}function Pu(e){return function(t){return e.apply(this,[wt(t,{mechanism:{data:{function:"requestAnimationFrame",handler:ze(e)},handled:!1,type:"instrument"}})])}}function Du(e){return function(...t){const n=this;return["onload","onerror","onprogress","onreadystatechange"].forEach(r=>{r in n&&typeof n[r]=="function"&&oe(n,r,function(a){const i={mechanism:{data:{function:r,handler:ze(a)},handled:!1,type:"instrument"}},s=hr(a);return s&&(i.mechanism.data.handler=ze(s)),wt(a,i)})}),e.apply(this,t)}}function Lu(e){const n=R[e],o=n&&n.prototype;!o||!o.hasOwnProperty||!o.hasOwnProperty("addEventListener")||(oe(o,"addEventListener",function(r){return function(a,i,s){try{Fu(i)&&(i.handleEvent=wt(i.handleEvent,{mechanism:{data:{function:"handleEvent",handler:ze(i),target:e},handled:!1,type:"instrument"}}))}catch{}return r.apply(this,[a,wt(i,{mechanism:{data:{function:"addEventListener",handler:ze(i),target:e},handled:!1,type:"instrument"}}),s])}}),oe(o,"removeEventListener",function(r){return function(a,i,s){try{const l=i.__sentry_wrapped__;l&&r.call(this,a,l,s)}catch{}return r.call(this,a,i,s)}}))}function Fu(e){return typeof e.handleEvent=="function"}const Ru=()=>({name:"BrowserSession",setupOnce(){if(typeof R.document>"u"){Vt&&S.warn("Using the `browserSessionIntegration` in non-browser environments is not supported.");return}ma({ignoreDuration:!0}),ha(),Hi(({from:e,to:t})=>{e!==void 0&&e!==t&&(ma({ignoreDuration:!0}),ha())})}}),Nu="GlobalHandlers",Ou=(e={})=>{const t={onerror:!0,onunhandledrejection:!0,...e};return{name:Nu,setupOnce(){Error.stackTraceLimit=50},setup(n){t.onerror&&(ju(n),Da("onerror")),t.onunhandledrejection&&(Uu(n),Da("onunhandledrejection"))}}},Mu=Ou;function ju(e){Ks(t=>{const{stackParser:n,attachStacktrace:o}=Ki();if(Y()!==e||$i())return;const{msg:r,url:a,line:i,column:s,error:l}=t,c=Hu(xr(n,l||r,void 0,o,!1),a,i,s);c.level="error",Ii(c,{originalException:l,mechanism:{handled:!1,type:"onerror"}})})}function Uu(e){qs(t=>{const{stackParser:n,attachStacktrace:o}=Ki();if(Y()!==e||$i())return;const r=$u(t),a=fr(r)?Vu(r):xr(n,r,void 0,o,!0);a.level="error",Ii(a,{originalException:r,mechanism:{handled:!1,type:"onunhandledrejection"}})})}function $u(e){if(fr(e))return e;try{if("reason"in e)return e.reason;if("detail"in e&&"reason"in e.detail)return e.detail.reason}catch{}return e}function Vu(e){return{exception:{values:[{type:"UnhandledRejection",value:`Non-Error promise rejection captured with value: ${String(e)}`}]}}}function Hu(e,t,n,o){const r=e.exception=e.exception||{},a=r.values=r.values||[],i=a[0]=a[0]||{},s=i.stacktrace=i.stacktrace||{},l=s.frames=s.frames||[],c=o,u=n,p=Le(t)&&t.length>0?t:tl();return l.length===0&&l.push({colno:c,filename:p,function:He,in_app:!0,lineno:u}),e}function Da(e){Vt&&S.log(`Global Handler attached: ${e}`)}function Ki(){const e=Y();return e&&e.getOptions()||{stackParser:()=>[],attachStacktrace:!1}}const zu=()=>({name:"HttpContext",preprocessEvent(e){if(!R.navigator&&!R.location&&!R.document)return;const t=e.request&&e.request.url||R.location&&R.location.href,{referrer:n}=R.document||{},{userAgent:o}=R.navigator||{},r={...e.request&&e.request.headers,...n&&{Referer:n},...o&&{"User-Agent":o}},a={...e.request,...t&&{url:t},headers:r};e.request=a}}),Bu="cause",Ku=5,Gu="LinkedErrors",qu=(e={})=>{const t=e.limit||Ku,n=e.key||Bu;return{name:Gu,preprocessEvent(o,r,a){const i=a.getOptions();dd(Sr,i.stackParser,i.maxValueLength,n,t,o,r)}}},Wu=qu;function Gi(e){const t=[Zc(),Yc(),Iu(),vu(),Mu(),Wu(),yd(),zu()];return e.autoSessionTracking!==!1&&t.push(Ru()),t}function Yu(e={}){const t={defaultIntegrations:Gi(e),release:typeof __SENTRY_RELEASE__=="string"?__SENTRY_RELEASE__:R.SENTRY_RELEASE&&R.SENTRY_RELEASE.id?R.SENTRY_RELEASE.id:void 0,autoSessionTracking:!0,sendClientReports:!0};return e.defaultIntegrations==null&&delete e.defaultIntegrations,{...t,...e}}function Qu(){const e=typeof R.window<"u"&&R;if(!e)return!1;const t=e.chrome?"chrome":"browser",n=e[t],o=n&&n.runtime&&n.runtime.id,r=R.location&&R.location.href||"",a=["chrome-extension:","moz-extension:","ms-browser-extension:","safari-web-extension:"],i=!!o&&R===R.top&&a.some(l=>r.startsWith(`${l}//`)),s=typeof e.nw<"u";return!!o&&!i&&!s}function Xu(e={}){const t=Yu(e);if(!t.skipBrowserExtensionCheck&&Qu()){ot(()=>{console.error("[Sentry] You cannot run Sentry this way in a browser extension, check: https://docs.sentry.io/platforms/javascript/best-practices/browser-extensions/")});return}Vt&&(Ui()||S.warn("No Fetch API detected. The Sentry SDK requires a Fetch API compatible environment to send events. Please add a Fetch API polyfill."));const n={...t,stackParser:Bs(t.stackParser||hu),integrations:Pc(t),transport:t.transport||ou};return Oc(Bd,n)}function so(){const e=vr().getScopeData(),t=it().getScopeData(),n=pe().getScopeData();return mn(e,t),mn(e,n),e.eventProcessors=[],e}function Ju(e){it().addScopeListener(t=>{const n=so();e(n,t)}),pe().addScopeListener(t=>{const n=so();e(n,t)}),vr().addScopeListener(t=>{const n=so();e(n,t)})}var La;(function(e){e[e.Classic=1]="Classic",e[e.Protocol=2]="Protocol",e[e.Both=3]="Both"})(La||(La={}));const Zu="sentry-ipc";var je;(function(e){e.RENDERER_START="sentry-electron.renderer-start",e.EVENT="sentry-electron.event",e.SCOPE="sentry-electron.scope",e.ENVELOPE="sentry-electron.envelope",e.STATUS="sentry-electron.status",e.ADD_METRIC="sentry-electron.add-metric"})(je||(je={}));const ep="sentry-electron-renderer-id";function dt(e){return`${Zu}://${e}/sentry_key`}function tp(){if(window.__SENTRY_IPC__)return window.__SENTRY_IPC__;{S.log("IPC was not configured in preload script, falling back to custom protocol and fetch");const e=window.__SENTRY_RENDERER_ID__=ae(),t={[ep]:e};return{sendRendererStart:()=>{fetch(dt(je.RENDERER_START),{method:"POST",body:"",headers:t}).catch(()=>{console.error(`Sentry SDK failed to establish connection with the Electron main process.
  - Ensure you have initialized the SDK in the main process
  - If your renderers use custom sessions, be sure to set 'getSessions' in the main process options
  - If you are bundling your main process code and using Electron < v5, you'll need to manually configure a preload script`)})},sendScope:n=>{fetch(dt(je.SCOPE),{method:"POST",body:n,headers:t}).catch(()=>{})},sendEvent:n=>{fetch(dt(je.EVENT),{method:"POST",body:n,headers:t}).catch(()=>{})},sendEnvelope:n=>{fetch(dt(je.ENVELOPE),{method:"POST",body:n,headers:t}).catch(()=>{})},sendStatus:n=>{fetch(dt(je.STATUS),{method:"POST",body:JSON.stringify({status:n}),headers:t}).catch(()=>{})},sendAddMetric:n=>{fetch(dt(je.ADD_METRIC),{method:"POST",body:JSON.stringify(n),headers:t}).catch(()=>{})}}}}let Jt;function Tr(){return Jt||(Jt=tp(),Jt.sendRendererStart()),Jt}const np=()=>({name:"ScopeToMain",setup(){const e=Tr();Ju((t,n)=>{e.sendScope(JSON.stringify(ye(t,20,2e3))),n.clearBreadcrumbs(),n.clearAttachments()})}});function op(e){const t=Tr();return Ni(e,async n=>(t.sendEnvelope(n.body),{statusCode:200}))}function rp(e){const t={pollInterval:1e3,anrThreshold:5e3,captureStackTrace:!1,...e},n=Tr();document.addEventListener("visibilitychange",()=>{n.sendStatus({status:document.visibilityState,config:t})}),n.sendStatus({status:document.visibilityState,config:t}),setInterval(()=>{n.sendStatus({status:"alive",config:t})},t.pollInterval)}const ap=50,[,ip]=zi,[,sp]=Cd(),lp=(e,t=0)=>{const n=[];for(const o of e.split(`
`).slice(t)){const r=ip(o),a=sp(o);if(r&&(a==null?void 0:a.in_app)!==!1?n.push(r):a&&n.push(re(a)),n.length>=ap)break}return hi(n)};function cp(e){return[...Gi(e),np()]}function dp(e={},t=Xu){if(window!=null&&window.__SENTRY__RENDERER_INIT__){S.warn(`The browser SDK has already been initialized.
If init has been called in the preload and contextIsolation is disabled, is not required to call init in the renderer`);return}window.__SENTRY__RENDERER_INIT__=!0,e.autoSessionTracking===void 0&&(e.autoSessionTracking=!1),e.sendClientReports=!1,e.defaultIntegrations===void 0&&(e.defaultIntegrations=cp(e)),e.stackParser===void 0&&(e.stackParser=lp),e.dsn===void 0&&(e.dsn="https://<EMAIL>/12345"),e.transport===void 0&&(e.transport=op),e.anrDetection&&rp(e.anrDetection===!0?{}:e.anrDetection),delete e.initialScope,t(e)}var Zt={},Fa;function up(){if(Fa)return Zt;Fa=1;var e=Is();return Zt.createRoot=e.createRoot,Zt.hydrateRoot=e.hydrateRoot,Zt}var qi=up();const pp=Ps(qi);function fp(e){return H({attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"},child:[]},{tag:"circle",attr:{cx:"12",cy:"12",r:"3"},child:[]}]})(e)}const mp="#3F83F8",hp="#6B7280",gp=40;function yp(e){const{icon:t,iconSize:n,active:o,text:r,onClick:a}=e,i=n||gp,s=o?mp:hp,l=h.useMemo(()=>h.cloneElement(t,{className:"fill-[var(--icon-color)] stroke-[var(--icon-color)]",width:`${i}px`,height:`${i}px`,style:{fontSize:i}}),[t,i]);return d.jsxs("section",{className:"flex flex-col items-center justify-center cursor-pointer p-4 gap-1",onClick:a,style:{"--icon-color":s},children:[l,d.jsx("span",{className:"text-center text-[var(--icon-color)]",children:r})]})}function xt(e){return d.jsx("span",{className:`after:content-["*"] after:text-[#E02424] after:ml-1 ${e.required?"":"after:hidden"}`,children:e.children})}function Ra(e){return d.jsx(dn,{content:e.content,children:d.jsx("div",{className:"p-1 hover:bg-[#f3f4f6] rounded cursor-pointer text-xl text-gray-500",children:e.children})})}function vp(e){return d.jsx("div",{"aria-hidden":!0,className:`fixed flex items-center justify-center top-0 left-0 right-0 bottom-0 bg-black/50 bg-opacity-50 ${e.invisible?"bg-transparent":""}`,onClick:e.onClick,children:e.children})}const Na=e=>typeof e=="function"?e():e,lo=(e,t)=>{typeof e=="function"?e(t):e&&(e.current=t)},wp=h.forwardRef(function(t,n){const{children:o,container:r,disablePortal:a=!1}=t,[i,s]=h.useState(Na(null)),l=h.useMemo(()=>{const u=[h.isValidElement(o)?o==null?void 0:o.ref:null,n];return u.every(p=>p==null)?null:p=>u.forEach(f=>lo(f,p))},[o,n]);if(h.useEffect(()=>{a||s(Na(r)??document.body)},[r,a]),h.useEffect(()=>{if(i&&!a)return lo(n,i),()=>{lo(n,null)}},[n,i,a]),a){if(h.isValidElement(o)){const c={ref:l};return h.cloneElement(o,c)}return o}return i&&Ds.createPortal(o,i)});function bp({container:e,children:t,open:n,zIndex:o,onClose:r}){const a=h.useRef(null),[i,s]=h.useState(!1);return h.useEffect(()=>{if(n)s(!0);else{const l=setTimeout(()=>{s(!1)},300);return()=>clearTimeout(l)}},[n]),!n&&!i?null:d.jsx(wp,{ref:a,container:e,children:d.jsx("div",{className:`fixed right-0 left-0 top-0 bottom-0 transition-opacity duration-300 ${n?"opacity-100":"opacity-0"} ${o?`z-${o}`:"z-1000"}`,children:d.jsx(vp,{invisible:!n,onClick:r,children:d.jsx("div",{onClick:l=>l.stopPropagation(),className:`
              rounded-lg 
              overflow-hidden 
              absolute 
              left-1/2 
              top-1/2 
              bg-white 
              transform 
              -translate-x-1/2 
              -translate-y-1/2
              transition-all
              duration-300
              ${n?"scale-100 opacity-100":"scale-95 opacity-0"}
            `,children:h.cloneElement(t,{onClose:r})})})})})}function Sp(e){return H({attr:{fill:"currentColor",viewBox:"0 0 16 16"},child:[{tag:"path",attr:{d:"M13.545 2.907a13.2 13.2 0 0 0-3.257-1.011.05.05 0 0 0-.052.025c-.141.25-.297.577-.406.833a12.2 12.2 0 0 0-3.658 0 8 8 0 0 0-.412-.833.05.05 0 0 0-.052-.025c-1.125.194-2.22.534-3.257 1.011a.04.04 0 0 0-.021.018C.356 6.024-.213 9.047.066 12.032q.003.022.021.037a13.3 13.3 0 0 0 3.995 2.02.05.05 0 0 0 .056-.019q.463-.63.818-1.329a.05.05 0 0 0-.01-.059l-.018-.011a9 9 0 0 1-1.248-.595.05.05 0 0 1-.02-.066l.015-.019q.127-.095.248-.195a.05.05 0 0 1 .051-.007c2.619 1.196 5.454 1.196 8.041 0a.05.05 0 0 1 .053.007q.121.1.248.195a.05.05 0 0 1-.004.085 8 8 0 0 1-1.249.594.05.05 0 0 0-.03.03.05.05 0 0 0 .003.041c.24.465.515.909.817 1.329a.05.05 0 0 0 .056.019 13.2 13.2 0 0 0 4.001-2.02.05.05 0 0 0 .021-.037c.334-3.451-.559-6.449-2.366-9.106a.03.03 0 0 0-.02-.019m-8.198 7.307c-.789 0-1.438-.724-1.438-1.612s.637-1.613 1.438-1.613c.807 0 1.45.73 1.438 1.613 0 .888-.637 1.612-1.438 1.612m5.316 0c-.788 0-1.438-.724-1.438-1.612s.637-1.613 1.438-1.613c.807 0 1.451.73 1.438 1.613 0 .888-.631 1.612-1.438 1.612"},child:[]}]})(e)}function Ep(e){return H({attr:{viewBox:"0 0 24 24",fill:"currentColor"},child:[{tag:"path",attr:{d:"M2.14753 11.8099C7.3949 9.52374 10.894 8.01654 12.6447 7.28833C17.6435 5.20916 18.6822 4.84799 19.3592 4.83606C19.5081 4.83344 19.8411 4.87034 20.0567 5.04534C20.2388 5.1931 20.2889 5.39271 20.3129 5.5328C20.3369 5.6729 20.3667 5.99204 20.343 6.2414C20.0721 9.08763 18.9 15.9947 18.3037 19.1825C18.0514 20.5314 17.5546 20.9836 17.0736 21.0279C16.0283 21.1241 15.2345 20.3371 14.2221 19.6735C12.6379 18.635 11.7429 17.9885 10.2051 16.9751C8.42795 15.804 9.58001 15.1603 10.5928 14.1084C10.8579 13.8331 15.4635 9.64397 15.5526 9.26395C15.5637 9.21642 15.5741 9.03926 15.4688 8.94571C15.3636 8.85216 15.2083 8.88415 15.0962 8.9096C14.9373 8.94566 12.4064 10.6184 7.50365 13.928C6.78528 14.4212 6.13461 14.6616 5.55163 14.649C4.90893 14.6351 3.67265 14.2856 2.7536 13.9869C1.62635 13.6204 0.730432 13.4267 0.808447 12.8044C0.849081 12.4803 1.29544 12.1488 2.14753 11.8099Z"},child:[]}]})(e)}function xp(e){return H({attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M4 8h16"},child:[]},{tag:"path",attr:{d:"M4 4m0 2a2 2 0 0 1 2 -2h12a2 2 0 0 1 2 2v12a2 2 0 0 1 -2 2h-12a2 2 0 0 1 -2 -2z"},child:[]},{tag:"path",attr:{d:"M8 4v4"},child:[]}]})(e)}const Tp=[{key:"mainMenu.joinTelegramGroup",icon:Ep,link:"https://t.me/+qXNEvnCNl-4xZTQ1"},{key:"mainMenu.joinDiscordCommunity",icon:Sp,link:"https://discord.gg/RVNthUVfqD"},{key:"menu.website",icon:xp,link:"https://snapany.com"}];function kp({children:e,icon:t,to:n}){const o=Ls(),r=Fs(n),a=Rs({path:r.pathname,end:!0});return d.jsx(yp,{text:e,icon:t,active:!!a,onClick:()=>o(n)})}function _p(e){return H({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"},child:[]}]})(e)}function Ap(e){return H({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M13.477 14.89A6 6 0 015.11 6.524l8.367 8.368zm1.414-1.414L6.524 5.11a6 6 0 018.367 8.367zM18 10a8 8 0 11-16 0 8 8 0 0116 0z",clipRule:"evenodd"},child:[]}]})(e)}function Cp(e){return H({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"},child:[]}]})(e)}function Ip(e){return H({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M2 6a2 2 0 012-2h5l2 2h5a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z"},child:[]}]})(e)}function Pp(e){return H({attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"},child:[]}]})(e)}function Dp(e){return H({attr:{fill:"none",viewBox:"0 0 24 24",strokeWidth:"2",stroke:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{strokeLinecap:"round",strokeLinejoin:"round",d:"M5 19a2 2 0 01-2-2V7a2 2 0 012-2h4l2 2h4a2 2 0 012 2v1M5 19h14a2 2 0 002-2v-5a2 2 0 00-2-2H9a2 2 0 00-2 2v5a2 2 0 01-2 2z"},child:[]}]})(e)}function Lp(e){return H({attr:{fill:"none",viewBox:"0 0 24 24",strokeWidth:"2",stroke:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{strokeLinecap:"round",strokeLinejoin:"round",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"},child:[]}]})(e)}function Fp(e){return H({attr:{fill:"none",viewBox:"0 0 24 24",strokeWidth:"2",stroke:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{strokeLinecap:"round",strokeLinejoin:"round",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"},child:[]}]})(e)}const Rp=(e,t)=>{const n=e.split(".").map(Number),o=t.split(".").map(Number);for(let r=0;r<Math.max(n.length,o.length);r++){const a=n[r]||0,i=o[r]||0;if(a>i)return 1;if(a<i)return-1}return 0};function Np(e,t,n,o){function r(a){return a instanceof n?a:new n(function(i){i(a)})}return new(n||(n=Promise))(function(a,i){function s(u){try{c(o.next(u))}catch(p){i(p)}}function l(u){try{c(o.throw(u))}catch(p){i(p)}}function c(u){u.done?a(u.value):r(u.value).then(s,l)}c((o=o.apply(e,t||[])).next())})}function Op(e,t){var n={label:0,sent:function(){if(a[0]&1)throw a[1];return a[1]},trys:[],ops:[]},o,r,a,i=Object.create((typeof Iterator=="function"?Iterator:Object).prototype);return i.next=s(0),i.throw=s(1),i.return=s(2),typeof Symbol=="function"&&(i[Symbol.iterator]=function(){return this}),i;function s(c){return function(u){return l([c,u])}}function l(c){if(o)throw new TypeError("Generator is already executing.");for(;i&&(i=0,c[0]&&(n=0)),n;)try{if(o=1,r&&(a=c[0]&2?r.return:c[0]?r.throw||((a=r.return)&&a.call(r),0):r.next)&&!(a=a.call(r,c[1])).done)return a;switch(r=0,a&&(c=[c[0]&2,a.value]),c[0]){case 0:case 1:a=c;break;case 4:return n.label++,{value:c[1],done:!1};case 5:n.label++,r=c[1],c=[0];continue;case 7:c=n.ops.pop(),n.trys.pop();continue;default:if(a=n.trys,!(a=a.length>0&&a[a.length-1])&&(c[0]===6||c[0]===2)){n=0;continue}if(c[0]===3&&(!a||c[1]>a[0]&&c[1]<a[3])){n.label=c[1];break}if(c[0]===6&&n.label<a[1]){n.label=a[1],a=c;break}if(a&&n.label<a[2]){n.label=a[2],n.ops.push(c);break}a[2]&&n.ops.pop(),n.trys.pop();continue}c=t.call(e,n)}catch(u){c=[6,u],r=0}finally{o=a=0}if(c[0]&5)throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}}function Mp(e,t){var n=typeof Symbol=="function"&&e[Symbol.iterator];if(!n)return e;var o=n.call(e),r,a=[],i;try{for(;(t===void 0||t-- >0)&&!(r=o.next()).done;)a.push(r.value)}catch(s){i={error:s}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(i)throw i.error}}return a}function jp(e,t,n){if(n||arguments.length===2)for(var o=0,r=t.length,a;o<r;o++)(a||!(o in t))&&(a||(a=Array.prototype.slice.call(t,0,o)),a[o]=t[o]);return e.concat(a||Array.prototype.slice.call(t))}var Up=function(e){h.useEffect(function(){e==null||e()},[])};function pt(e){var t=this,n=h.useRef(!1);return h.useCallback(function(){for(var o=[],r=0;r<arguments.length;r++)o[r]=arguments[r];return Np(t,void 0,void 0,function(){var a,i;return Op(this,function(s){switch(s.label){case 0:if(n.current)return[2];n.current=!0,s.label=1;case 1:return s.trys.push([1,3,4,5]),[4,e.apply(void 0,jp([],Mp(o),!1))];case 2:return a=s.sent(),[2,a];case 3:throw i=s.sent(),i;case 4:return n.current=!1,[7];case 5:return[2]}})})},[e])}const Oa=300;function $p(e){const{message:t,type:n,onClose:o}=e,[r,a]=h.useState(!1),i=h.useMemo(()=>n==="error"?d.jsx("div",{className:"mr-3 inline-flex h-8 w-8 shrink-0 items-center justify-center rounded-lg bg-red-100 text-red-500 dark:bg-red-800 dark:text-red-200",children:d.jsx(Ap,{className:"h-5 w-5"})}):n==="success"?d.jsx("div",{className:"mr-3 inline-flex h-8 w-8 shrink-0 items-center justify-center rounded-lg bg-green-100 text-green-500 dark:bg-green-800 dark:text-green-200",children:d.jsx(_p,{className:"h-5 w-5"})}):null,[n]),s=h.useCallback(()=>{a(!1),setTimeout(o,Oa)},[o]);return h.useEffect(()=>{const l=setTimeout(s,e.duration);return()=>clearTimeout(l)},[s,e.duration]),Up(()=>{requestAnimationFrame(()=>{a(!0)})}),d.jsx("div",{className:`mt-4 mr-4 transition-all duration-${Oa} ease-in-out
        ${r?"opacity-100 translate-x-0":"opacity-0 translate-x-full"}`,children:d.jsxs(Kr,{children:[i,d.jsx("div",{className:"text-sm font-normal",children:t}),d.jsx(Kr.Toggle,{onDismiss:s})]})})}let Te=null;const D=e=>{const{type:t,message:n,duration:o}=e;if(!n)return;Te||(Te=document.createElement("div"),Te.setAttribute("id","notice-container"),Te.className="flex flex-col fixed right-0 top-0 z-2001",document.body.appendChild(Te));const r=document.createElement("div");Te.appendChild(r);const a=qi.createRoot(r),i=()=>{a.unmount(),Te&&r.parentNode===Te&&setTimeout(()=>{Te.removeChild(r)},500)};a.render(d.jsx($p,{type:t,message:n,duration:o??1500,onClose:i}))},kr=e=>(t,n)=>{t&&D({type:e,message:t,duration:e==="error"?8e3:n??1500})};D.info=kr("info");D.error=kr("error");D.success=kr("success");var co={exports:{}},uo={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ma;function Vp(){if(Ma)return uo;Ma=1;var e=Ns();function t(p,f){return p===f&&(p!==0||1/p===1/f)||p!==p&&f!==f}var n=typeof Object.is=="function"?Object.is:t,o=e.useState,r=e.useEffect,a=e.useLayoutEffect,i=e.useDebugValue;function s(p,f){var m=f(),v=o({inst:{value:m,getSnapshot:f}}),w=v[0].inst,b=v[1];return a(function(){w.value=m,w.getSnapshot=f,l(w)&&b({inst:w})},[p,m,f]),r(function(){return l(w)&&b({inst:w}),p(function(){l(w)&&b({inst:w})})},[p]),i(m),m}function l(p){var f=p.getSnapshot;p=p.value;try{var m=f();return!n(p,m)}catch{return!0}}function c(p,f){return f()}var u=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?c:s;return uo.useSyncExternalStore=e.useSyncExternalStore!==void 0?e.useSyncExternalStore:u,uo}var ja;function Hp(){return ja||(ja=1,co.exports=Vp()),co.exports}var zp=Hp();const Wi=0,Yi=1,Qi=2,Ua=3;var $a=Object.prototype.hasOwnProperty;function Bo(e,t){var n,o;if(e===t)return!0;if(e&&t&&(n=e.constructor)===t.constructor){if(n===Date)return e.getTime()===t.getTime();if(n===RegExp)return e.toString()===t.toString();if(n===Array){if((o=e.length)===t.length)for(;o--&&Bo(e[o],t[o]););return o===-1}if(!n||typeof e=="object"){o=0;for(n in e)if($a.call(e,n)&&++o&&!$a.call(t,n)||!(n in t)||!Bo(e[n],t[n]))return!1;return Object.keys(t).length===o}}return e!==e&&t!==t}const Ie=new WeakMap,Ve=()=>{},X=Ve(),Ko=Object,A=e=>e===X,we=e=>typeof e=="function",Be=(e,t)=>({...e,...t}),Xi=e=>we(e.then),po={},en={},_r="undefined",Ht=typeof window!=_r,Go=typeof document!=_r,Bp=Ht&&"Deno"in window,Kp=()=>Ht&&typeof window.requestAnimationFrame!=_r,Ji=(e,t)=>{const n=Ie.get(e);return[()=>!A(t)&&e.get(t)||po,o=>{if(!A(t)){const r=e.get(t);t in en||(en[t]=r),n[5](t,Be(r,o),r||po)}},n[6],()=>!A(t)&&t in en?en[t]:!A(t)&&e.get(t)||po]};let qo=!0;const Gp=()=>qo,[Wo,Yo]=Ht&&window.addEventListener?[window.addEventListener.bind(window),window.removeEventListener.bind(window)]:[Ve,Ve],qp=()=>{const e=Go&&document.visibilityState;return A(e)||e!=="hidden"},Wp=e=>(Go&&document.addEventListener("visibilitychange",e),Wo("focus",e),()=>{Go&&document.removeEventListener("visibilitychange",e),Yo("focus",e)}),Yp=e=>{const t=()=>{qo=!0,e()},n=()=>{qo=!1};return Wo("online",t),Wo("offline",n),()=>{Yo("online",t),Yo("offline",n)}},Qp={isOnline:Gp,isVisible:qp},Xp={initFocus:Wp,initReconnect:Yp},Va=!Nt.useId,Ot=!Ht||Bp,Jp=e=>Kp()?window.requestAnimationFrame(e):setTimeout(e,1),fo=Ot?h.useEffect:h.useLayoutEffect,mo=typeof navigator<"u"&&navigator.connection,Ha=!Ot&&mo&&(["slow-2g","2g"].includes(mo.effectiveType)||mo.saveData),tn=new WeakMap,ho=(e,t)=>Ko.prototype.toString.call(e)===`[object ${t}]`;let Zp=0;const Qo=e=>{const t=typeof e,n=ho(e,"Date"),o=ho(e,"RegExp"),r=ho(e,"Object");let a,i;if(Ko(e)===e&&!n&&!o){if(a=tn.get(e),a)return a;if(a=++Zp+"~",tn.set(e,a),Array.isArray(e)){for(a="@",i=0;i<e.length;i++)a+=Qo(e[i])+",";tn.set(e,a)}if(r){a="#";const s=Ko.keys(e).sort();for(;!A(i=s.pop());)A(e[i])||(a+=i+":"+Qo(e[i])+",");tn.set(e,a)}}else a=n?e.toJSON():t=="symbol"?e.toString():t=="string"?JSON.stringify(e):""+e;return a},Ar=e=>{if(we(e))try{e=e()}catch{e=""}const t=e;return e=typeof e=="string"?e:(Array.isArray(e)?e.length:e)?Qo(e):"",[e,t]};let ef=0;const Xo=()=>++ef;async function Zi(...e){const[t,n,o,r]=e,a=Be({populateCache:!0,throwOnError:!0},typeof r=="boolean"?{revalidate:r}:r||{});let i=a.populateCache;const s=a.rollbackOnError;let l=a.optimisticData;const c=f=>typeof s=="function"?s(f):s!==!1,u=a.throwOnError;if(we(n)){const f=n,m=[],v=t.keys();for(const w of v)!/^\$(inf|sub)\$/.test(w)&&f(t.get(w)._k)&&m.push(w);return Promise.all(m.map(p))}return p(n);async function p(f){const[m]=Ar(f);if(!m)return;const[v,w]=Ji(t,m),[b,g,T,P]=Ie.get(t),j=()=>{const Z=b[m];return(we(a.revalidate)?a.revalidate(v().data,f):a.revalidate!==!1)&&(delete T[m],delete P[m],Z&&Z[0])?Z[0](Qi).then(()=>v().data):v().data};if(e.length<3)return j();let E=o,k;const N=Xo();g[m]=[N,0];const _=!A(l),K=v(),U=K.data,ee=K._c,de=A(ee)?U:ee;if(_&&(l=we(l)?l(de,U):l,w({data:l,_c:de})),we(E))try{E=E(de)}catch(Z){k=Z}if(E&&Xi(E))if(E=await E.catch(Z=>{k=Z}),N!==g[m][0]){if(k)throw k;return E}else k&&_&&c(k)&&(i=!0,w({data:de,_c:X}));if(i&&!k)if(we(i)){const Z=i(E,de);w({data:Z,error:X,_c:X})}else w({data:E,error:X,_c:X});if(g[m][1]=Xo(),Promise.resolve(j()).then(()=>{w({_c:X})}),k){if(u)throw k;return}return E}}const za=(e,t)=>{for(const n in e)e[n][0]&&e[n][0](t)},tf=(e,t)=>{if(!Ie.has(e)){const n=Be(Xp,t),o={},r=Zi.bind(X,e);let a=Ve;const i={},s=(u,p)=>{const f=i[u]||[];return i[u]=f,f.push(p),()=>f.splice(f.indexOf(p),1)},l=(u,p,f)=>{e.set(u,p);const m=i[u];if(m)for(const v of m)v(p,f)},c=()=>{if(!Ie.has(e)&&(Ie.set(e,[o,{},{},{},r,l,s]),!Ot)){const u=n.initFocus(setTimeout.bind(X,za.bind(X,o,Wi))),p=n.initReconnect(setTimeout.bind(X,za.bind(X,o,Yi)));a=()=>{u&&u(),p&&p(),Ie.delete(e)}}};return c(),[e,r,c,a]}return[e,Ie.get(e)[4]]},nf=(e,t,n,o,r)=>{const a=n.errorRetryCount,i=r.retryCount,s=~~((Math.random()+.5)*(1<<(i<8?i:8)))*n.errorRetryInterval;!A(a)&&i>a||setTimeout(o,s,r)},of=Bo,[es,rf]=tf(new Map),af=Be({onLoadingSlow:Ve,onSuccess:Ve,onError:Ve,onErrorRetry:nf,onDiscarded:Ve,revalidateOnFocus:!0,revalidateOnReconnect:!0,revalidateIfStale:!0,shouldRetryOnError:!0,errorRetryInterval:Ha?1e4:5e3,focusThrottleInterval:5*1e3,dedupingInterval:2*1e3,loadingTimeout:Ha?5e3:3e3,compare:of,isPaused:()=>!1,cache:es,mutate:rf,fallback:{}},Qp),sf=(e,t)=>{const n=Be(e,t);if(t){const{use:o,fallback:r}=e,{use:a,fallback:i}=t;o&&a&&(n.use=o.concat(a)),r&&i&&(n.fallback=Be(r,i))}return n},lf=h.createContext({}),cf="$inf$",ts=Ht&&window.__SWR_DEVTOOLS_USE__,df=ts?window.__SWR_DEVTOOLS_USE__:[],uf=()=>{ts&&(window.__SWR_DEVTOOLS_REACT__=Nt)},pf=e=>we(e[1])?[e[0],e[1],e[2]||{}]:[e[0],null,(e[1]===null?e[2]:e[1])||{}],ff=()=>Be(af,h.useContext(lf)),mf=e=>(t,n,o)=>e(t,n&&((...a)=>{const[i]=Ar(t),[,,,s]=Ie.get(es);if(i.startsWith(cf))return n(...a);const l=s[i];return A(l)?n(...a):(delete s[i],l)}),o),hf=df.concat(mf),gf=e=>function(...n){const o=ff(),[r,a,i]=pf(n),s=sf(o,i);let l=e;const{use:c}=s,u=(c||[]).concat(hf);for(let p=u.length;p--;)l=u[p](l);return l(r,a||s.fetcher||null,s)},yf=(e,t,n)=>{const o=t[e]||(t[e]=[]);return o.push(n),()=>{const r=o.indexOf(n);r>=0&&(o[r]=o[o.length-1],o.pop())}};uf();const go=Nt.use||(e=>{switch(e.status){case"pending":throw e;case"fulfilled":return e.value;case"rejected":throw e.reason;default:throw e.status="pending",e.then(t=>{e.status="fulfilled",e.value=t},t=>{e.status="rejected",e.reason=t}),e}}),yo={dedupe:!0},vf=(e,t,n)=>{const{cache:o,compare:r,suspense:a,fallbackData:i,revalidateOnMount:s,revalidateIfStale:l,refreshInterval:c,refreshWhenHidden:u,refreshWhenOffline:p,keepPreviousData:f}=n,[m,v,w,b]=Ie.get(o),[g,T]=Ar(e),P=h.useRef(!1),j=h.useRef(!1),E=h.useRef(g),k=h.useRef(t),N=h.useRef(n),_=()=>N.current,K=()=>_().isVisible()&&_().isOnline(),[U,ee,de,Z]=Ji(o,g),fe=h.useRef({}).current,be=A(i)?A(n.fallback)?X:n.fallback[g]:i,Se=(O,M)=>{for(const G in fe){const V=G;if(V==="data"){if(!r(O[V],M[V])&&(!A(O[V])||!r(Bt,M[V])))return!1}else if(M[V]!==O[V])return!1}return!0},Re=h.useMemo(()=>{const O=!g||!t?!1:A(s)?_().isPaused()||a?!1:l!==!1:s,M=Q=>{const Ee=Be(Q);return delete Ee._k,O?{isValidating:!0,isLoading:!0,...Ee}:Ee},G=U(),V=Z(),me=M(G),ct=G===V?me:M(V);let z=me;return[()=>{const Q=M(U());return Se(Q,z)?(z.data=Q.data,z.isLoading=Q.isLoading,z.isValidating=Q.isValidating,z.error=Q.error,z):(z=Q,Q)},()=>ct]},[o,g]),st=zp.useSyncExternalStore(h.useCallback(O=>de(g,(M,G)=>{Se(G,M)||O()}),[o,g]),Re[0],Re[1]),Mr=!P.current,ws=m[g]&&m[g].length>0,lt=st.data,Ge=A(lt)?be&&Xi(be)?go(be):be:lt,zt=st.error,Jn=h.useRef(Ge),Bt=f?A(lt)?A(Jn.current)?Ge:Jn.current:lt:Ge,jr=ws&&!A(zt)?!1:Mr&&!A(s)?s:_().isPaused()?!1:a?A(Ge)?!1:l:A(Ge)||l,Ur=!!(g&&t&&Mr&&jr),bs=A(st.isValidating)?Ur:st.isValidating,Ss=A(st.isLoading)?Ur:st.isLoading,Et=h.useCallback(async O=>{const M=k.current;if(!g||!M||j.current||_().isPaused())return!1;let G,V,me=!0;const ct=O||{},z=!w[g]||!ct.dedupe,Q=()=>Va?!j.current&&g===E.current&&P.current:g===E.current,Ee={isValidating:!1,isLoading:!1},Vr=()=>{ee(Ee)},Hr=()=>{const ie=w[g];ie&&ie[1]===V&&delete w[g]},zr={isValidating:!0};A(U().data)&&(zr.isLoading=!0);try{if(z&&(ee(zr),n.loadingTimeout&&A(U().data)&&setTimeout(()=>{me&&Q()&&_().onLoadingSlow(g,n)},n.loadingTimeout),w[g]=[M(T),Xo()]),[G,V]=w[g],G=await G,z&&setTimeout(Hr,n.dedupingInterval),!w[g]||w[g][1]!==V)return z&&Q()&&_().onDiscarded(g),!1;Ee.error=X;const ie=v[g];if(!A(ie)&&(V<=ie[0]||V<=ie[1]||ie[1]===0))return Vr(),z&&Q()&&_().onDiscarded(g),!1;const xe=U().data;Ee.data=r(xe,G)?xe:G,z&&Q()&&_().onSuccess(G,g,n)}catch(ie){Hr();const xe=_(),{shouldRetryOnError:Zn}=xe;xe.isPaused()||(Ee.error=ie,z&&Q()&&(xe.onError(ie,g,xe),(Zn===!0||we(Zn)&&Zn(ie))&&(!_().revalidateOnFocus||!_().revalidateOnReconnect||K())&&xe.onErrorRetry(ie,g,xe,Es=>{const eo=m[g];eo&&eo[0]&&eo[0](Ua,Es)},{retryCount:(ct.retryCount||0)+1,dedupe:!0})))}return me=!1,Vr(),!0},[g,o]),$r=h.useCallback((...O)=>Zi(o,E.current,...O),[]);if(fo(()=>{k.current=t,N.current=n,A(lt)||(Jn.current=lt)}),fo(()=>{if(!g)return;const O=Et.bind(X,yo);let M=0;const V=yf(g,m,(me,ct={})=>{if(me==Wi){const z=Date.now();_().revalidateOnFocus&&z>M&&K()&&(M=z+_().focusThrottleInterval,O())}else if(me==Yi)_().revalidateOnReconnect&&K()&&O();else{if(me==Qi)return Et();if(me==Ua)return Et(ct)}});return j.current=!1,E.current=g,P.current=!0,ee({_k:T}),jr&&(A(Ge)||Ot?O():Jp(O)),()=>{j.current=!0,V()}},[g]),fo(()=>{let O;function M(){const V=we(c)?c(U().data):c;V&&O!==-1&&(O=setTimeout(G,V))}function G(){!U().error&&(u||_().isVisible())&&(p||_().isOnline())?Et(yo).then(M):M()}return M(),()=>{O&&(clearTimeout(O),O=-1)}},[c,u,p,g]),h.useDebugValue(Bt),a&&A(Ge)&&g){if(!Va&&Ot)throw new Error("Fallback data is required when using Suspense in SSR.");k.current=t,N.current=n,j.current=!1;const O=b[g];if(!A(O)){const M=$r(O);go(M)}if(A(zt)){const M=Et(yo);A(Bt)||(M.status="fulfilled",M.value=!0),go(M)}else throw zt}return{mutate:$r,get data(){return fe.data=!0,Bt},get error(){return fe.error=!0,zt},get isValidating(){return fe.isValidating=!0,bs},get isLoading(){return fe.isLoading=!0,Ss}}},mt=gf(vf),{electronAPI:L,electron:wf}=window,bf=()=>L.checkForUpdates(),Sf=()=>L.getAppVersion(),ns=()=>L.getSettings(),Ba=e=>L.saveSettings(e),Ef=e=>L.openPathLocation(e),xf=()=>L.getSystemLanguage(),Tf=()=>L.selectDirectory(),kf=e=>L.getFileInfo(e),_f=e=>L.openExternal(e),Af=e=>L.changeLanguage(e),Cf=()=>L.getSavedSites(),nn=e=>L.saveSites(e),If=(e,t)=>L.openAuthWindow(e,t),Pf=e=>L.removeAuth(e),Df=(e,t)=>L.fetchImage(e,t),Ka=L.getDownloadVideoInfo;L.saveDownloadTask;const Lf=L.openFileLocation,Ff=L.cancelDownload,Rf=L.clearJsonTasks;L.getDownloadTask;L.resumeDownload;const Nf=L.getDownloadTasks;L.downloadUpdate;L.quitAndInstall;L.checkLocalInstaller;L.onUpdateDownloadProgress;L.removeUpdateProgressListener;const{ipcRenderer:os}=wf,Of=os.on,Mf=os.removeListener,jf=e=>Number(e)>0&&Number(e)<65535,Uf=e=>/^(?:(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}|(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}|xn--[a-zA-Z0-9]+)$/.test(e),$f=(e,t)=>{const{mediaType:n,audioFormat:o,audioTracks:r,bitrate:a,hasCover:i,quality:s,subtitles:l,videoFormat:c,platform:u}=e,p={downloadType:n,thumbnail:i,downloadPlatform:u};return n==="video"?p.downloadTypeVideo={quality:s,format:c,subtitle:l==="none"?[]:l,audioChange:r==="all"?["all"]:r}:p.downloadTypeAudio={quality:a,format:o},{...t,...p}},Vf=e=>{const{downloadType:t,downloadTypeVideo:n,downloadTypeAudio:o,thumbnail:r,downloadPlatform:a}=e,i={};return t&&(i.mediaType=t),r!==void 0&&(i.hasCover=r),a&&(i.platform=a),t==="audio"?(o!=null&&o.format&&(i.audioFormat=o.format),o!=null&&o.quality&&(i.bitrate=o.quality)):(n!=null&&n.audioChange&&(i.audioTracks=n.audioChange),n!=null&&n.format&&(i.videoFormat=n.format),n!=null&&n.quality&&(i.quality=n.quality),n!=null&&n.subtitle&&(i.subtitles=n.subtitle)),i};function Ke(){const{data:e,mutate:t,isLoading:n}=mt("snapanyVersion",Sf),{data:o,mutate:r,isLoading:a}=mt("getSettings",ns),{data:i,mutate:s,isLoading:l}=mt("checkForUpdate",bf),c=pt(async p=>{if(!o)return;const f={...o,...p};await Ba(f),r()}),u=pt(async p=>{if(!o)return;const f=$f(p,o);await Ba(f),r()});return{version:e,mutateVersion:t,settings:o,mutateSettings:r,patchSetting:c,updateInfo:i,mutateUpdateInfo:s,isLoading:n||a||l,updateDownloadConfig:u}}function Hf(){const{t:e}=W(),{version:t,updateInfo:n,mutateUpdateInfo:o,isLoading:r}=Ke(),a=n==null?void 0:n.version,i=h.useCallback((u,p)=>!!u&&!!p&&Rp(p,u)<0,[]),s=h.useMemo(()=>i(a,t),[a,i,t]),l=async()=>{const u=await o();if(!u)return D.error(e("errors.checkVersionFailed"));i(u.version,t)?D.success(e("settings.latestVersionAvailable")):D.success(e("settings.latestVersionNotAvailable"))},c=()=>{D.error(e("errors.notImplemented"))};return d.jsxs("div",{className:"space-y-[22px]",children:[d.jsxs("p",{children:[e("settings.version")," : v",t]}),d.jsxs("section",{className:"flex gap-x-7 items-center",children:[d.jsxs("span",{children:[e("settings.latestVersion")," :"," ",a?`v${a}`:e("application.loading")]}),d.jsx(Pe,{disabled:r,size:"sm",color:"blue",onClick:()=>s?c():l(),children:e(s?"settings.upgrade":"settings.checkVersion")})]}),d.jsxs("p",{children:[e("menu.website")," : https://snapany.com"]})]})}function zf(e){return d.jsx("div",{className:`px-2 py-4 text-lg font-bold text-center cursor-pointer ${e.active?"bg-white text-[#3F83F8]":""}`,onClick:e.onClick,children:e.text})}function Bf(e){return d.jsxs("div",{className:"flex flex-col gap-1.5",children:[d.jsxs("section",{className:"flex gap-1 items-center",children:[e.icon,d.jsx("span",{className:"text-black",children:e.siteName})]}),d.jsx("section",{className:"text-[#4B5563] text-sm",children:e.siteLink})]})}function Kf(e){return d.jsx(zn.Item,{className:"flex justify-between items-center px-4 py-3 border-[1px] border-[#D1D5DB] rounded-[10px]",children:e.children})}function Gf(e){return d.jsxs("div",{className:"flex gap-2.5",children:[e.isAuthorized?d.jsx(Pe,{size:"sm",color:"red",onClick:e.onLogout,children:e.logoutText}):d.jsx(Pe,{color:"blue",size:"sm",onClick:e.onLogin,children:e.loginText}),e.enableDelete&&d.jsx(Pe,{size:"sm",color:"red",onClick:e.onDelete,children:e.deleteText})]})}function qf({id:e,handleOpenFolder:t,path:n="",handleValidPath:o,handleChangeDownloadPath:r,actionText:a,rightIcon:i,placeholder:s,className:l,disableBlurSubmit:c},u){const[p,f]=h.useState(n),m=h.useRef(null),v=h.useRef(null);h.useEffect(()=>{const g=t;if(!i||!g||!v.current)return;const T=v.current.querySelector('[data-testid="right-icon"]');T.classList.remove("pointer-events-none"),T.classList.add("cursor-pointer");const P=j=>{j.stopPropagation(),g()};return T.addEventListener("click",P),()=>{T.removeEventListener("click",P)}},[t,i]),h.useImperativeHandle(u,()=>({setShowPath(g){f(g)},clearShowPath(){f("")}})),h.useEffect(()=>{f(n)},[n]),r??(r=()=>{o(p),f("")});const w=g=>{n!==p&&o(g.target.value)},b=g=>{g.key==="Escape"&&(f(n),setTimeout(()=>{m.current.blur()},0)),g.key==="Enter"&&(c&&(o(p),f("")),m.current.blur())};return d.jsxs("div",{className:`flex ${l}`,ref:v,children:[d.jsx(_t,{ref:m,id:e,className:"w-[50px] grow shrink",theme:{field:{input:{withAddon:{off:"rounded-l-lg"}}}},rightIcon:i,value:p,onChange:g=>f(g.target.value),onKeyDown:b,onBlur:c?void 0:w,placeholder:s}),d.jsx("button",{className:"bg-[#1F2A37] text-white px-5 py-2.5 rounded-r-xl cursor-pointer text-sm",onClick:r,children:a})]})}const rs=h.forwardRef(qf),Wf="aaa1rp3bb0ott3vie4c1le2ogado5udhabi7c0ademy5centure6ountant0s9o1tor4d0s1ult4e0g1ro2tna4f0l1rica5g0akhan5ency5i0g1rbus3force5tel5kdn3l0ibaba4pay4lfinanz6state5y2sace3tom5m0azon4ericanexpress7family11x2fam3ica3sterdam8nalytics7droid5quan4z2o0l2partments8p0le4q0uarelle8r0ab1mco4chi3my2pa2t0e3s0da2ia2sociates9t0hleta5torney7u0ction5di0ble3o3spost5thor3o0s4w0s2x0a2z0ure5ba0by2idu3namex4d1k2r0celona5laycard4s5efoot5gains6seball5ketball8uhaus5yern5b0c1t1va3cg1n2d1e0ats2uty4er2ntley5rlin4st0buy5t2f1g1h0arti5i0ble3d1ke2ng0o3o1z2j1lack0friday9ockbuster8g1omberg7ue3m0s1w2n0pparibas9o0ats3ehringer8fa2m1nd2o0k0ing5sch2tik2on4t1utique6x2r0adesco6idgestone9oadway5ker3ther5ussels7s1t1uild0ers6siness6y1zz3v1w1y1z0h3ca0b1fe2l0l1vinklein9m0era3p2non3petown5ital0one8r0avan4ds2e0er0s4s2sa1e1h1ino4t0ering5holic7ba1n1re3c1d1enter4o1rn3f0a1d2g1h0anel2nel4rity4se2t2eap3intai5ristmas6ome4urch5i0priani6rcle4sco3tadel4i0c2y3k1l0aims4eaning6ick2nic1que6othing5ud3ub0med6m1n1o0ach3des3ffee4llege4ogne5m0mbank4unity6pany2re3uter5sec4ndos3struction8ulting7tact3ractors9oking4l1p2rsica5untry4pon0s4rses6pa2r0edit0card4union9icket5own3s1uise0s6u0isinella9v1w1x1y0mru3ou3z2dad1nce3ta1e1ing3sun4y2clk3ds2e0al0er2s3gree4livery5l1oitte5ta3mocrat6ntal2ist5si0gn4v2hl2iamonds6et2gital5rect0ory7scount3ver5h2y2j1k1m1np2o0cs1tor4g1mains5t1wnload7rive4tv2ubai3nlop4pont4rban5vag2r2z2earth3t2c0o2deka3u0cation8e1g1mail3erck5nergy4gineer0ing9terprises10pson4quipment8r0icsson6ni3s0q1tate5t1u0rovision8s2vents5xchange6pert3osed4ress5traspace10fage2il1rwinds6th3mily4n0s2rm0ers5shion4t3edex3edback6rrari3ero6i0delity5o2lm2nal1nce1ial7re0stone6mdale6sh0ing5t0ness6j1k1lickr3ghts4r2orist4wers5y2m1o0o0d1tball6rd1ex2sale4um3undation8x2r0ee1senius7l1ogans4ntier7tr2ujitsu5n0d2rniture7tbol5yi3ga0l0lery3o1up4me0s3p1rden4y2b0iz3d0n2e0a1nt0ing5orge5f1g0ee3h1i0ft0s3ves2ing5l0ass3e1obal2o4m0ail3bh2o1x2n1odaddy5ld0point6f2o0dyear5g0le4p1t1v2p1q1r0ainger5phics5tis4een3ipe3ocery4up4s1t1u0cci3ge2ide2tars5ru3w1y2hair2mburg5ngout5us3bo2dfc0bank7ealth0care8lp1sinki6re1mes5iphop4samitsu7tachi5v2k0t2m1n1ockey4ldings5iday5medepot5goods5s0ense7nda3rse3spital5t0ing5t0els3mail5use3w2r1sbc3t1u0ghes5yatt3undai7ibm2cbc2e1u2d1e0ee3fm2kano4l1m0amat4db2mo0bilien9n0c1dustries8finiti5o2g1k1stitute6urance4e4t0ernational10uit4vestments10o1piranga7q1r0ish4s0maili5t0anbul7t0au2v3jaguar4va3cb2e0ep2tzt3welry6io2ll2m0p2nj2o0bs1urg4t1y2p0morgan6rs3uegos4niper7kaufen5ddi3e0rryhotels6logistics9properties14fh2g1h1i0a1ds2m1ndle4tchen5wi3m1n1oeln3matsu5sher5p0mg2n2r0d1ed3uokgroup8w1y0oto4z2la0caixa5mborghini8er3ncaster6d0rover6xess5salle5t0ino3robe5w0yer5b1c1ds2ease3clerc5frak4gal2o2xus4gbt3i0dl2fe0insurance9style7ghting6ke2lly3mited4o2ncoln4k2psy3ve1ing5k1lc1p2oan0s3cker3us3l1ndon4tte1o3ve3pl0financial11r1s1t0d0a3u0ndbeck6xe1ury5v1y2ma0drid4if1son4keup4n0agement7go3p1rket0ing3s4riott5shalls7ttel5ba2c0kinsey7d1e0d0ia3et2lbourne7me1orial6n0u2rckmsd7g1h1iami3crosoft7l1ni1t2t0subishi9k1l0b1s2m0a2n1o0bi0le4da2e1i1m1nash3ey2ster5rmon3tgage6scow4to0rcycles9v0ie4p1q1r1s0d2t0n1r2u0seum3ic4v1w1x1y1z2na0b1goya4me2vy3ba2c1e0c1t0bank4flix4work5ustar5w0s2xt0direct7us4f0l2g0o2hk2i0co2ke1on3nja3ssan1y5l1o0kia3rton4w0ruz3tv4p1r0a1w2tt2u1yc2z2obi1server7ffice5kinawa6layan0group9lo3m0ega4ne1g1l0ine5oo2pen3racle3nge4g0anic5igins6saka4tsuka4t2vh3pa0ge2nasonic7ris2s1tners4s1y3y2ccw3e0t2f0izer5g1h0armacy6d1ilips5one2to0graphy6s4ysio5ics1tet2ures6d1n0g1k2oneer5zza4k1l0ace2y0station9umbing5s3m1n0c2ohl2ker3litie5rn2st3r0america6xi3ess3ime3o0d0uctions8f1gressive8mo2perties3y5tection8u0dential9s1t1ub2w0c2y2qa1pon3uebec3st5racing4dio4e0ad1lestate6tor2y4cipes5d0stone5umbrella9hab3ise0n3t2liance6n0t0als5pair3ort3ublican8st0aurant8view0s5xroth6ich0ardli6oh3l1o1p2o0cks3deo3gers4om3s0vp3u0gby3hr2n2w0e2yukyu6sa0arland6fe0ty4kura4le1on3msclub4ung5ndvik0coromant12ofi4p1rl2s1ve2xo3b0i1s2c0b1haeffler7midt4olarships8ol3ule3warz5ience5ot3d1e0arch3t2cure1ity6ek2lect4ner3rvices6ven3w1x0y3fr2g1h0angrila6rp3ell3ia1ksha5oes2p0ping5uji3w3i0lk2na1gles5te3j1k0i0n2y0pe4l0ing4m0art3ile4n0cf3o0ccer3ial4ftbank4ware6hu2lar2utions7ng1y2y2pa0ce3ort2t3r0l2s1t0ada2ples4r1tebank4farm7c0group6ockholm6rage3e3ream4udio2y3yle4u0cks3pplies3y2ort5rf1gery5zuki5v1watch4iss4x1y0dney4stems6z2tab1ipei4lk2obao4rget4tamotors6r2too4x0i3c0i2d0k2eam2ch0nology8l1masek5nnis4va3f1g1h0d1eater2re6iaa2ckets5enda4ps2res2ol4j0maxx4x2k0maxx5l1m0all4n1o0day3kyo3ols3p1ray3shiba5tal3urs3wn2yota3s3r0ade1ing4ining5vel0ers0insurance16ust3v2t1ube2i1nes3shu4v0s2w1z2ua1bank3s2g1k1nicom3versity8o2ol2ps2s1y1z2va0cations7na1guard7c1e0gas3ntures6risign5mögensberater2ung14sicherung10t2g1i0ajes4deo3g1king4llas4n1p1rgin4sa1ion4va1o3laanderen9n1odka3lvo3te1ing3o2yage5u2wales2mart4ter4ng0gou5tch0es6eather0channel12bcam3er2site5d0ding5ibo2r3f1hoswho6ien2ki2lliamhill9n0dows4e1ners6me2olterskluwer11odside6rk0s2ld3w2s1tc1f3xbox3erox4ihuan4n2xx2yz3yachts4hoo3maxun5ndex5e1odobashi7ga2kohama6u0tube6t1un3za0ppos4ra3ero3ip2m1one3uerich6w2",Yf="ελ1υ2бг1ел3дети4ею2католик6ом3мкд2он1сква6онлайн5рг3рус2ф2сайт3рб3укр3қаз3հայ3ישראל5קום3ابوظبي5رامكو5لاردن4بحرين5جزائر5سعودية6عليان5مغرب5مارات5یران5بارت2زار4يتك3ھارت5تونس4سودان3رية5شبكة4عراق2ب2مان4فلسطين6قطر3كاثوليك6وم3مصر2ليسيا5وريتانيا7قع4همراه5پاکستان7ڀارت4कॉम3नेट3भारत0म्3ोत5संगठन5বাংলা5ভারত2ৰত4ਭਾਰਤ4ભારત4ଭାରତ4இந்தியா6லங்கை6சிங்கப்பூர்11భారత్5ಭಾರತ4ഭാരതം5ලංකා4คอม3ไทย3ລາວ3გე2みんな3アマゾン4クラウド4グーグル4コム2ストア3セール3ファッション6ポイント4世界2中信1国1國1文网3亚马逊3企业2佛山2信息2健康2八卦2公司1益2台湾1灣2商城1店1标2嘉里0大酒店5在线2大拿2天主教3娱乐2家電2广东2微博2慈善2我爱你3手机2招聘2政务1府2新加坡2闻2时尚2書籍2机构2淡马锡3游戏2澳門2点看2移动2组织机构4网址1店1站1络2联通2谷歌2购物2通販2集团2電訊盈科4飞利浦3食品2餐厅2香格里拉3港2닷넷1컴2삼성2한국2",bt=(e,t)=>{for(const n in t)e[n]=t[n];return e},Jo="numeric",Zo="ascii",er="alpha",It="asciinumeric",Ct="alphanumeric",tr="domain",as="emoji",Qf="scheme",Xf="slashscheme",vo="whitespace";function Jf(e,t){return e in t||(t[e]=[]),t[e]}function We(e,t,n){t[Jo]&&(t[It]=!0,t[Ct]=!0),t[Zo]&&(t[It]=!0,t[er]=!0),t[It]&&(t[Ct]=!0),t[er]&&(t[Ct]=!0),t[Ct]&&(t[tr]=!0),t[as]&&(t[tr]=!0);for(const o in t){const r=Jf(o,n);r.indexOf(e)<0&&r.push(e)}}function Zf(e,t){const n={};for(const o in t)t[o].indexOf(e)>=0&&(n[o]=!0);return n}function ne(e=null){this.j={},this.jr=[],this.jd=null,this.t=e}ne.groups={};ne.prototype={accepts(){return!!this.t},go(e){const t=this,n=t.j[e];if(n)return n;for(let o=0;o<t.jr.length;o++){const r=t.jr[o][0],a=t.jr[o][1];if(a&&r.test(e))return a}return t.jd},has(e,t=!1){return t?e in this.j:!!this.go(e)},ta(e,t,n,o){for(let r=0;r<e.length;r++)this.tt(e[r],t,n,o)},tr(e,t,n,o){o=o||ne.groups;let r;return t&&t.j?r=t:(r=new ne(t),n&&o&&We(t,n,o)),this.jr.push([e,r]),r},ts(e,t,n,o){let r=this;const a=e.length;if(!a)return r;for(let i=0;i<a-1;i++)r=r.tt(e[i]);return r.tt(e[a-1],t,n,o)},tt(e,t,n,o){o=o||ne.groups;const r=this;if(t&&t.j)return r.j[e]=t,t;const a=t;let i,s=r.go(e);if(s?(i=new ne,bt(i.j,s.j),i.jr.push.apply(i.jr,s.jr),i.jd=s.jd,i.t=s.t):i=new ne,a){if(o)if(i.t&&typeof i.t=="string"){const l=bt(Zf(i.t,o),n);We(a,l,o)}else n&&We(a,n,o);i.t=a}return r.j[e]=i,i}};const x=(e,t,n,o,r)=>e.ta(t,n,o,r),$=(e,t,n,o,r)=>e.tr(t,n,o,r),Ga=(e,t,n,o,r)=>e.ts(t,n,o,r),y=(e,t,n,o,r)=>e.tt(t,n,o,r),Ae="WORD",nr="UWORD",is="ASCIINUMERICAL",ss="ALPHANUMERICAL",Mt="LOCALHOST",or="TLD",rr="UTLD",cn="SCHEME",ut="SLASH_SCHEME",Cr="NUM",ar="WS",Ir="NL",Pt="OPENBRACE",Dt="CLOSEBRACE",yn="OPENBRACKET",vn="CLOSEBRACKET",wn="OPENPAREN",bn="CLOSEPAREN",Sn="OPENANGLEBRACKET",En="CLOSEANGLEBRACKET",xn="FULLWIDTHLEFTPAREN",Tn="FULLWIDTHRIGHTPAREN",kn="LEFTCORNERBRACKET",_n="RIGHTCORNERBRACKET",An="LEFTWHITECORNERBRACKET",Cn="RIGHTWHITECORNERBRACKET",In="FULLWIDTHLESSTHAN",Pn="FULLWIDTHGREATERTHAN",Dn="AMPERSAND",Pr="APOSTROPHE",Ln="ASTERISK",Ue="AT",Fn="BACKSLASH",Rn="BACKTICK",Nn="CARET",$e="COLON",Dr="COMMA",On="DOLLAR",he="DOT",Mn="EQUALS",Lr="EXCLAMATION",le="HYPHEN",Lt="PERCENT",jn="PIPE",Un="PLUS",$n="POUND",Ft="QUERY",Fr="QUOTE",ls="FULLWIDTHMIDDLEDOT",Rr="SEMI",ge="SLASH",Rt="TILDE",Vn="UNDERSCORE",cs="EMOJI",Hn="SYM";var ds=Object.freeze({__proto__:null,WORD:Ae,UWORD:nr,ASCIINUMERICAL:is,ALPHANUMERICAL:ss,LOCALHOST:Mt,TLD:or,UTLD:rr,SCHEME:cn,SLASH_SCHEME:ut,NUM:Cr,WS:ar,NL:Ir,OPENBRACE:Pt,CLOSEBRACE:Dt,OPENBRACKET:yn,CLOSEBRACKET:vn,OPENPAREN:wn,CLOSEPAREN:bn,OPENANGLEBRACKET:Sn,CLOSEANGLEBRACKET:En,FULLWIDTHLEFTPAREN:xn,FULLWIDTHRIGHTPAREN:Tn,LEFTCORNERBRACKET:kn,RIGHTCORNERBRACKET:_n,LEFTWHITECORNERBRACKET:An,RIGHTWHITECORNERBRACKET:Cn,FULLWIDTHLESSTHAN:In,FULLWIDTHGREATERTHAN:Pn,AMPERSAND:Dn,APOSTROPHE:Pr,ASTERISK:Ln,AT:Ue,BACKSLASH:Fn,BACKTICK:Rn,CARET:Nn,COLON:$e,COMMA:Dr,DOLLAR:On,DOT:he,EQUALS:Mn,EXCLAMATION:Lr,HYPHEN:le,PERCENT:Lt,PIPE:jn,PLUS:Un,POUND:$n,QUERY:Ft,QUOTE:Fr,FULLWIDTHMIDDLEDOT:ls,SEMI:Rr,SLASH:ge,TILDE:Rt,UNDERSCORE:Vn,EMOJI:cs,SYM:Hn});const ke=/[a-z]/,Tt=new RegExp("\\p{L}","u"),wo=new RegExp("\\p{Emoji}","u"),_e=/\d/,bo=/\s/,qa="\r",So=`
`,em="️",tm="‍",Eo="￼";let on=null,rn=null;function nm(e=[]){const t={};ne.groups=t;const n=new ne;on==null&&(on=Wa(Wf)),rn==null&&(rn=Wa(Yf)),y(n,"'",Pr),y(n,"{",Pt),y(n,"}",Dt),y(n,"[",yn),y(n,"]",vn),y(n,"(",wn),y(n,")",bn),y(n,"<",Sn),y(n,">",En),y(n,"（",xn),y(n,"）",Tn),y(n,"「",kn),y(n,"」",_n),y(n,"『",An),y(n,"』",Cn),y(n,"＜",In),y(n,"＞",Pn),y(n,"&",Dn),y(n,"*",Ln),y(n,"@",Ue),y(n,"`",Rn),y(n,"^",Nn),y(n,":",$e),y(n,",",Dr),y(n,"$",On),y(n,".",he),y(n,"=",Mn),y(n,"!",Lr),y(n,"-",le),y(n,"%",Lt),y(n,"|",jn),y(n,"+",Un),y(n,"#",$n),y(n,"?",Ft),y(n,'"',Fr),y(n,"/",ge),y(n,";",Rr),y(n,"~",Rt),y(n,"_",Vn),y(n,"\\",Fn),y(n,"・",ls);const o=$(n,_e,Cr,{[Jo]:!0});$(o,_e,o);const r=$(o,ke,is,{[It]:!0}),a=$(o,Tt,ss,{[Ct]:!0}),i=$(n,ke,Ae,{[Zo]:!0});$(i,_e,r),$(i,ke,i),$(r,_e,r),$(r,ke,r);const s=$(n,Tt,nr,{[er]:!0});$(s,ke),$(s,_e,a),$(s,Tt,s),$(a,_e,a),$(a,ke),$(a,Tt,a);const l=y(n,So,Ir,{[vo]:!0}),c=y(n,qa,ar,{[vo]:!0}),u=$(n,bo,ar,{[vo]:!0});y(n,Eo,u),y(c,So,l),y(c,Eo,u),$(c,bo,u),y(u,qa),y(u,So),$(u,bo,u),y(u,Eo,u);const p=$(n,wo,cs,{[as]:!0});y(p,"#"),$(p,wo,p),y(p,em,p);const f=y(p,tm);y(f,"#"),$(f,wo,p);const m=[[ke,i],[_e,r]],v=[[ke,null],[Tt,s],[_e,a]];for(let w=0;w<on.length;w++)Ne(n,on[w],or,Ae,m);for(let w=0;w<rn.length;w++)Ne(n,rn[w],rr,nr,v);We(or,{tld:!0,ascii:!0},t),We(rr,{utld:!0,alpha:!0},t),Ne(n,"file",cn,Ae,m),Ne(n,"mailto",cn,Ae,m),Ne(n,"http",ut,Ae,m),Ne(n,"https",ut,Ae,m),Ne(n,"ftp",ut,Ae,m),Ne(n,"ftps",ut,Ae,m),We(cn,{scheme:!0,ascii:!0},t),We(ut,{slashscheme:!0,ascii:!0},t),e=e.sort((w,b)=>w[0]>b[0]?1:-1);for(let w=0;w<e.length;w++){const b=e[w][0],T=e[w][1]?{[Qf]:!0}:{[Xf]:!0};b.indexOf("-")>=0?T[tr]=!0:ke.test(b)?_e.test(b)?T[It]=!0:T[Zo]=!0:T[Jo]=!0,Ga(n,b,b,T)}return Ga(n,"localhost",Mt,{ascii:!0}),n.jd=new ne(Hn),{start:n,tokens:bt({groups:t},ds)}}function us(e,t){const n=om(t.replace(/[A-Z]/g,s=>s.toLowerCase())),o=n.length,r=[];let a=0,i=0;for(;i<o;){let s=e,l=null,c=0,u=null,p=-1,f=-1;for(;i<o&&(l=s.go(n[i]));)s=l,s.accepts()?(p=0,f=0,u=s):p>=0&&(p+=n[i].length,f++),c+=n[i].length,a+=n[i].length,i++;a-=p,i-=f,c-=p,r.push({t:u.t,v:t.slice(a-c,a),s:a-c,e:a})}return r}function om(e){const t=[],n=e.length;let o=0;for(;o<n;){let r=e.charCodeAt(o),a,i=r<55296||r>56319||o+1===n||(a=e.charCodeAt(o+1))<56320||a>57343?e[o]:e.slice(o,o+2);t.push(i),o+=i.length}return t}function Ne(e,t,n,o,r){let a;const i=t.length;for(let s=0;s<i-1;s++){const l=t[s];e.j[l]?a=e.j[l]:(a=new ne(o),a.jr=r.slice(),e.j[l]=a),e=a}return a=new ne(n),a.jr=r.slice(),e.j[t[i-1]]=a,a}function Wa(e){const t=[],n=[];let o=0,r="0123456789";for(;o<e.length;){let a=0;for(;r.indexOf(e[o+a])>=0;)a++;if(a>0){t.push(n.join(""));for(let i=parseInt(e.substring(o,o+a),10);i>0;i--)n.pop();o+=a}else n.push(e[o]),o++}return t}const jt={defaultProtocol:"http",events:null,format:Ya,formatHref:Ya,nl2br:!1,tagName:"a",target:null,rel:null,validate:!0,truncate:1/0,className:null,attributes:null,ignoreTags:[],render:null};function Nr(e,t=null){let n=bt({},jt);e&&(n=bt(n,e instanceof Nr?e.o:e));const o=n.ignoreTags,r=[];for(let a=0;a<o.length;a++)r.push(o[a].toUpperCase());this.o=n,t&&(this.defaultRender=t),this.ignoreTags=r}Nr.prototype={o:jt,ignoreTags:[],defaultRender(e){return e},check(e){return this.get("validate",e.toString(),e)},get(e,t,n){const o=t!=null;let r=this.o[e];return r&&(typeof r=="object"?(r=n.t in r?r[n.t]:jt[e],typeof r=="function"&&o&&(r=r(t,n))):typeof r=="function"&&o&&(r=r(t,n.t,n)),r)},getObj(e,t,n){let o=this.o[e];return typeof o=="function"&&t!=null&&(o=o(t,n.t,n)),o},render(e){const t=e.render(this);return(this.get("render",null,e)||this.defaultRender)(t,e.t,e)}};function Ya(e){return e}function ps(e,t){this.t="token",this.v=e,this.tk=t}ps.prototype={isLink:!1,toString(){return this.v},toHref(e){return this.toString()},toFormattedString(e){const t=this.toString(),n=e.get("truncate",t,this),o=e.get("format",t,this);return n&&o.length>n?o.substring(0,n)+"…":o},toFormattedHref(e){return e.get("formatHref",this.toHref(e.get("defaultProtocol")),this)},startIndex(){return this.tk[0].s},endIndex(){return this.tk[this.tk.length-1].e},toObject(e=jt.defaultProtocol){return{type:this.t,value:this.toString(),isLink:this.isLink,href:this.toHref(e),start:this.startIndex(),end:this.endIndex()}},toFormattedObject(e){return{type:this.t,value:this.toFormattedString(e),isLink:this.isLink,href:this.toFormattedHref(e),start:this.startIndex(),end:this.endIndex()}},validate(e){return e.get("validate",this.toString(),this)},render(e){const t=this,n=this.toHref(e.get("defaultProtocol")),o=e.get("formatHref",n,this),r=e.get("tagName",n,t),a=this.toFormattedString(e),i={},s=e.get("className",n,t),l=e.get("target",n,t),c=e.get("rel",n,t),u=e.getObj("attributes",n,t),p=e.getObj("events",n,t);return i.href=o,s&&(i.class=s),l&&(i.target=l),c&&(i.rel=c),u&&bt(i,u),{tagName:r,attributes:i,content:a,eventListeners:p}}};function Xn(e,t){class n extends ps{constructor(r,a){super(r,a),this.t=e}}for(const o in t)n.prototype[o]=t[o];return n.t=e,n}const Qa=Xn("email",{isLink:!0,toHref(){return"mailto:"+this.toString()}}),Xa=Xn("text"),rm=Xn("nl"),an=Xn("url",{isLink:!0,toHref(e=jt.defaultProtocol){return this.hasProtocol()?this.v:`${e}://${this.v}`},hasProtocol(){const e=this.tk;return e.length>=2&&e[0].t!==Mt&&e[1].t===$e}}),se=e=>new ne(e);function am({groups:e}){const t=e.domain.concat([Dn,Ln,Ue,Fn,Rn,Nn,On,Mn,le,Cr,Lt,jn,Un,$n,ge,Hn,Rt,Vn]),n=[$e,Dr,he,Lr,Lt,Ft,Fr,Rr,Sn,En,Pt,Dt,vn,yn,wn,bn,xn,Tn,kn,_n,An,Cn,In,Pn],o=[Dn,Pr,Ln,Fn,Rn,Nn,On,Mn,le,Pt,Dt,Lt,jn,Un,$n,Ft,ge,Hn,Rt,Vn],r=se(),a=y(r,Rt);x(a,o,a),x(a,e.domain,a);const i=se(),s=se(),l=se();x(r,e.domain,i),x(r,e.scheme,s),x(r,e.slashscheme,l),x(i,o,a),x(i,e.domain,i);const c=y(i,Ue);y(a,Ue,c),y(s,Ue,c),y(l,Ue,c);const u=y(a,he);x(u,o,a),x(u,e.domain,a);const p=se();x(c,e.domain,p),x(p,e.domain,p);const f=y(p,he);x(f,e.domain,p);const m=se(Qa);x(f,e.tld,m),x(f,e.utld,m),y(c,Mt,m);const v=y(p,le);y(v,le,v),x(v,e.domain,p),x(m,e.domain,p),y(m,he,f),y(m,le,v);const w=y(m,$e);x(w,e.numeric,Qa);const b=y(i,le),g=y(i,he);y(b,le,b),x(b,e.domain,i),x(g,o,a),x(g,e.domain,i);const T=se(an);x(g,e.tld,T),x(g,e.utld,T),x(T,e.domain,i),x(T,o,a),y(T,he,g),y(T,le,b),y(T,Ue,c);const P=y(T,$e),j=se(an);x(P,e.numeric,j);const E=se(an),k=se();x(E,t,E),x(E,n,k),x(k,t,E),x(k,n,k),y(T,ge,E),y(j,ge,E);const N=y(s,$e),_=y(l,$e),K=y(_,ge),U=y(K,ge);x(s,e.domain,i),y(s,he,g),y(s,le,b),x(l,e.domain,i),y(l,he,g),y(l,le,b),x(N,e.domain,E),y(N,ge,E),y(N,Ft,E),x(U,e.domain,E),x(U,t,E),y(U,ge,E);const ee=[[Pt,Dt],[yn,vn],[wn,bn],[Sn,En],[xn,Tn],[kn,_n],[An,Cn],[In,Pn]];for(let de=0;de<ee.length;de++){const[Z,fe]=ee[de],be=y(E,Z);y(k,Z,be),y(be,fe,E);const Se=se(an);x(be,t,Se);const Re=se();x(be,n),x(Se,t,Se),x(Se,n,Re),x(Re,t,Se),x(Re,n,Re),y(Se,fe,E),y(Re,fe,E)}return y(r,Mt,T),y(r,Ir,rm),{start:r,tokens:ds}}function im(e,t,n){let o=n.length,r=0,a=[],i=[];for(;r<o;){let s=e,l=null,c=null,u=0,p=null,f=-1;for(;r<o&&!(l=s.go(n[r].t));)i.push(n[r++]);for(;r<o&&(c=l||s.go(n[r].t));)l=null,s=c,s.accepts()?(f=0,p=s):f>=0&&f++,r++,u++;if(f<0)r-=u,r<o&&(i.push(n[r]),r++);else{i.length>0&&(a.push(xo(Xa,t,i)),i=[]),r-=f,u-=f;const m=p.t,v=n.slice(r-u,r);a.push(xo(m,t,v))}}return i.length>0&&a.push(xo(Xa,t,i)),a}function xo(e,t,n){const o=n[0].s,r=n[n.length-1].e,a=t.slice(o,r);return new e(a,n)}const q={scanner:null,parser:null,tokenQueue:[],pluginQueue:[],customSchemes:[],initialized:!1};function sm(){q.scanner=nm(q.customSchemes);for(let e=0;e<q.tokenQueue.length;e++)q.tokenQueue[e][1]({scanner:q.scanner});q.parser=am(q.scanner.tokens);for(let e=0;e<q.pluginQueue.length;e++)q.pluginQueue[e][1]({scanner:q.scanner,parser:q.parser});return q.initialized=!0,q}function fs(e){return q.initialized||sm(),im(q.parser.start,e,us(q.scanner.start,e))}fs.scan=us;function lm(e,t=null,n=null){if(t&&typeof t=="object"){if(n)throw Error(`linkifyjs: Invalid link type ${t}; must be a string`);n=t,t=null}const o=new Nr(n),r=fs(e),a=[];for(let i=0;i<r.length;i++){const s=r[i];s.isLink&&(!t||s.t===t)&&o.check(s)&&a.push(s.toFormattedObject(o))}return a}const cm="text/plain",dm="us-ascii",To=(e,t)=>t.some(n=>n instanceof RegExp?n.test(e):n===e),um=new Set(["https:","http:","file:"]),pm=e=>{try{const{protocol:t}=new URL(e);return t.endsWith(":")&&!t.includes(".")&&!um.has(t)}catch{return!1}},fm=(e,{stripHash:t})=>{var p;const n=/^data:(?<type>[^,]*?),(?<data>[^#]*?)(?:#(?<hash>.*))?$/.exec(e);if(!n)throw new Error(`Invalid URL: ${e}`);let{type:o,data:r,hash:a}=n.groups;const i=o.split(";");a=t?"":a;let s=!1;i[i.length-1]==="base64"&&(i.pop(),s=!0);const l=((p=i.shift())==null?void 0:p.toLowerCase())??"",u=[...i.map(f=>{let[m,v=""]=f.split("=").map(w=>w.trim());return m==="charset"&&(v=v.toLowerCase(),v===dm)?"":`${m}${v?`=${v}`:""}`}).filter(Boolean)];return s&&u.push("base64"),(u.length>0||l&&l!==cm)&&u.unshift(l),`data:${u.join(";")},${s?r.trim():r}${a?`#${a}`:""}`};function Ja(e,t){if(t={defaultProtocol:"http",normalizeProtocol:!0,forceHttp:!1,forceHttps:!1,stripAuthentication:!0,stripHash:!1,stripTextFragment:!0,stripWWW:!0,removeQueryParameters:[/^utm_\w+/i],removeTrailingSlash:!0,removeSingleSlash:!0,removeDirectoryIndex:!1,removeExplicitPort:!1,sortQueryParameters:!0,...t},typeof t.defaultProtocol=="string"&&!t.defaultProtocol.endsWith(":")&&(t.defaultProtocol=`${t.defaultProtocol}:`),e=e.trim(),/^data:/i.test(e))return fm(e,t);if(pm(e))return e;const n=e.startsWith("//");!n&&/^\.*\//.test(e)||(e=e.replace(/^(?!(?:\w+:)?\/\/)|^\/\//,t.defaultProtocol));const r=new URL(e);if(t.forceHttp&&t.forceHttps)throw new Error("The `forceHttp` and `forceHttps` options cannot be used together");if(t.forceHttp&&r.protocol==="https:"&&(r.protocol="http:"),t.forceHttps&&r.protocol==="http:"&&(r.protocol="https:"),t.stripAuthentication&&(r.username="",r.password=""),t.stripHash?r.hash="":t.stripTextFragment&&(r.hash=r.hash.replace(/#?:~:text.*?$/i,"")),r.pathname){const i=/\b[a-z][a-z\d+\-.]{1,50}:\/\//g;let s=0,l="";for(;;){const u=i.exec(r.pathname);if(!u)break;const p=u[0],f=u.index,m=r.pathname.slice(s,f);l+=m.replace(/\/{2,}/g,"/"),l+=p,s=f+p.length}const c=r.pathname.slice(s,r.pathname.length);l+=c.replace(/\/{2,}/g,"/"),r.pathname=l}if(r.pathname)try{r.pathname=decodeURI(r.pathname)}catch{}if(t.removeDirectoryIndex===!0&&(t.removeDirectoryIndex=[/^index\.[a-z]+$/]),Array.isArray(t.removeDirectoryIndex)&&t.removeDirectoryIndex.length>0){let i=r.pathname.split("/");const s=i[i.length-1];To(s,t.removeDirectoryIndex)&&(i=i.slice(0,-1),r.pathname=i.slice(1).join("/")+"/")}if(r.hostname&&(r.hostname=r.hostname.replace(/\.$/,""),t.stripWWW&&/^www\.(?!www\.)[a-z\-\d]{1,63}\.[a-z.\-\d]{2,63}$/.test(r.hostname)&&(r.hostname=r.hostname.replace(/^www\./,""))),Array.isArray(t.removeQueryParameters))for(const i of[...r.searchParams.keys()])To(i,t.removeQueryParameters)&&r.searchParams.delete(i);if(!Array.isArray(t.keepQueryParameters)&&t.removeQueryParameters===!0&&(r.search=""),Array.isArray(t.keepQueryParameters)&&t.keepQueryParameters.length>0)for(const i of[...r.searchParams.keys()])To(i,t.keepQueryParameters)||r.searchParams.delete(i);if(t.sortQueryParameters){r.searchParams.sort();try{r.search=decodeURIComponent(r.search)}catch{}}t.removeTrailingSlash&&(r.pathname=r.pathname.replace(/\/$/,"")),t.removeExplicitPort&&r.port&&(r.port="");const a=e;return e=r.toString(),!t.removeSingleSlash&&r.pathname==="/"&&!a.endsWith("/")&&r.hash===""&&(e=e.replace(/\/$/,"")),(t.removeTrailingSlash||r.pathname==="/")&&r.hash===""&&t.removeSingleSlash&&(e=e.replace(/\/$/,"")),n&&!t.normalizeProtocol&&(e=e.replace(/^http:\/\//,"//")),t.stripProtocol&&(e=e.replace(/^(?:https?:)?\/\//,"")),e}const mm=[{hostname:"youtu.be",redirect:"youtube.com"},{hostname:"twitter.com",redirect:"x.com"}],hm=e=>{const n=lm(e).filter(o=>o.type!=="email");return console.log(n),n},Za={defaultProtocol:"https",stripWWW:!0,removeTrailingSlash:!0},gm=async(e,t)=>{let n=e.trim();if(!/\.[a-z]{2,}$/i.test(n)&&n!=="")throw new Error("messages.validUrlPrompt");try{n=Ja(n,Za)}catch{throw new Error("messages.validUrlPrompt")}const r=new URL(n);for(const s of mm)if(s.hostname===r.hostname){r.hostname=s.redirect,n=r.toString();break}if(new Set(t.map(({url:s})=>Ja(s,Za))).has(n))throw new Error("messages.websiteAlreadyInList");const i={name:r.hostname,url:n,isAuthorized:!1,authUrl:n,enableDelete:!0};return[...t,i]},ei=["B","KB","MB","GB","TB"],ti=(e,t="")=>{let n=e,o=0;for(;n>=1024&&o<ei.length-1;)n/=1024,o++;return`${n.toFixed(2)} ${ei[o]}${t}`};function ms(){const{t:e}=W(),{data:t=[],mutate:n}=mt("getSavedSites",Cf),o=pt(async s=>{const{authUrl:l,url:c}=s,{success:u}=await If(l,c);if(!u)return;const p=t.map(f=>f.authUrl===l?{...f,isAuthorized:!0}:f);nn(p),n()}),r=pt(async s=>{const{authUrl:l}=s;try{await Pf(l);const c=t.map(u=>u.authUrl===l?{...u,isAuthorized:!0}:u);nn(c),await n(),D.success(e("messages.removeAuthSuccess"))}catch{D.error(e("messages.removeAuthFailed"))}}),a=pt(async s=>{try{const l=await gm(s,t);return await nn(l),await n()}catch(l){D.error(e(l.message??"messages.defaultError"))}}),i=pt(async s=>{const l=t.filter(c=>c.authUrl!==s.authUrl);nn(l),n()});return{authSites:t,mutateAuthSites:n,loginSite:o,logoutSite:r,addCustomSite:a,deleteSite:i}}function ym(){const{t:e}=W(),{authSites:t,loginSite:n,logoutSite:o,addCustomSite:r,deleteSite:a}=ms();return d.jsxs("div",{className:"flex flex-col grow",children:[d.jsx("p",{className:"text-[#6B7280] font-[400] mb-2",children:e("settings.authorizationPanelTips")}),d.jsx(zn,{className:"space-y-2.5",unstyled:!0,children:t.map(i=>d.jsxs(Kf,{children:[d.jsx(Bf,{icon:d.jsx("img",{src:`https://www.google.com/s2/favicons?domain=${i.url}&sz=24`}),siteLink:i.url,siteName:i.name}),d.jsx(Gf,{isAuthorized:i.isAuthorized,enableDelete:i.enableDelete,onLogout:()=>o(i),onLogin:()=>n(i),onDelete:()=>a(i),logoutText:e("settings.logOut"),loginText:e("settings.logIn"),deleteText:e("settings.delete")})]},i.url))}),d.jsx("div",{className:"grow flex justify-center items-end py-4",children:d.jsx(rs,{className:"w-[80%]",actionText:e("settings.addUrl"),placeholder:e("messages.validUrlPrompt"),handleValidPath:r,disableBlurSubmit:!0})})]})}const vm=[{value:"en",label:"English"},{value:"zh-Hans",label:"简体中文"},{value:"hi",label:"हिन्दी"},{value:"es",label:"Español"},{value:"fr",label:"Français"},{value:"ru",label:"Русский"},{value:"id",label:"Bahasa Indonesia"},{value:"bn",label:"বাংলা"},{value:"pt",label:"Português"},{value:"de",label:"Deutsch"},{value:"ja",label:"日本語"},{value:"ko",label:"한국어"},{value:"vi",label:"Tiếng Việt"},{value:"tr",label:"Türkçe"},{value:"it",label:"Italiano"},{value:"zh-Hant",label:"繁體中文"}],ni="system";function wm(){const{t:e,i18n:t}=W(),n=h.useRef(null),o=h.useMemo(()=>[{value:ni,label:e("settings.system")}].concat(vm),[e]),{settings:r,patchSetting:a}=Ke(),i=async u=>{const p=u.target.value,f=p===ni?await xf():p;await Af(p),await a({language:p}),t.changeLanguage(f)},s=async()=>{const{path:u}=await Tf();await a({defaultDownloadPath:u})},l=async()=>{var u;(u=n.current)==null||u.setShowPath((r==null?void 0:r.defaultDownloadPath)??""),D.error(e("errors.notImplemented"))},c=async()=>{const u=r==null?void 0:r.defaultDownloadPath;u&&await Ef(u)};return d.jsxs("div",{className:"grid grid-cols-[auto_1fr] gap-x-[33px] gap-y-[22px] justify-center items-center",children:[d.jsxs(un,{htmlFor:"settingFileUpload",className:"text-end",children:[e("settings.saveTo")," :"]}),d.jsx(rs,{id:"settingFileUpload",handleValidPath:l,path:r==null?void 0:r.defaultDownloadPath,handleOpenFolder:c,handleChangeDownloadPath:s,ref:n,rightIcon:Ip,actionText:e("settings.changeFolderBrowser")}),d.jsxs(un,{htmlFor:"settingLanguage",className:"text-end",children:[e("settings.language")," :"]}),d.jsx(pi,{id:"settingLanguage",required:!0,defaultValue:r==null?void 0:r.language,onChange:i,children:o.map(u=>d.jsx("option",{value:u.value,children:u.label},u.value))})]})}var Oe=(e=>(e.system="SYSTEM",e.http="HTTP",e.socks5="SOCKS5",e.none="NONE",e))(Oe||{});function bm(){const{t:e}=W(),{settings:t,patchSetting:n}=Ke(),o=t==null?void 0:t.proxy,{type:r,host:a,password:i,port:s,username:l}=o??{},[c,u]=h.useState(r??Oe.none),p=h.useMemo(()=>[Oe.none,Oe.system].includes(c),[c]),f=m=>{if(m.preventDefault(),p){n({proxy:{type:c}}).then(()=>D.success(e("messages.saveSuccess")),()=>D.error(e("messages.saveFailed")));return}const v=new FormData(m.currentTarget),w=v.get("host"),b=v.get("port"),g=v.get("username"),T=v.get("password");if(!w){D.error(e("settings.proxyInfoMessage.pleaseEnterValidProxyHost"));return}if(!Uf(w)){D.error(e("settings.proxyInfoMessage.pleaseEnterValidProxyHost"));return}if(!b){D.error(e("settings.proxyInfoMessage.pleaseEnterProxyPort"));return}if(!jf(b)){D.error(e("settings.proxyInfoMessage.pleaseEnterValidProxyPort"));return}n({proxy:{type:c,host:w,port:b,username:g,password:T}}).then(()=>D.success(e("messages.saveSuccess")),()=>D.error(e("messages.saveFailed")))};return d.jsxs("form",{className:"grid grid-cols-[auto_1fr] gap-x-4.5 gap-y-6 items-center",onSubmit:f,children:[d.jsx(xt,{required:!0,children:e("settings.proxyType")}),d.jsxs(pi,{defaultValue:r??c,onChange:m=>u(m.target.value),children:[d.jsx("option",{value:Oe.system,children:e("settings.usingSystemProxy")}),d.jsxs("option",{value:Oe.http,children:["HTTP ",e("settings.proxy")]}),d.jsxs("option",{value:Oe.socks5,children:["SOCKS5 ",e("settings.proxy")]}),d.jsx("option",{value:Oe.none,children:e("settings.notUsingProxy")})]}),d.jsx(xt,{required:!0,children:e("settings.host")}),d.jsx(_t,{defaultValue:a,name:"host",placeholder:"127.0.0.1",disabled:p}),d.jsx(xt,{required:!0,children:e("settings.port")}),d.jsx(_t,{name:"port",defaultValue:s,placeholder:"7890",disabled:p}),d.jsx(xt,{children:e("settings.login")}),d.jsx(_t,{defaultValue:l,name:"username",placeholder:e("settings.proxyInfoMessage.optional"),disabled:p}),d.jsx(xt,{children:e("settings.password")}),d.jsx(_t,{defaultValue:i,name:"password",type:"password",placeholder:e("settings.proxyInfoMessage.optional"),disabled:p}),d.jsx("span",{}),d.jsx("div",{children:d.jsx(Pe,{size:"sm",color:"blue",type:"submit",children:e("settings.save")})})]})}function Sm({onClose:e}){const{t}=W(),[n,o]=h.useState("general"),r=h.useMemo(()=>[{key:"general",label:t("settings.general"),element:d.jsx(wm,{})},{key:"auth",label:t("settings.authorization"),element:d.jsx(ym,{})},{key:"proxy",label:t("settings.proxy"),element:d.jsx(bm,{})},{key:"about",label:t("settings.about"),element:d.jsx(Hf,{})}],[t]),a=h.useMemo(()=>r.find(i=>i.key===n).element,[r,n]);return d.jsxs("div",{className:"flex h-[70vh] w-[70vw] max-w-[1025px] max-h-[657px]",children:[d.jsx("aside",{className:"py-4 bg-[#E5E7EB] min-w-[185px]",children:r.map(({key:i,label:s})=>d.jsx(zf,{text:s,onClick:()=>o(i),active:n===i},i))}),d.jsxs("main",{className:"flex grow bg-white flex-col justify-center pb-0",children:[d.jsx("section",{className:"flex justify-end mb-[22px] pt-4 px-4",children:d.jsx(Pp,{className:"text-xl cursor-pointer",onClick:e})}),d.jsx("section",{className:"flex flex-col grow px-4 overflow-auto",children:a})]})]})}function Em(e){return H({attr:{viewBox:"0 0 576 512"},child:[{tag:"path",attr:{d:"M64 0C28.7 0 0 28.7 0 64L0 352c0 35.3 28.7 64 64 64l176 0-10.7 32L160 448c-17.7 0-32 14.3-32 32s14.3 32 32 32l256 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-69.3 0L336 416l176 0c35.3 0 64-28.7 64-64l0-288c0-35.3-28.7-64-64-64L64 0zM512 64l0 288L64 352 64 64l448 0z"},child:[]}]})(e)}function xm(e){return H({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M288 32c0-17.7-14.3-32-32-32s-32 14.3-32 32l0 242.7-73.4-73.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3l128 128c12.5 12.5 32.8 12.5 45.3 0l128-128c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L288 274.7 288 32zM64 352c-35.3 0-64 28.7-64 64l0 32c0 35.3 28.7 64 64 64l384 0c35.3 0 64-28.7 64-64l0-32c0-35.3-28.7-64-64-64l-101.5 0-45.3 45.3c-25 25-65.5 25-90.5 0L165.5 352 64 352zm368 56a24 24 0 1 1 0 48 24 24 0 1 1 0-48z"},child:[]}]})(e)}function Tm(e){return H({attr:{viewBox:"0 0 1024 1024"},child:[{tag:"path",attr:{d:"M574 665.4a8.03 8.03 0 0 0-11.3 0L446.5 781.6c-53.8 53.8-144.6 59.5-204 0-59.5-59.5-53.8-150.2 0-204l116.2-116.2c3.1-3.1 3.1-8.2 0-11.3l-39.8-39.8a8.03 8.03 0 0 0-11.3 0L191.4 526.5c-84.6 84.6-84.6 221.5 0 306s221.5 84.6 306 0l116.2-116.2c3.1-3.1 3.1-8.2 0-11.3L574 665.4zm258.6-474c-84.6-84.6-221.5-84.6-306 0L410.3 307.6a8.03 8.03 0 0 0 0 11.3l39.7 39.7c3.1 3.1 8.2 3.1 11.3 0l116.2-116.2c53.8-53.8 144.6-59.5 204 0 59.5 59.5 53.8 150.2 0 204L665.3 562.6a8.03 8.03 0 0 0 0 11.3l39.8 39.8c3.1 3.1 8.2 3.1 11.3 0l116.2-116.2c84.5-84.6 84.5-221.5 0-306.1zM610.1 372.3a8.03 8.03 0 0 0-11.3 0L372.3 598.7a8.03 8.03 0 0 0 0 11.3l39.6 39.6c3.1 3.1 8.2 3.1 11.3 0l226.4-226.4c3.1-3.1 3.1-8.2 0-11.3l-39.5-39.6z"},child:[]}]})(e)}const km=e=>h.createElement("svg",{width:250,height:250,viewBox:"0 0 250 250",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e},h.createElement("g",{id:"empty 1",clipPath:"url(#clip0_4_3244)"},h.createElement("path",{id:"Vector",d:"M224.938 45.7771H29.921C27.29 45.7787 24.7672 46.8246 22.9068 48.685C21.0464 50.5454 20.0005 53.0682 19.9988 55.6992V234.63C20.0005 237.261 21.0464 239.783 22.9068 241.644C24.7672 243.504 27.29 244.55 29.921 244.552H224.938C227.57 244.55 230.093 243.505 231.953 241.644C233.814 239.784 234.86 237.261 234.862 234.63V55.6992C234.86 53.068 233.814 50.5451 231.953 48.6847C230.093 46.8242 227.57 45.7785 224.938 45.7771Z",fill:"#C6C6C6"}),h.createElement("path",{id:"Vector_2",d:"M212.1 182.374H42.7612C37.2817 182.374 32.8391 177.932 32.8391 172.452V14.3581C32.8391 8.8786 37.2817 4.43597 42.7612 4.43597H212.1C217.58 4.43597 222.023 8.8786 222.023 14.3581V172.452C222.023 177.932 217.58 182.374 212.1 182.374Z",fill:"#F9F9F9"}),h.createElement("path",{id:"Vector_3",d:"M35.9805 175.593V17.4995C35.9805 12.02 40.4231 7.57737 45.9026 7.57737H215.242C217.177 7.57737 218.976 8.13987 220.502 9.09786C218.748 6.30073 215.645 4.43597 212.1 4.43597H42.7612C37.2817 4.43597 32.8391 8.8786 32.8391 14.3581V172.452C32.8391 175.998 34.7039 179.1 37.501 180.855C36.543 179.328 35.9805 177.529 35.9805 175.594V175.593Z",fill:"url(#paint0_linear_4_3244)"}),h.createElement("path",{id:"Vector_4",d:"M191.265 37.8388H63.5979V45.1145H191.265V37.8388ZM191.265 65.6196H63.5979V72.8952H191.265V65.6196ZM191.265 93.4065H63.5979V100.682H191.265V93.4065ZM191.265 121.187H63.5979V128.464H191.265V121.187Z",fill:"white"}),h.createElement("path",{id:"Vector_5",d:"M78.8116 97.7036L110.893 129.786H78.8116V97.7036Z",fill:"url(#paint1_linear_4_3244)"}),h.createElement("path",{id:"Vector_6",d:"M234.862 132.432V58.6173L222.023 45.7771V132.431H234.862V132.432Z",fill:"#C6C6C6"}),h.createElement("path",{id:"Vector_7",d:"M249.901 139.109L236.068 237.031C235.376 241.926 231.186 245.565 226.243 245.565H28.6179C23.6742 245.565 19.4847 241.926 18.7931 237.031L0.0994092 104.714C-0.743828 98.7408 3.89142 93.4037 9.9242 93.4037H70.9549C75.8986 93.4037 80.0881 97.042 80.7797 101.936L83.2295 119.264C83.9211 124.16 88.1107 127.798 93.0543 127.798H240.078C246.109 127.798 250.745 133.135 249.902 139.109H249.901Z",fill:"url(#paint2_linear_4_3244)"}),h.createElement("path",{id:"Vector_8",d:"M200.889 201.887H53.9737C53.1685 201.887 52.3909 201.593 51.7868 201.061C51.1827 200.528 50.7934 199.794 50.692 198.995L48.8539 184.442C48.7955 183.976 48.8367 183.503 48.9749 183.055C49.1131 182.607 49.3451 182.193 49.6554 181.841C49.9658 181.489 50.3474 181.207 50.775 181.014C51.2026 180.821 51.6664 180.721 52.1356 180.72H202.727C203.196 180.721 203.66 180.821 204.087 181.014C204.515 181.207 204.897 181.489 205.207 181.841C205.517 182.193 205.749 182.607 205.887 183.055C206.026 183.503 206.067 183.976 206.009 184.442L204.17 198.995C204.069 199.794 203.68 200.528 203.076 201.061C202.471 201.593 201.694 201.887 200.889 201.887Z",fill:"#D5D5D5"})),h.createElement("defs",null,h.createElement("linearGradient",{id:"paint0_linear_4_3244",x1:133.249,y1:99.2126,x2:7.7674,y2:-26.27,gradientUnits:"userSpaceOnUse"},h.createElement("stop",{stopColor:"white"})),h.createElement("linearGradient",{id:"paint1_linear_4_3244",x1:100.007,y1:134.933,x2:61.2296,y2:96.1565,gradientUnits:"userSpaceOnUse"},h.createElement("stop",{stopColor:"#C2CECE",stopOpacity:0}),h.createElement("stop",{offset:.179,stopColor:"#AFBCBC",stopOpacity:.179}),h.createElement("stop",{offset:1,stopColor:"#5B6A6A"})),h.createElement("linearGradient",{id:"paint2_linear_4_3244",x1:125,y1:93.4037,x2:125,y2:245.565,gradientUnits:"userSpaceOnUse"},h.createElement("stop",{stopColor:"#EEF0F4"}),h.createElement("stop",{offset:.927,stopColor:"#E4E4E4"})),h.createElement("clipPath",{id:"clip0_4_3244"},h.createElement("rect",{width:250,height:250,fill:"white"}))));function te(e){return d.jsxs(De.Item,{className:"flex justify-between gap-4 text-nowrap",onClick:e.onClick,children:[d.jsx("span",{children:e.label}),d.jsx(Cp,{className:e.showCheck?"visible":"invisible"})]})}function oi(e){const{label:t,children:n}=e;return d.jsx(De.Item,{as:"div",children:d.jsx(De,{dismissOnClick:!1,inline:!0,trigger:"hover",placement:"right",label:t,theme:{inlineWrapper:"grow flex justify-between items-center -mr-4 pr-4",content:"max-h-[90vh] overflow-auto"},children:n})})}function Or(e){return d.jsxs("div",{className:"flex gap-1 cursor-pointer text-sm",children:[d.jsx("span",{className:"text-gray-500",children:e.label}),d.jsx("span",{children:e.content})]})}function _m(e){const{t}=W(),{data:n,render:o}=e;return n.length===0?d.jsxs("div",{className:"flex flex-col justify-center items-center h-full",children:[d.jsx(km,{className:"scale-75"}),d.jsxs("section",{className:"text-[#6B7280]",children:[d.jsx("p",{children:t("download.emptyState.step1")}),d.jsx("p",{children:t("download.emptyState.step2")})]})]}):d.jsx(zn,{unstyled:!0,className:"space-y-2.5",children:n.map(o)})}const hs=[{value:"en",label:"English"},{value:"zh",label:"中文"},{value:"es",label:"Español"},{value:"fr",label:"Français"},{value:"de",label:"Deutsch"},{value:"ja",label:"日本語"},{value:"ko",label:"한국어"},{value:"ru",label:"Русский"},{value:"pt",label:"Português"},{value:"it",label:"Italiano"},{value:"nl",label:"Nederlands"},{value:"tr",label:"Türkçe"},{value:"pl",label:"Polski"},{value:"sv",label:"Svenska"},{value:"fi",label:"Suomi"},{value:"da",label:"Dansk"},{value:"no",label:"Norsk"},{value:"el",label:"Ελληνικά"},{value:"cs",label:"Čeština"},{value:"hu",label:"Magyar"},{value:"uk",label:"Українská"},{value:"ro",label:"Română"},{value:"ar",label:"العربية"},{value:"hi",label:"हिन्दी"},{value:"th",label:"ไทย"},{value:"vi",label:"Tiếng Việt"},{value:"id",label:"Bahasa Indonesia"},{value:"ms",label:"Bahasa Melayu"},{value:"fil",label:"Filipino"},{value:"he",label:"עברית"},{value:"fa",label:"فارسی"},{value:"bn",label:"বাংলা"},{value:"ta",label:"தமிழ்"},{value:"te",label:"తెలుగు"},{value:"ur",label:"اردو"}],Am=hs.map(e=>({...e,value:`${e.value}`})),Cm=[{label:"8K(4320p)",value:"4320"},{label:"4K(2160p)",value:"2160"},{label:"2K(1440p)",value:"1440"},{label:"1080p",value:"1080"},{label:"720p",value:"720"},{label:"480p",value:"480"},{label:"360p",value:"360"},{label:"240p",value:"240"}],Im=[{label:"320k",value:"320"},{label:"256k",value:"256"},{label:"128k",value:"128"},{label:"64k",value:"64"}],ri=[{label:"MP4",value:"mp4"},{label:"MKV",value:"mkv"}],ir=[{label:"MP3",value:"mp3"},{label:"M4A",value:"m4a"},{label:"OGG",value:"ogg"}],ai=[{label:"Windows",value:"windows"},{label:"Mac OS",value:"macos"},{label:"Linux",value:"linux"},{label:"iOS",value:"ios"},{label:"Android",value:"android"}],ii={video:{windows:"mp4",macos:"mp4",linux:"mkv",ios:"mp4",android:"mp4"},audio:{windows:"mp3",macos:"m4a",linux:"ogg",ios:"m4a",android:"mp3"}},si=e=>{let t;const n=new Set,o=(c,u)=>{const p=typeof c=="function"?c(t):c;if(!Object.is(p,t)){const f=t;t=u??(typeof p!="object"||p===null)?p:Object.assign({},t,p),n.forEach(m=>m(t,f))}},r=()=>t,s={setState:o,getState:r,getInitialState:()=>l,subscribe:c=>(n.add(c),()=>n.delete(c))},l=t=e(o,r,s);return s},Pm=e=>e?si(e):si,Dm=e=>e;function Lm(e,t=Dm){const n=Nt.useSyncExternalStore(e.subscribe,()=>t(e.getState()),()=>t(e.getInitialState()));return Nt.useDebugValue(n),n}const li=e=>{const t=Pm(e),n=o=>Lm(t,o);return Object.assign(n,t),n},gs=e=>e?li(e):li,sr=(e,t)=>{const n=new Set(t);return n.has(e)?n.delete(e):n.add(e),[...n]},tt=gs(e=>({mediaType:"video",hasCover:!1,subtitles:"none",audioTracks:"all",quality:"best",bitrate:"best",platform:"none",videoFormat:"mp4",audioFormat:"m4a",subtitleAddOrRemove:t=>e(n=>({subtitles:sr(t,n.subtitles==="none"?[]:n.subtitles)})),audioTracksAddOrRemove:t=>e(n=>({audioTracks:sr(t,n.audioTracks==="all"?[]:n.audioTracks)})),changeMediaType:t=>e(()=>({mediaType:t})),updateHasCover:()=>e(t=>({hasCover:!t.hasCover})),resetAudioTracks:()=>e(()=>({subtitles:"none"})),resetSubtitles:()=>e(()=>({audioTracks:"all"})),setDefaultAudioTracks:()=>e(t=>({audioTracks:Array.isArray(t.audioTracks)?t.audioTracks:[]})),changeQuality:t=>e(()=>({quality:t})),changeBitrate:t=>e(()=>({bitrate:t})),changeVideoFormat:t=>e(()=>({videoFormat:t,platform:"none"})),changeAudioFormat:t=>e(()=>({audioFormat:t,platform:"none"})),changePlatform:t=>{const n=ii.video[t],o=ii.audio[t];e(()=>({platform:t,audioFormat:o,videoFormat:n}))},loadSetting(t){const n=Vf(t);e(n)}}));function Fm(){const{t:e}=W(),{subtitleAddOrRemove:t,audioTracksAddOrRemove:n,changeMediaType:o,mediaType:r,hasCover:a,updateHasCover:i,subtitles:s,audioTracks:l,resetAudioTracks:c,resetSubtitles:u,setDefaultAudioTracks:p}=tt(),{updateDownloadConfig:f}=Ke(),m=h.useMemo(()=>r==="video",[r]),v=h.useMemo(()=>e(m?"download.resourceType.video":"download.resourceType.audio"),[m,e]),w=h.useCallback(b=>async()=>{await b();const g=tt.getState();await f(g)},[f]);return d.jsxs(De,{label:d.jsx(Or,{label:e("download.download"),content:v}),inline:!0,dismissOnClick:!1,theme:{arrowIcon:"ml-1 text-gray-500"},children:[d.jsx(te,{label:e("download.resourceType.video"),showCheck:m,onClick:w(()=>o("video"))}),d.jsx(te,{label:e("download.resourceType.audio"),showCheck:!m,onClick:w(()=>o("audio"))}),d.jsx(De.Divider,{}),d.jsx(te,{label:e("download.thumbnail"),showCheck:a,onClick:w(()=>i())}),m&&d.jsxs(oi,{label:e("download.subtitles"),children:[d.jsx(te,{label:e("download.none"),showCheck:s==="none",onClick:w(()=>c())}),hs.map(({label:b,value:g})=>d.jsx(te,{showCheck:s.includes(g),label:b,onClick:w(()=>t(g))},g))]}),d.jsxs(oi,{label:e("download.audioTracks"),children:[d.jsx(te,{label:e("download.default"),showCheck:l!=="all",onClick:w(()=>p())}),d.jsx(te,{label:e("download.allTracks"),showCheck:l==="all",onClick:w(()=>u())}),d.jsx(De.Divider,{}),Am.map(({label:b,value:g})=>d.jsx(te,{label:b,showCheck:!!(l!=null&&l.includes(g)),onClick:w(()=>n(g))},g))]})]})}function Rm(){var m,v,w;const{t:e}=W(),{platform:t,mediaType:n,videoFormat:o,changePlatform:r,changeVideoFormat:a,audioFormat:i,changeAudioFormat:s}=tt(),{updateDownloadConfig:l}=Ke(),c=h.useMemo(()=>n==="video",[n]),u=h.useCallback(b=>async()=>{await b();const g=tt.getState();await l(g)},[l]),p=()=>ri.map(({label:b,value:g})=>d.jsx(te,{label:b,showCheck:g===o,onClick:u(()=>a(g))},g)),f=()=>ir.map(({label:b,value:g})=>d.jsx(te,{label:b,showCheck:g===i,onClick:u(()=>s(g))},g));return d.jsxs(De,{inline:!0,label:d.jsx(Or,{content:t!=="none"?(m=ai.find(b=>b.value===t))==null?void 0:m.label:c?(v=ri.find(b=>b.value===o))==null?void 0:v.label:(w=ir.find(b=>b.value===i))==null?void 0:w.label,label:e(t==="none"?"download.format":"download.for")}),theme:{arrowIcon:"ml-1 text-gray-500"},children:[c?p():f(),d.jsx(De.Divider,{}),ai.map(({label:b,value:g})=>d.jsx(te,{label:b,showCheck:g===t,onClick:u(()=>r(g))},g))]})}function Nm(){const{t:e}=W(),{mediaType:t,changeBitrate:n,changeQuality:o,quality:r,bitrate:a}=tt(),{updateDownloadConfig:i}=Ke(),s=h.useMemo(()=>[{label:e("download.videoQuality.best"),value:"best"},...Cm],[e]),l=h.useMemo(()=>[{label:e("download.audioQuality.highest"),value:"best"},...Im],[e]),c=h.useMemo(()=>t==="video",[t]),u=h.useMemo(()=>{var b;const v=c?r:a;return(b=(c?s:l).find(g=>g.value===v))==null?void 0:b.label},[c,s,l,r,a]),p=h.useCallback(v=>async()=>{await v();const w=tt.getState();await i(w)},[i]),f=()=>s.map(({label:v,value:w})=>d.jsx(te,{showCheck:r===w,label:v,onClick:p(()=>o(w))},w)),m=()=>l.map(({label:v,value:w})=>d.jsx(te,{showCheck:a===w,label:v,onClick:p(()=>n(w))},w));return d.jsx(De,{inline:!0,label:d.jsx(Or,{content:u,label:e("download.quality")}),theme:{arrowIcon:"ml-1 text-gray-500"},children:c?f():m()})}function Om(){return d.jsxs("div",{className:"flex items-center space-x-5",children:[d.jsx(Fm,{}),d.jsx(Nm,{}),d.jsx(Rm,{})]})}function Mm(e){const{open:t,onCancel:n,links:o,onOk:r}=e,{t:a}=W(),[i,s]=h.useState([]),l=h.useMemo(()=>i.length===o.length,[i.length,o.length]);return d.jsxs(to,{show:t,onClose:n,className:"bg-gray-900/50 dar:bg-gray-900/80",children:[d.jsx(to.Header,{className:"border-b-0",children:d.jsx("b",{className:"text-2xl",children:a("download.modal.needDownloadToSelect")})}),d.jsxs(to.Body,{children:[o.map(c=>d.jsxs("section",{className:"gap-3 flex items-center",children:[d.jsx(Gr,{className:"checked:bg-[#1C64F2] checked:border-[#1C64F2]",color:"blue",id:`multipleLink/${c.href}`,checked:i.includes(c.href),onChange:()=>{const u=sr(c.href,i);s(u)}}),d.jsx(un,{htmlFor:`multipleLink/${c.href}`,children:c.href})]},c.href)),d.jsxs("div",{className:"pt-8 flex items-center gap-3",children:[d.jsx(Gr,{color:"blue",className:"checked:bg-[#1C64F2] checked:border-[#1C64F2]",id:"selectAll",checked:l,onClick:()=>{s(l?[]:o.map(c=>c.href))}}),d.jsx(un,{htmlFor:"selectAll",children:a(l?"download.modal.cancelAll":"download.modal.selectAll")}),d.jsx(Pe,{className:"ml-auto",color:"blue",onClick:()=>r(i),children:a("download.modal.download")})]})]})]})}var C=(e=>(e.DownloadingAudio="downloading_audio",e.DownloadingVideo="downloading_video",e.Completed="completed",e.Merging="merging",e.DownloadError="download_error",e.ParseError="parse_error",e.UnsupportedUrl="unsupported_url",e.ParseSignError="parse_sign_error",e.DownloadSignError="download_sign_error",e.Cancelled="cancelled",e.Pending="pending",e.Parsing="parsing",e.MergeError="merge_error",e))(C||{});function jm(e){return H({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M256,8C119,8,8,119,8,256S119,504,256,504,504,393,504,256,393,8,256,8Zm92.49,313h0l-20,25a16,16,0,0,1-22.49,2.5h0l-67-49.72a40,40,0,0,1-15-31.23V112a16,16,0,0,1,16-16h32a16,16,0,0,1,16,16V256l58,42.5A16,16,0,0,1,348.49,321Z"},child:[]}]})(e)}function Um(e){return H({attr:{viewBox:"0 0 384 512"},child:[{tag:"path",attr:{d:"M224 136V0H24C10.7 0 0 10.7 0 24v464c0 13.3 10.7 24 24 24h336c13.3 0 24-10.7 24-24V160H248c-13.2 0-24-10.8-24-24zm160-14.1v6.1H256V0h6.1c6.4 0 12.5 2.5 17 7l97.9 98c4.5 4.5 7 10.6 7 16.9z"},child:[]}]})(e)}function $m(e){return H({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M256 32C114.52 32 0 146.496 0 288v48a32 32 0 0 0 17.689 28.622l14.383 7.191C34.083 431.903 83.421 480 144 480h24c13.255 0 24-10.745 24-24V280c0-13.255-10.745-24-24-24h-24c-31.342 0-59.671 12.879-80 33.627V288c0-105.869 86.131-192 192-192s192 86.131 192 192v1.627C427.671 268.879 399.342 256 368 256h-24c-13.255 0-24 10.745-24 24v176c0 13.255 10.745 24 24 24h24c60.579 0 109.917-48.098 111.928-108.187l14.382-7.191A32 32 0 0 0 512 336v-48c0-141.479-114.496-256-256-256z"},child:[]}]})(e)}function Vm(e){return H({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M470.38 1.51L150.41 96A32 32 0 0 0 128 126.51v261.41A139 139 0 0 0 96 384c-53 0-96 28.66-96 64s43 64 96 64 96-28.66 96-64V214.32l256-75v184.61a138.4 138.4 0 0 0-32-3.93c-53 0-96 28.66-96 64s43 64 96 64 96-28.65 96-64V32a32 32 0 0 0-41.62-30.49z"},child:[]}]})(e)}function Hm(e){return H({attr:{viewBox:"0 0 576 512"},child:[{tag:"path",attr:{d:"M336.2 64H47.8C21.4 64 0 85.4 0 111.8v288.4C0 426.6 21.4 448 47.8 448h288.4c26.4 0 47.8-21.4 47.8-47.8V111.8c0-26.4-21.4-47.8-47.8-47.8zm189.4 37.7L416 177.3v157.4l109.6 75.5c21.2 14.6 50.4-.3 50.4-25.8V127.5c0-25.4-29.1-40.4-50.4-25.8z"},child:[]}]})(e)}function ys({okText:e,cancelText:t,children:n,title:o,okButtonProps:r,cancelButtonProps:a,onCancel:i,onConfirm:s,disabled:l}){const{t:c}=W(),[u,p]=h.useState(!1);return d.jsx(ks,{open:l?!1:u,onOpenChange:f=>{l||p(f)},content:d.jsxs("div",{className:"px-7.5 py-4 flex flex-col gap-6",children:[d.jsx("main",{className:" flex items-center justify-center text-sm",children:o}),d.jsxs("footer",{className:"flex justify-around gap-6",children:[d.jsx(Pe,{className:"text-sm",color:"light",...a,onClick:f=>{p(!1),i==null||i(f)},children:t??c("common.cancel")}),d.jsx(Pe,{className:"text-sm",color:"blue",...r,onClick:s,children:e??c("common.ok")})]})]}),children:d.jsx("div",{children:n})})}const zm=window.electronAPI.platform==="darwin";function Bm({onDelete:e,onOpen:t,task:n,className:o}){const[r,a]=h.useState(!1),{t:i}=W();return d.jsx(ys,{title:d.jsx("div",{className:"max-w-[181px] text-center",children:i("download.popconfirm.openFileFailed")}),disabled:!r,onConfirm:()=>e==null?void 0:e(n),children:d.jsx(dn,{content:i(zm?"taskActions.showInFinder":"taskActions.showInFolder"),children:d.jsx(Dp,{className:o,onClick:s=>{r||t==null||t(n).then(({success:l})=>{l||(a(!0),setTimeout(()=>{var c;(c=s.target)==null||c.dispatchEvent(new MouseEvent("click",{bubbles:!0}))},0))})}})})})}function kt(e){return d.jsxs("section",{className:"flex items-center gap-1",children:[e.icon,e.content]})}function Km(e){return d.jsxs("div",{children:[d.jsx(zn.Item,{className:"py-5 px-4 grid grid-cols-[1fr_auto] gap-x-2 bg-white",children:e.children}),e.progress>0&&e.progress<100&&d.jsx(_s,{size:"sm",progress:e.progress,color:"blue",theme:{base:"rounded-none",bar:"rounded-none"}})]})}function Gm(e){return d.jsxs("p",{className:"inline-flex gap-1",children:[d.jsx("span",{children:e.width}),d.jsx("span",{children:"∗"}),d.jsx("span",{children:e.height})]})}const qm=e=>h.createElement("svg",{viewBox:"0 0 1098 1024",xmlns:"http://www.w3.org/2000/svg",...e},h.createElement("title",null,"Image placeholder"),h.createElement("path",{d:"M365.714286 329.142857q0 45.714286-32.036571 77.677714t-77.677714 32.036571-77.677714-32.036571-32.036571-77.677714 32.036571-77.677714 77.677714-32.036571 77.677714 32.036571 32.036571 77.677714zM950.857143 548.571429l0 256-804.571429 0 0-109.714286 182.857143-182.857143 91.428571 91.428571 292.571429-292.571429zM1005.714286 146.285714l-914.285714 0q-7.460571 0-12.873143 5.412571t-5.412571 12.873143l0 694.857143q0 7.460571 5.412571 12.873143t12.873143 5.412571l914.285714 0q7.460571 0 12.873143-5.412571t5.412571-12.873143l0-694.857143q0-7.460571-5.412571-12.873143t-12.873143-5.412571zM1097.142857 164.571429l0 694.857143q0 37.741714-26.843429 64.585143t-64.585143 26.843429l-914.285714 0q-37.741714 0-64.585143-26.843429t-26.843429-64.585143l0-694.857143q0-37.741714 26.843429-64.585143t64.585143-26.843429l914.285714 0q37.741714 0 64.585143 26.843429t26.843429 64.585143z",className:"ant-skeleton-image-path"}));function Wm({url:e,headers:t,loading:n}){const{data:o,isLoading:r}=mt(e&&t?`fetchImage/${e}`:null,()=>Df(e,t),{}),a=h.useMemo(()=>r||n?d.jsx(As,{size:"xl",theme:{color:{info:"fill-[#1C64F2]"},light:{off:{base:"text-[#374151]"}}}}):o!=null&&o.dataBase64?d.jsx("div",{className:"w-full h-full bg-cover bg-no-repeat bg-center",style:{backgroundImage:`url(data:image/jpeg;base64,${o.dataBase64})`}}):d.jsx(qm,{className:"w-12 h-12 fill-[#bfbfbf]"}),[r,n,o==null?void 0:o.dataBase64]);return d.jsx("div",{className:"shrink-0 w-[143px] h-[88px] bg-[#D9D9D9] flex items-center justify-center",children:a})}const Ym=e=>{const t=Math.floor(e),n=Math.floor(t/3600),o=Math.floor(t%3600/60),r=t%60;return n>0?`${n}:${o.toString().padStart(2,"0")}:${r.toString().padStart(2,"0")}`:`${o}:${r.toString().padStart(2,"0")}`},Qm=e=>{if(!e)return"Unknown";const t=["B","KB","MB","GB"];let n=e,o=0;for(;n>=1024&&o<t.length-1;)n/=1024,o++;return`${n.toFixed(2)} ${t[o]}`},Xm=[C.DownloadError,C.ParseError,C.ParseSignError,C.DownloadSignError,C.UnsupportedUrl],Jm=[C.Parsing,C.DownloadingAudio,C.DownloadingVideo];function Zm({task:e,onDelete:t,onOpen:n,onRetry:o,onAuth:r}){const{t:a}=W(),{settings:i}=Ke(),s=i==null?void 0:i.defaultDownloadPath,l=e==null?void 0:e.finalFilename,c=e.videoInfo,u=(e==null?void 0:e.status)??"",p=u===C.Completed,{data:f}=mt(p&&l&&s?`getFileInfo/${l}`:null,()=>kf(`${s}/${l}`),{shouldRetryOnError:!1}),m=Xm.includes(u),v=h.useMemo(()=>{if(p)return 100;const P=Number(e==null?void 0:e.progress);return P>0&&P<100?P:0},[p,e==null?void 0:e.progress]),w=h.useMemo(()=>{if(u)switch(u){case C.DownloadingAudio:case C.DownloadingVideo:return a("taskStatus.downloading");case C.Merging:return a("taskStatus.merging");case C.DownloadError:return e.error||a("taskStatus.downloadFailed");case C.ParseError:case C.UnsupportedUrl:return e.error||a("taskStatus.parseFailed");case C.DownloadSignError:case C.ParseSignError:return d.jsxs("p",{children:[a("errors.needLoginToDownload"),d.jsx("span",{className:"px-1",onClick:()=>r==null?void 0:r(e),children:a("auth.logIn")})]});case C.Cancelled:return a("taskStatus.cancelled");default:return a("taskStatus.preparingToDownload")}},[u,a,e,r]),b=()=>{if(!f)return;const{filesize:P,format:j,resolutionWidth:E,resolutionHeight:k,duration:N,audioBitrate:_}=f,K=ir.map(({value:ee})=>ee).includes(j),U=ee=>!["Unknown",""].includes(ee);return d.jsxs(d.Fragment,{children:[d.jsx(kt,{icon:K?d.jsx(Vm,{}):d.jsx(Hm,{}),content:j.toUpperCase()}),Number(N)>0&&d.jsx(kt,{icon:d.jsx(jm,{}),content:Ym(N)}),K?U(_)&&d.jsx(kt,{icon:d.jsx($m,{}),content:_}):d.jsx(kt,{icon:d.jsx(Em,{}),content:d.jsx(Gm,{width:E,height:k})}),d.jsx(kt,{icon:d.jsx(Um,{}),content:Qm(P)})]})},g=()=>{if(!e)return;const{speed:P,eta:j,downloadSize:E,status:k}=e;return d.jsxs("div",{className:Cs({"text-[#ff4d4f]":m}),children:[w,!m&&k!==C.Merging&&d.jsxs("div",{className:"flex gap-3",children:[P&&d.jsxs("span",{children:[a("taskActions.speed"),":"," ",ti(Number(P),"/s")]}),j&&d.jsxs("span",{children:[a("taskActions.timeLeft"),": ",j]}),Number(E)>0&&d.jsxs("span",{children:[a("taskActions.fileSize"),":"," ",ti(Number(E))]})]})]})},T=()=>{const P="text-3xl cursor-pointer",j=!Jm.includes(u);return d.jsxs(d.Fragment,{children:[p?d.jsx(Bm,{task:e,onOpen:n,onDelete:t,className:P}):m&&u!==C.UnsupportedUrl&&d.jsx(dn,{content:a("taskActions.retry"),children:d.jsx(Lp,{className:P,onClick:()=>o==null?void 0:o(e)})}),d.jsx(ys,{disabled:j,title:a("download.popconfirm.downloadingDeleteTitle"),onConfirm:()=>t==null?void 0:t(e),okButtonProps:{color:"failure"},okText:a("download.popconfirm.deleteText"),children:d.jsx(dn,{content:a("taskActions.delete"),children:d.jsx(Fp,{className:P,onClick:()=>j&&(t==null?void 0:t(e))})})})]})};return d.jsxs(Km,{progress:v,children:[d.jsxs("main",{className:"flex gap-8 items-center",children:[d.jsx(Wm,{url:c.thumbnail,headers:c.thumbnailHeaders,loading:u===C.Parsing}),d.jsxs("div",{className:"flex flex-col justify-between h-full gap-y-1",children:[d.jsx("section",{className:"table table-fixed w-full",children:d.jsx("p",{className:"text-sm text-ellipsis overflow-hidden whitespace-nowrap",children:c.title||e.url})}),d.jsx("section",{className:"flex gap-x-[39px] text-[#666] text-sm flex-wrap",children:p?b():g()})]})]}),d.jsx("aside",{className:"flex items-center gap-6",children:T()})]},c.id)}const eh=h.memo(Zm),B=[];for(let e=0;e<256;++e)B.push((e+256).toString(16).slice(1));function th(e,t=0){return(B[e[t+0]]+B[e[t+1]]+B[e[t+2]]+B[e[t+3]]+"-"+B[e[t+4]]+B[e[t+5]]+"-"+B[e[t+6]]+B[e[t+7]]+"-"+B[e[t+8]]+B[e[t+9]]+"-"+B[e[t+10]]+B[e[t+11]]+B[e[t+12]]+B[e[t+13]]+B[e[t+14]]+B[e[t+15]]).toLowerCase()}let ko;const nh=new Uint8Array(16);function oh(){if(!ko){if(typeof crypto>"u"||!crypto.getRandomValues)throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");ko=crypto.getRandomValues.bind(crypto)}return ko(nh)}const rh=typeof crypto<"u"&&crypto.randomUUID&&crypto.randomUUID.bind(crypto),ci={randomUUID:rh};function di(e,t,n){var r;if(ci.randomUUID&&!e)return ci.randomUUID();e=e||{};const o=e.random??((r=e.rng)==null?void 0:r.call(e))??oh();if(o.length<16)throw new Error("Random bytes length must be >= 16");return o[6]=o[6]&15|64,o[8]=o[8]&63|128,th(o)}const qe=class qe{constructor(){}static getInstance(){return qe.instance||(qe.instance=new qe),qe.instance}async generateId(){return console.log("生成任务ID:",`task-${di()}`),`task-${di()}`}};Br(qe,"instance");let lr=qe;const ah=lr.getInstance(),_o=new Set,ih=[C.DownloadingVideo,C.DownloadingAudio],sh=[C.Completed,C.ParseError,C.DownloadError,C.ParseSignError,C.DownloadSignError,C.MergeError],lh=(e,t)=>(n,o)=>{if(!_o.has(o.taskId)&&ih.includes(o.status)){_o.add(o.taskId),t().fetchTask();return}if(sh.includes(o.status)){_o.delete(o.taskId),t().fetchTask();return}e(({tasks:r})=>({tasks:r.map(a=>a.id===o.taskId?{...a,status:o.status??a.status,progress:o.percent??a.progress,speed:o.speed??a.speed,eta:o.eta??a.eta,downloadSize:o.downloadSize??a.downloadSize}:a)}))},ch=[C.Completed,C.DownloadError,C.Cancelled],dh=gs((e,t)=>({tasks:[],syncHandle:void 0,async fetchTask(){const o=(await Nf()).sort((r,a)=>a.createdAt-r.createdAt);e(()=>({tasks:o}))},async deleteTask(n){ch.includes(n.status)||await Ff(n.id),await Rf(n.id||n.videoInfo.id),await t().fetchTask()},async openTaskFolder(n){try{return await Lf(n.id)}catch(o){return hn("打开文件夹失败:"+o),o instanceof Error&&o.message.includes("ENOENT")?D.error(qr("errors.fileNotFound")):D.error(qr("errors.openFileLocationFailed")),{success:!1}}},async retryTask(n){await Ka(n.url,n.id),await t().fetchTask()},async addTask(n){const o=await ah.generateId();await Ka(n,o),await t().fetchTask()},registerSync(){const{syncHandle:n}=t();if(n)return;const o=lh(e,t);Of("download-status-change",o),e(()=>({syncHandle:o}))},clearSync(){const{syncHandle:n}=t();n&&(Mf("download-status-change",n),e(()=>({syncHandle:void 0})))}})),uh=()=>{const{t:e}=W(),[t,n]=h.useState([]),{tasks:o,fetchTask:r,registerSync:a,deleteTask:i,openTaskFolder:s,retryTask:l,addTask:c}=dh(),{loadSetting:u}=tt(),{authSites:p,addCustomSite:f,loginSite:m}=ms(),{mutateSettings:v,settings:w}=Ke(),b=h.useRef(!1),g=h.useRef(null);h.useEffect(()=>{r(),a()},[r,a]),h.useEffect(()=>{b.current||!w||(u(w),b.current=!0)},[u,w]);const T=async k=>{w&&(await c(k),await v())},P=async k=>{n([]);try{for(const N of k)try{T(N)}catch(_){console.error(`处理链接失败: ${N}`,_),D.error(_.message)}}catch(N){hn("批量下载处理失败:"+N),D.error(N.message)}},j=async k=>{try{const N=new URL(k.url),_=p.find(U=>U.authUrl===N.origin);let K=_;if(!_){const U=await f(k.url);if(!U)throw new Error;K=U[U.length-1]}await m(K),D.success(e("messages.authSuccess")),l(k)}catch{D.error(e("messages.authFailed"))}},E=async()=>{var k;try{const N=await navigator.clipboard.readText(),_=hm(N);if(_.length>0){if(_.length===1)T(_[0].href);else{const K=_.slice(0,10);n(K)}(k=g.current)==null||k.scrollTo({behavior:"smooth",top:0})}else D.error(e("errors.clipboardNotContainsValidUrl"))}catch(N){hn("读取剪贴板失败:"+N),D.error(N.message)}};return d.jsxs("div",{className:"flex flex-col w-full h-full ",children:[d.jsxs("div",{className:"h-[80px] bg-white flex items-center shrink-0 px-4",children:[d.jsx("section",{className:"flex gap-2 items-center mr-4 ",children:d.jsxs(Pe,{onClick:E,size:"sm",color:"blue",theme:{inner:{base:"flex items-center justify-center"}},children:[d.jsx(Tm,{className:"mr-2"}),d.jsxs("span",{className:"text-[16px]",children:[" ",e("download.pasteLink")]})]})}),d.jsx(Om,{})]}),d.jsx("article",{className:"grow flex flex-col p-4 overflow-auto scroll-smooth",ref:g,children:d.jsx(_m,{data:o,render:k=>d.jsx(eh,{task:k,onDelete:i,onOpen:s,onRetry:l,onAuth:j},k.id)})}),d.jsx(Mm,{links:t,open:t.length>0,onCancel:()=>n([]),onOk:P})]})},vs=[{key:"application.menu.download",icon:d.jsx(xm,{}),path:"/download",element:d.jsx(uh,{})}],ph=vs.map(({path:e,element:t})=>({path:e,element:t})).concat([{path:"/",element:d.jsx(Os,{to:"/download"})}]);function fh(){const{t:e}=W(),[t,n]=h.useState(!1),o=Ms(ph);return o?d.jsxs("div",{className:"flex h-screen w-full overflow-hidden",children:[d.jsxs("aside",{className:"flex flex-col shrink-0 bg-white gap-1 py-4",children:[d.jsx("nav",{children:vs.map(r=>r.path&&r.key&&d.jsx(kp,{icon:r.icon,to:r.path,children:e(r.key)},r.key))}),d.jsxs("footer",{className:"gap-2 grow justify-center items-end flex flex-wrap px-4 content-end",children:[Tp.map(({key:r,icon:a,link:i})=>d.jsx(Ra,{content:e(r),children:d.jsx(a,{onClick:()=>_f(i)})},r)),d.jsx(Ra,{content:e("menu.settings"),children:d.jsx(fp,{onClick:()=>n(!0)})})]})]}),d.jsx("main",{className:"bg-[#F3F4F6] grow",children:o}),d.jsx(bp,{open:t,onClose:()=>n(!1),children:d.jsx(Sm,{})})]}):null}const mh={pasteLink:"لصق الرابط",pasteLinkFromClipboard:"لصق الرابط من الحافظة",playlistChannel:"قائمة تشغيل/قناة",pastePlaylistChannelLink:"لصق رابط قائمة تشغيل أو قناة",download:"تحميل",resourceType:{video:"فيديو",audio:"صوت"},quality:"الجودة",videoQuality:{best:"الأفضل"},audioQuality:{highest:"الأعلى"},format:"الصيغة",for:"لـ",thumbnail:"صورة مصغرة",subtitles:"ترجمات",audioTracks:"مسارات صوتية",allTracks:"جميع المسارات",default:"افتراضي",none:"لا شيء",modal:{selectAll:"تحديد الكل",cancelAll:"إلغاء الكل",cancel:"إلغاء",download:"تحميل",needDownloadToSelect:"يرجى تحديد عنصر واحد على الأقل للتحميل"},emptyState:{step1:"الخطوة الأولى: انسخ رابط الفيديو",step2:"الخطوة الثانية: انقر للصق الرابط والتحميل"},popconfirm:{downloadingDeleteTitle:"আপনি কি নিশ্চিত যে আপনি এই কাজটি মুছতে চান?",deleteText:"মুছুন",openFileFailed:"ফাইল খোলা ব্যর্থ হয়েছে, আপনি কি মুছতে চান?"}},hh={retrievingInformation:"جارٍ استرداد المعلومات",downloading:"جارٍ التحميل",audioDownloading:"جارٍ تحميل الصوت",videoDownloading:"جارٍ تحميل الفيديو",subtitlesDownloading:"جارٍ تحميل الترجمات",converting:"جارٍ التحويل",merging:"جارٍ الدمج",downloadFailed:"فشل التحميل",parseFailed:"فشل التحليل",cancelled:"تم الإلغاء",preparingToDownload:"جارٍ التحضير للتحميل"},gh={retry:"إعادة المحاولة",delete:"حذف",showInFinder:"عرض في Finder",showInFolder:"عرض في المجلد",more:"المزيد",logIn:"تسجيل الدخول",timeLeft:"الوقت المتبقي",speed:"السرعة",fileSize:"حجم الملف"},yh={logIn:"تسجيل الدخول",logOut:"تسجيل الخروج",logInToX:"تسجيل الدخول إلى https://x.com",done:"تم",cancel:"إلغاء",cancelLogin:"إلغاء تسجيل الدخول",loginTo:"تسجيل الدخول إلى"},vh={copyCaption:"نسخ التعليق",copyLinkAddress:"نسخ عنوان الرابط",openInBrowser:"فتح الرابط في المتصفح",remove:"إزالة",removeAll:"إزالة الكل"},wh={connectionTimeout:"انتهت مهلة الاتصال، يرجى التحقق من اتصال الشبكة",unsupportedUrl:"الرابط غير مدعوم بعد. سيتم دعمه قريبًا",needLoginToDownload:"يجب تسجيل الدخول للتحميل",fileNotFound:"الملف غير موجود",folderNotFound:"المجلد غير موجود",openFileLocationFailed:"فشل فتح موقع الملف",clipboardNotContainsValidUrl:"الحافظة لا تحتوي على رابط صالح",retryFailed:"فشل إعادة المحاولة",checkVersionFailed:"فشل التحقق من الإصدار",notImplemented:"لم يتم تطبيقه بعد",parseError:"পার্স ত্রুটি",downloadError:"ডাউনলোড ত্রুটি, অনুগ্রহ করে নেটওয়ার্ক সংযোগ পরীক্ষা করুন",mergeError:"রূপান্তর ত্রুটি"},bh={saveSuccess:"تم الحفظ بنجاح",saveFailed:"فشل الحفظ",authSuccess:"تم التفويض بنجاح",authFailed:"فشل التفويض",removeAuthSuccess:"تمت إزالة التفويض بنجاح",removeAuthFailed:"فشل إزالة التفويض",validUrlPrompt:"يرجى إدخال رابط صالح",websiteAlreadyInList:"هذا الموقع موجود بالفعل في القائمة",defaultError:"فشل العملية"},Sh={removeAll:{removeAllItemsFromTheList:"هل تريد إزالة جميع العناصر من القائمة؟",deleteDownloadedFiles:"حذف الملفات التي تم تحميلها",remove:"إزالة",cancel:"إلغاء"},fileDeleted:{fileHasBeenDeletedOrMoved:"تم حذف الملف أو نقله. هل تريد إزالة هذا العنصر؟",remove:"إزالة",cancel:"إلغاء"},deleteDownloading:{fileIsDownloading:"الملف قيد التحميل. هل تريد حذفه؟",delete:"حذف",cancel:"إلغاء"}},Eh={website:"موقع الويب",settings:"الإعدادات"},xh={general:"عام",saveTo:"حفظ في",changeFolderBrowser:"تغيير المجلد",language:"اللغة",system:"النظام",createSubdirectoriesForDownloadedPlaylistsAndChannels:"إنشاء مجلدات فرعية لقوائم التشغيل والقنوات التي تم تحميلها",numerateFilesInPlaylistsAndChannels:"ترقيم الملفات في قوائم التشغيل والقنوات",embedSubtitlesInVideoFile:"تضمين الترجمات في ملف الفيديو",authorization:"التفويض",logOut:"تسجيل الخروج",logIn:"تسجيل الدخول",delete:"حذف",addUrl:"إضافة",enterTheWebsiteUrl:"أدخل رابط الموقع",authorizationPanelTips:"تسجيل الدخول إلى الموقع يسمح بتحميل محتوى مقيد بالعمر، ومحتوى العضوية الذي اشتريته، ومحتوى خاص آخر.",proxy:"بروكسي",proxyType:"نوع البروكسي",httpProxy:"بروكسي HTTP",socks5Proxy:"بروكسي SOCKS5",usingSystemProxy:"استخدام بروكسي النظام",notUsingProxy:"عدم استخدام بروكسي",host:"المضيف",port:"المنفذ",proxyInfoMessage:{pleaseEnterProxyHost:"يرجى إدخال عنوان مضيف البروكسي",pleaseEnterValidProxyHost:"يرجى إدخال عنوان مضيف صالح",pleaseEnterProxyPort:"يرجى إدخال منفذ البروكسي",pleaseEnterValidProxyPort:"يرجى إدخال رقم منفذ صالح (1-65535)",optional:"اختياري"},login:"اسم المستخدم",password:"كلمة المرور",save:"حفظ",about:"حول",version:"الإصدار",latestVersion:"أحدث إصدار",upgrade:"ترقية",message:{loadSettingsFailed:"فشل تحميل الإعدادات"},checkVersion:"التحقق من الإصدار",latestVersionAvailable:"تم العثور على أحدث إصدار",latestVersionNotAvailable:"لديك بالفعل أحدث إصدار"},Th={newVersionAvailable:"إصدار جديد متاح",whatsNew:"ما الجديد",upgradeNow:"ترقية الآن",downloading:"جارٍ التحميل...",remindAfterDownload:"ذكرني بعد التحميل",newVersionReady:"الإصدار الجديد جاهز",installNow:"تثبيت الآن",remindLater:"ذكرني لاحقًا"},kh={download:"تحميل",online:"متصل",convert:"تحويل",audioVideoMerger:"دمج الصوت والفيديو",joinTelegramGroup:"انضم إلى مجموعة تيليغرام",joinDiscordCommunity:"انضم إلى مجتمع ديسكورد"},_h={menu:{download:"تحميل الروابط",network:"اكتشاف الشبكة",format:"تحويل الصيغة",merge:"دمج الصوت والفيديو"},loading:"جارٍ التحميل..."},Ah={cancel:"বাতিল",ok:"ঠিক আছে"},Ch={download:mh,taskStatus:hh,taskActions:gh,auth:yh,contextMenu:vh,errors:wh,messages:bh,dialogs:Sh,menu:Eh,settings:xh,update:Th,mainMenu:kh,application:_h,common:Ah},Ih={pasteLink:"Link einfügen",pasteLinkFromClipboard:"Link aus der Zwischenablage einfügen",playlistChannel:"Wiedergabeliste/Kanal",pastePlaylistChannelLink:"Wiedergabelisten-/Kanal-Link einfügen",download:"Herunterladen",resourceType:{video:"Video",audio:"Audio"},quality:"Qualität",videoQuality:{best:"Beste"},audioQuality:{highest:"Höchste"},format:"Format",for:"Für",thumbnail:"Vorschaubild",subtitles:"Untertitel",audioTracks:"Audiospuren",allTracks:"Alle Spuren",default:"Standard",none:"Keine",modal:{selectAll:"Alles auswählen",cancelAll:"Alles abbrechen",cancel:"Abbrechen",download:"Herunterladen",needDownloadToSelect:"Bitte wählen Sie mindestens ein Element zum Herunterladen aus"},emptyState:{step1:"Schritt 1: Kopieren Sie die Video-URL",step2:"Schritt 2: Klicken Sie, um den Link einzufügen und herunterzuladen"},popconfirm:{downloadingDeleteTitle:"Sind Sie sicher, dass Sie diese Aufgabe löschen möchten?",deleteText:"Löschen",openFileFailed:"Öffnen der Datei fehlgeschlagen, möchten Sie sie löschen?"}},Ph={retrievingInformation:"Informationen werden abgerufen",downloading:"Wird heruntergeladen",audioDownloading:"Audio wird heruntergeladen",videoDownloading:"Video wird heruntergeladen",subtitlesDownloading:"Untertitel werden heruntergeladen",converting:"Wird konvertiert",merging:"Wird zusammengeführt",downloadFailed:"Download fehlgeschlagen",parseFailed:"Analyse fehlgeschlagen",cancelled:"Abgebrochen",preparingToDownload:"Download wird vorbereitet"},Dh={retry:"Wiederholen",delete:"Löschen",showInFinder:"Im Finder anzeigen",showInFolder:"Im Ordner anzeigen",more:"Mehr",logIn:"Anmelden",timeLeft:"Verbleibende Zeit",speed:"Geschwindigkeit",fileSize:"Dateigröße"},Lh={logIn:"Anmelden",logOut:"Abmelden",logInToX:"Bei https://x.com anmelden",done:"Fertig",cancel:"Abbrechen",cancelLogin:"Anmeldung abbrechen",loginTo:"Anmelden bei"},Fh={copyCaption:"Beschriftung kopieren",copyLinkAddress:"Link-Adresse kopieren",openInBrowser:"Im Browser öffnen",remove:"Entfernen",removeAll:"Alle entfernen"},Rh={connectionTimeout:"Zeitüberschreitung der Verbindung, bitte Netzwerkverbindung überprüfen",unsupportedUrl:"URL wird noch nicht unterstützt. Bald verfügbar",needLoginToDownload:"Anmeldung zum Herunterladen erforderlich",fileNotFound:"Datei nicht gefunden",folderNotFound:"Ordner nicht gefunden",openFileLocationFailed:"Öffnen des Dateispeicherorts fehlgeschlagen",clipboardNotContainsValidUrl:"Zwischenablage enthält keine gültige URL",retryFailed:"Wiederholung fehlgeschlagen",checkVersionFailed:"Überprüfung der Version fehlgeschlagen",notImplemented:"Noch nicht implementiert",parseError:"Analysefehler",downloadError:"Download-Fehler, bitte überprüfen Sie die Netzwerkverbindung",mergeError:"Konvertierungsfehler"},Nh={saveSuccess:"Speichern erfolgreich",saveFailed:"Speichern fehlgeschlagen",authSuccess:"Autorisierung erfolgreich",authFailed:"Autorisierung fehlgeschlagen",removeAuthSuccess:"Autorisierung erfolgreich entfernt",removeAuthFailed:"Entfernen der Autorisierung fehlgeschlagen",validUrlPrompt:"Bitte geben Sie eine gültige URL ein",websiteAlreadyInList:"Diese Website ist bereits in der Liste",defaultError:"Operation fehlgeschlagen"},Oh={removeAll:{removeAllItemsFromTheList:"Alle Elemente aus der Liste entfernen?",deleteDownloadedFiles:"Heruntergeladene Dateien löschen",remove:"Entfernen",cancel:"Abbrechen"},fileDeleted:{fileHasBeenDeletedOrMoved:"Die Datei wurde gelöscht oder verschoben. Diesen Eintrag entfernen?",remove:"Entfernen",cancel:"Abbrechen"},deleteDownloading:{fileIsDownloading:"Die Datei wird heruntergeladen. Löschen?",delete:"Löschen",cancel:"Abbrechen"}},Mh={website:"Website",settings:"Einstellungen"},jh={general:"Allgemein",saveTo:"Speichern unter",changeFolderBrowser:"Ordner ändern",language:"Sprache",system:"System",createSubdirectoriesForDownloadedPlaylistsAndChannels:"Unterverzeichnisse für heruntergeladene Wiedergabelisten und Kanäle erstellen",numerateFilesInPlaylistsAndChannels:"Dateien in Wiedergabelisten und Kanälen nummerieren",embedSubtitlesInVideoFile:"Untertitel in Videodatei einbetten",authorization:"Autorisierung",logOut:"Abmelden",logIn:"Anmelden",delete:"Löschen",addUrl:"Hinzufügen",enterTheWebsiteUrl:"Website-URL eingeben",authorizationPanelTips:"Die Anmeldung auf der Website ermöglicht das Herunterladen von altersbeschränkten Inhalten, gekauften Mitgliedschaftsinhalten und anderen privaten Inhalten.",proxy:"Proxy",proxyType:"Proxy-Typ",httpProxy:"HTTP-Proxy",socks5Proxy:"SOCKS5-Proxy",usingSystemProxy:"System-Proxy verwenden",notUsingProxy:"Kein Proxy verwenden",host:"Host",port:"Port",proxyInfoMessage:{pleaseEnterProxyHost:"Bitte geben Sie die Proxy-Host-Adresse ein",pleaseEnterValidProxyHost:"Bitte geben Sie eine gültige Host-Adresse ein",pleaseEnterProxyPort:"Bitte geben Sie den Proxy-Port ein",pleaseEnterValidProxyPort:"Bitte geben Sie eine gültige Portnummer ein (1-65535)",optional:"Optional"},login:"Benutzername",password:"Passwort",save:"Speichern",about:"Über",version:"Version",latestVersion:"Neueste Version",upgrade:"Aktualisieren",message:{loadSettingsFailed:"Laden der Einstellungen fehlgeschlagen"},checkVersion:"Version überprüfen",latestVersionAvailable:"Neueste Version gefunden",latestVersionNotAvailable:"Bereits die neueste Version"},Uh={newVersionAvailable:"Neue Version verfügbar",whatsNew:"Was ist neu",upgradeNow:"Jetzt aktualisieren",downloading:"Wird heruntergeladen...",remindAfterDownload:"Nach dem Download erinnern",newVersionReady:"Neue Version ist bereit",installNow:"Jetzt installieren",remindLater:"Später erinnern"},$h={download:"Download",online:"Online",convert:"Konvertieren",audioVideoMerger:"Audio-Video-Zusammenführung",joinTelegramGroup:"Telegram-Gruppe beitreten",joinDiscordCommunity:"Discord-Community beitreten"},Vh={menu:{download:"Link-Download",network:"Netzwerküberwachung",format:"Formatkonvertierung",merge:"Audio- und Video-Zusammenführung"},loading:"Laden..."},Hh={cancel:"Abbrechen",ok:"OK"},zh={download:Ih,taskStatus:Ph,taskActions:Dh,auth:Lh,contextMenu:Fh,errors:Rh,messages:Nh,dialogs:Oh,menu:Mh,settings:jh,update:Uh,mainMenu:$h,application:Vh,common:Hh},Bh={pasteLink:"Paste Link",pasteLinkFromClipboard:"Paste Link From Clipboard",playlistChannel:"Playlist/Channel",pastePlaylistChannelLink:"Paste Playlist/Channel Link",download:"Download",resourceType:{video:"Video",audio:"Audio"},quality:"Quality",videoQuality:{best:"Best"},audioQuality:{highest:"Highest"},format:"Format",for:"For",thumbnail:"Thumbnail",subtitles:"Subtitles",audioTracks:"Audio tracks",allTracks:"All Tracks",default:"Default",none:"None",modal:{selectAll:"Select All",cancelAll:"Cancel All",cancel:"Cancel",download:"Download",needDownloadToSelect:"Please select at least one item to download"},emptyState:{step1:"Step 1: Copy video URL",step2:"Step 2: Click to paste link and download"},popconfirm:{downloadingDeleteTitle:"Are you sure you want to delete this task?",deleteText:"Delete",openFileFailed:"Open file failed, do you want to delete?"}},Kh={retrievingInformation:"Retrieving information",downloading:"Downloading",audioDownloading:"Audio Downloading",videoDownloading:"Video Downloading",subtitlesDownloading:"Subtitles Downloading",converting:"Converting",merging:"Merging",downloadFailed:"Download Failed",parseFailed:"Parse Failed",cancelled:"Cancelled",preparingToDownload:"Preparing to download"},Gh={retry:"Retry",delete:"Delete",showInFinder:"Show in Finder",showInFolder:"Show in Folder",more:"More",logIn:"Log in",timeLeft:"Time left",speed:"Speed",fileSize:"File size"},qh={logIn:"Log in",logOut:"Log out",logInToX:"Log in to https://x.com",done:"Done",cancel:"Cancel",cancelLogin:"Cancel Login",loginTo:"Login to"},Wh={copyCaption:"Copy Caption",copyLinkAddress:"Copy Link Address",openInBrowser:"Open Link in Browser",remove:"Remove",removeAll:"Remove All"},Yh={connectionTimeout:"Connection timed out, please check the network connection",unsupportedUrl:"URL not supported yet. Coming soon",needLoginToDownload:"Need to log in to download",fileNotFound:"File not found",folderNotFound:"Folder not found",openFileLocationFailed:"Open File Location Failed",clipboardNotContainsValidUrl:"Clipboard does not contain a valid URL",retryFailed:"Retry failed",checkVersionFailed:"Check Version Failed",notImplemented:"Not implemented yet",parseError:"Parse Error",downloadError:"Download Error, please check the network connection",mergeError:"Convert Error"},Qh={saveSuccess:"Save Success",saveFailed:"Save Failed",authSuccess:"Authorization Success",authFailed:"Authorization Failed",removeAuthSuccess:"Remove Authorization Success",removeAuthFailed:"Remove Authorization Failed",validUrlPrompt:"Please enter a valid URL",websiteAlreadyInList:"This website is already in the list",defaultError:"Operation Failed"},Xh={removeAll:{removeAllItemsFromTheList:"Remove all items from the list?",deleteDownloadedFiles:"Delete downloaded files",remove:"Remove",cancel:"Cancel"},fileDeleted:{fileHasBeenDeletedOrMoved:"The file has been deleted or moved. Remove this item?",remove:"Remove",cancel:"Cancel"},deleteDownloading:{fileIsDownloading:"The file is downloading. Delete it？",delete:"Delete",cancel:"Cancel"}},Jh={website:"Website",settings:"Settings"},Zh={general:"General",saveTo:"Save to",changeFolderBrowser:"Change Folder",language:"Language",system:"System",createSubdirectoriesForDownloadedPlaylistsAndChannels:"Create subdirectories for downloaded playlists and channels",numerateFilesInPlaylistsAndChannels:"Numerate files in playlists and channels",embedSubtitlesInVideoFile:"Embed subtitles in video file",authorization:"Authorization",logOut:"Log out",logIn:"Log in",delete:"Delete",addUrl:"Add",enterTheWebsiteUrl:"Enter the website URL",authorizationPanelTips:"Logging in to the website allows for downloading of age-restricted content, membership content you have purchased, and other private content.",proxy:"Proxy",proxyType:"Proxy Type",httpProxy:"HTTP Proxy",socks5Proxy:"SOCKS5 Proxy",usingSystemProxy:"Using System Proxy",notUsingProxy:"Not Using Proxy",host:"Host",port:"Port",proxyInfoMessage:{pleaseEnterProxyHost:"Please enter the proxy host address",pleaseEnterValidProxyHost:"Please enter a valid host address",pleaseEnterProxyPort:"Please enter the proxy port",pleaseEnterValidProxyPort:"Please enter a valid port number (1-65535)",optional:"Optional"},login:"Login",password:"Password",save:"Save",about:"About",version:"Version",latestVersion:"Latest Version",upgrade:"Upgrade",message:{loadSettingsFailed:"Load settings failed"},checkVersion:"Check Version",latestVersionAvailable:"Found latest version",latestVersionNotAvailable:"Already the latest version"},eg={newVersionAvailable:"New version available",whatsNew:"What's New",upgradeNow:"Upgrade Now",downloading:"Downloading...",remindAfterDownload:"Remind Me After Download",newVersionReady:"New version is ready",installNow:"Install Now",remindLater:"Remind Me Later"},tg={download:"Download",online:"Online",convert:"Convert",audioVideoMerger:"Audio Video Merger",joinTelegramGroup:"Join Telegram Group",joinDiscordCommunity:"Join Discord Community"},ng={menu:{download:"Link Download",network:"Network Sniffing",format:"Format Conversion",merge:"Audio and Video Merge"},loading:"loading..."},og={cancel:"Cancel",ok:"Ok"},rg={download:Bh,taskStatus:Kh,taskActions:Gh,auth:qh,contextMenu:Wh,errors:Yh,messages:Qh,dialogs:Xh,menu:Jh,settings:Zh,update:eg,mainMenu:tg,application:ng,common:og},ag={pasteLink:"Pegar enlace",pasteLinkFromClipboard:"Pegar enlace desde el portapapeles",playlistChannel:"Lista de reproducción/Canal",pastePlaylistChannelLink:"Pegar enlace de lista de reproducción o canal",download:"Descargar",resourceType:{video:"Vídeo",audio:"Audio"},quality:"Calidad",videoQuality:{best:"Mejor"},audioQuality:{highest:"Máxima"},format:"Formato",for:"Para",thumbnail:"Miniatura",subtitles:"Subtítulos",audioTracks:"Pistas de audio",allTracks:"Todas las pistas",default:"Predeterminado",none:"Ninguno",modal:{selectAll:"Seleccionar todo",cancelAll:"Cancelar todo",cancel:"Cancelar",download:"Descargar",needDownloadToSelect:"Por favor, selecciona al menos un elemento para descargar"},emptyState:{step1:"Paso 1: Copia la URL del video",step2:"Paso 2: Haz clic para pegar el enlace y descargar"},popconfirm:{downloadingDeleteTitle:"¿Estás seguro de que quieres eliminar esta tarea?",deleteText:"Eliminar",openFileFailed:"Error al abrir el archivo, ¿quieres eliminarlo?"}},ig={retrievingInformation:"Recuperando información",downloading:"Descargando",audioDownloading:"Descargando audio",videoDownloading:"Descargando vídeo",subtitlesDownloading:"Descargando subtítulos",converting:"Convirtiendo",merging:"Fusionando",downloadFailed:"Error en la descarga",parseFailed:"Error en el análisis",cancelled:"Cancelado",preparingToDownload:"Preparando la descarga"},sg={retry:"Reintentar",delete:"Eliminar",showInFinder:"Mostrar en Finder",showInFolder:"Mostrar en carpeta",more:"Más",logIn:"Iniciar sesión",timeLeft:"Tiempo restante",speed:"Velocidad",fileSize:"Tamaño del archivo"},lg={logIn:"Iniciar sesión",logOut:"Cerrar sesión",logInToX:"Iniciar sesión en https://x.com",done:"Listo",cancel:"Cancelar",cancelLogin:"Cancelar inicio de sesión",loginTo:"Iniciar sesión en"},cg={copyCaption:"Copiar subtítulo",copyLinkAddress:"Copiar dirección del enlace",openInBrowser:"Abrir enlace en el navegador",remove:"Eliminar",removeAll:"Eliminar todo"},dg={connectionTimeout:"Se agotó el tiempo de conexión, por favor verifica tu conexión a internet",unsupportedUrl:"URL no compatible por ahora. Próximamente disponible",needLoginToDownload:"Necesitas iniciar sesión para descargar",fileNotFound:"Archivo no encontrado",folderNotFound:"Carpeta no encontrada",openFileLocationFailed:"Error al abrir la ubicación del archivo",clipboardNotContainsValidUrl:"El portapapeles no contiene una URL válida",retryFailed:"Error al reintentar",checkVersionFailed:"Error al verificar la versión",notImplemented:"Aún no implementado",parseError:"Error de análisis",downloadError:"Error de descarga, por favor verifica la conexión de red",mergeError:"Error de conversión"},ug={saveSuccess:"Guardado con éxito",saveFailed:"Error al guardar",authSuccess:"Autorización exitosa",authFailed:"Error en la autorización",removeAuthSuccess:"Autorización eliminada con éxito",removeAuthFailed:"Error al eliminar la autorización",validUrlPrompt:"Por favor, introduce una URL válida",websiteAlreadyInList:"Este sitio web ya está en la lista",defaultError:"Error"},pg={removeAll:{removeAllItemsFromTheList:"¿Eliminar todos los elementos de la lista?",deleteDownloadedFiles:"Eliminar archivos descargados",remove:"Eliminar",cancel:"Cancelar"},fileDeleted:{fileHasBeenDeletedOrMoved:"El archivo ha sido eliminado o movido. ¿Eliminar este elemento?",remove:"Eliminar",cancel:"Cancelar"},deleteDownloading:{fileIsDownloading:"El archivo se está descargando. ¿Eliminarlo?",delete:"Eliminar",cancel:"Cancelar"}},fg={website:"Sitio web",settings:"Ajustes"},mg={general:"General",saveTo:"Guardar en",changeFolderBrowser:"Cambiar carpeta",language:"Idioma",system:"Sistema",createSubdirectoriesForDownloadedPlaylistsAndChannels:"Crear subcarpetas para listas de reproducción y canales descargados",numerateFilesInPlaylistsAndChannels:"Numerar archivos en listas de reproducción y canales",embedSubtitlesInVideoFile:"Incrustar subtítulos en el archivo de vídeo",authorization:"Autorización",logOut:"Cerrar sesión",logIn:"Iniciar sesión",delete:"Eliminar",addUrl:"Añadir",enterTheWebsiteUrl:"Introduce la URL del sitio web",authorizationPanelTips:"Iniciar sesión en el sitio web permite descargar contenido restringido por edad, contenido de suscripción que hayas adquirido y otro contenido privado.",proxy:"Proxy",proxyType:"Tipo de proxy",httpProxy:"Proxy HTTP",socks5Proxy:"Proxy SOCKS5",usingSystemProxy:"Usando el proxy del sistema",notUsingProxy:"Sin usar proxy",host:"Servidor",port:"Puerto",proxyInfoMessage:{pleaseEnterProxyHost:"Por favor, introduce la dirección del servidor proxy",pleaseEnterValidProxyHost:"Por favor, introduce una dirección de servidor válida",pleaseEnterProxyPort:"Por favor, introduce el puerto del proxy",pleaseEnterValidProxyPort:"Por favor, introduce un número de puerto válido (1-65535)",optional:"Opcional"},login:"Usuario",password:"Contraseña",save:"Guardar",about:"Acerca de",version:"Versión",latestVersion:"Última versión",upgrade:"Actualizar",message:{loadSettingsFailed:"Error al cargar los ajustes"},checkVersion:"Verificar versión",latestVersionAvailable:"Se encontró la última versión",latestVersionNotAvailable:"Ya tienes la última versión"},hg={newVersionAvailable:"Nueva versión disponible",whatsNew:"¿Qué hay de nuevo?",upgradeNow:"Actualizar ahora",downloading:"Descargando...",remindAfterDownload:"Recordarme después de la descarga",newVersionReady:"La nueva versión está lista",installNow:"Instalar ahora",remindLater:"Recordarme más tarde"},gg={download:"Descargar",online:"En línea",convert:"Convertir",audioVideoMerger:"Fusión de audio y vídeo",joinTelegramGroup:"Unirse al grupo de Telegram",joinDiscordCommunity:"Unirse a la comunidad de Discord"},yg={menu:{download:"Descarga de enlaces",network:"Detección de red",format:"Conversión de formato",merge:"Fusión de audio y vídeo"},loading:"cargando..."},vg={cancel:"Cancelar",ok:"Aceptar"},wg={download:ag,taskStatus:ig,taskActions:sg,auth:lg,contextMenu:cg,errors:dg,messages:ug,dialogs:pg,menu:fg,settings:mg,update:hg,mainMenu:gg,application:yg,common:vg},bg={pasteLink:"Coller le lien",pasteLinkFromClipboard:"Coller le lien depuis le presse-papiers",playlistChannel:"Playlist/Chaîne",pastePlaylistChannelLink:"Coller le lien de la playlist/chaîne",download:"Télécharger",resourceType:{video:"Vidéo",audio:"Audio"},quality:"Qualité",videoQuality:{best:"Meilleure"},audioQuality:{highest:"Maximale"},format:"Format",for:"Pour",thumbnail:"Miniature",subtitles:"Sous-titres",audioTracks:"Pistes audio",allTracks:"Toutes les pistes",default:"Par défaut",none:"Aucune",modal:{selectAll:"Tout sélectionner",cancelAll:"Tout annuler",cancel:"Annuler",download:"Télécharger",needDownloadToSelect:"Veuillez sélectionner au moins un élément à télécharger"},emptyState:{step1:"Étape 1 : Copiez l'URL de la vidéo",step2:"Étape 2 : Cliquez pour coller le lien et télécharger"},popconfirm:{downloadingDeleteTitle:"Êtes-vous sûr de vouloir supprimer cette tâche ?",deleteText:"Supprimer",openFileFailed:"Échec de l'ouverture du fichier, voulez-vous le supprimer ?"}},Sg={retrievingInformation:"Récupération des informations",downloading:"Téléchargement en cours",audioDownloading:"Téléchargement de l'audio en cours",videoDownloading:"Téléchargement de la vidéo en cours",subtitlesDownloading:"Téléchargement des sous-titres en cours",converting:"Conversion en cours",merging:"Fusion en cours",downloadFailed:"Échec du téléchargement",parseFailed:"Échec de l'analyse",cancelled:"Annulé",preparingToDownload:"Préparation du téléchargement"},Eg={retry:"Réessayer",delete:"Supprimer",showInFinder:"Afficher dans le Finder",showInFolder:"Afficher dans le dossier",more:"Plus",logIn:"Se connecter",timeLeft:"Temps restant",speed:"Vitesse",fileSize:"Taille du fichier"},xg={logIn:"Se connecter",logOut:"Se déconnecter",logInToX:"Se connecter à https://x.com",done:"Terminé",cancel:"Annuler",cancelLogin:"Annuler la connexion",loginTo:"Se connecter à"},Tg={copyCaption:"Copier la légende",copyLinkAddress:"Copier l'adresse du lien",openInBrowser:"Ouvrir le lien dans le navigateur",remove:"Supprimer",removeAll:"Tout supprimer"},kg={connectionTimeout:"Délai de connexion dépassé, veuillez vérifier votre connexion réseau",unsupportedUrl:"URL non encore prise en charge. Bientôt disponible",needLoginToDownload:"Connexion requise pour télécharger",fileNotFound:"Fichier non trouvé",folderNotFound:"Dossier non trouvé",openFileLocationFailed:"Échec de l'ouverture de l'emplacement du fichier",clipboardNotContainsValidUrl:"Le presse-papiers ne contient pas d'URL valide",retryFailed:"Échec de la nouvelle tentative",checkVersionFailed:"Échec de la vérification de la version",notImplemented:"Pas encore implémenté",parseError:"Erreur d'analyse",downloadError:"Erreur de téléchargement, veuillez vérifier la connexion réseau",mergeError:"Erreur de conversion"},_g={saveSuccess:"Enregistrement réussi",saveFailed:"Échec de l'enregistrement",authSuccess:"Autorisation réussie",authFailed:"Échec de l'autorisation",removeAuthSuccess:"Suppression de l'autorisation réussie",removeAuthFailed:"Échec de la suppression de l'autorisation",validUrlPrompt:"Veuillez entrer une URL valide",websiteAlreadyInList:"Ce site web est déjà dans la liste",defaultError:"Opération échouée"},Ag={removeAll:{removeAllItemsFromTheList:"Supprimer tous les éléments de la liste ?",deleteDownloadedFiles:"Supprimer les fichiers téléchargés",remove:"Supprimer",cancel:"Annuler"},fileDeleted:{fileHasBeenDeletedOrMoved:"Le fichier a été supprimé ou déplacé. Supprimer cet élément ?",remove:"Supprimer",cancel:"Annuler"},deleteDownloading:{fileIsDownloading:"Le fichier est en cours de téléchargement. Le supprimer ?",delete:"Supprimer",cancel:"Annuler"}},Cg={website:"Site web",settings:"Paramètres"},Ig={general:"Général",saveTo:"Enregistrer dans",changeFolderBrowser:"Changer de dossier",language:"Langue",system:"Système",createSubdirectoriesForDownloadedPlaylistsAndChannels:"Créer des sous-dossiers pour les playlists et chaînes téléchargées",numerateFilesInPlaylistsAndChannels:"Numéroter les fichiers dans les playlists et chaînes",embedSubtitlesInVideoFile:"Intégrer les sous-titres dans le fichier vidéo",authorization:"Autorisation",logOut:"Se déconnecter",logIn:"Se connecter",delete:"Supprimer",addUrl:"Ajouter",enterTheWebsiteUrl:"Entrer l'URL du site web",authorizationPanelTips:"La connexion au site web permet de télécharger du contenu restreint par âge, du contenu d'abonnement que vous avez acheté et d'autres contenus privés.",proxy:"Proxy",proxyType:"Type de proxy",httpProxy:"Proxy HTTP",socks5Proxy:"Proxy SOCKS5",usingSystemProxy:"Utilisation du proxy système",notUsingProxy:"Sans utiliser de proxy",host:"Hôte",port:"Port",proxyInfoMessage:{pleaseEnterProxyHost:"Veuillez entrer l'adresse de l'hôte proxy",pleaseEnterValidProxyHost:"Veuillez entrer une adresse d'hôte valide",pleaseEnterProxyPort:"Veuillez entrer le port du proxy",pleaseEnterValidProxyPort:"Veuillez entrer un numéro de port valide (1-65535)",optional:"Optionnel"},login:"Identifiant",password:"Mot de passe",save:"Enregistrer",about:"À propos",version:"Version",latestVersion:"Dernière version",upgrade:"Mettre à jour",message:{loadSettingsFailed:"Échec du chargement des paramètres"},checkVersion:"Vérifier la version",latestVersionAvailable:"Nouvelle version trouvée",latestVersionNotAvailable:"Vous avez déjà la dernière version"},Pg={newVersionAvailable:"Nouvelle version disponible",whatsNew:"Quoi de neuf",upgradeNow:"Mettre à jour maintenant",downloading:"Téléchargement en cours...",remindAfterDownload:"Me rappeler après le téléchargement",newVersionReady:"La nouvelle version est prête",installNow:"Installer maintenant",remindLater:"Me rappeler plus tard"},Dg={download:"Télécharger",online:"En ligne",convert:"Convertir",audioVideoMerger:"Fusion Audio Vidéo",joinTelegramGroup:"Rejoindre le groupe Telegram",joinDiscordCommunity:"Rejoindre la communauté Discord"},Lg={menu:{download:"Téléchargement de liens",network:"Analyse réseau",format:"Conversion de format",merge:"Fusion audio et vidéo"},loading:"Chargement..."},Fg={cancel:"Annuler",ok:"OK"},Rg={download:bg,taskStatus:Sg,taskActions:Eg,auth:xg,contextMenu:Tg,errors:kg,messages:_g,dialogs:Ag,menu:Cg,settings:Ig,update:Pg,mainMenu:Dg,application:Lg,common:Fg},Ng={pasteLink:"लिंक पेस्ट करें",pasteLinkFromClipboard:"क्लिपबोर्ड से लिंक पेस्ट करें",playlistChannel:"प्लेलिस्ट/चैनल",pastePlaylistChannelLink:"प्लेलिस्ट/चैनल लिंक पेस्ट करें",download:"डाउनलोड",resourceType:{video:"वीडियो",audio:"ऑडियो"},quality:"गुणवत्ता",videoQuality:{best:"सर्वश्रेष्ठ"},audioQuality:{highest:"सर्वोच्च"},format:"प्रारूप",for:"के लिए",thumbnail:"थंबनेल",subtitles:"उपशीर्षक",audioTracks:"ऑडियो ट्रैक",allTracks:"सभी ट्रैक",default:"डिफ़ॉल्ट",none:"कोई नहीं",modal:{selectAll:"सभी का चयन करें",cancelAll:"सभी रद्द करें",cancel:"रद्द करें",download:"डाउनलोड",needDownloadToSelect:"कृपया डाउनलोड करने के लिए कम से कम एक आइटम का चयन करें"},emptyState:{step1:"चरण 1: वीडियो URL कॉपी करें",step2:"चरण 2: लिंक पेस्ट करने और डाउनलोड करने के लिए क्लिक करें"},popconfirm:{downloadingDeleteTitle:"क्या आप वाकई इस कार्य को हटाना चाहते हैं?",deleteText:"हटाएं",openFileFailed:"फ़ाइल खोलना विफल रहा, क्या आप इसे हटाना चाहते हैं?"}},Og={retrievingInformation:"जानकारी प्राप्त की जा रही है",downloading:"डाउनलोड हो रहा है",audioDownloading:"ऑडियो डाउनलोड हो रहा है",videoDownloading:"वीडियो डाउनलोड हो रहा है",subtitlesDownloading:"उपशीर्षक डाउनलोड हो रहे हैं",converting:"कन्वर्ट हो रहा है",merging:"मर्ज हो रहा है",downloadFailed:"डाउनलोड विफल हुआ",parseFailed:"पार्सिंग विफल हुई",cancelled:"रद्द कर दिया गया",preparingToDownload:"डाउनलोड की तैयारी हो रही है"},Mg={retry:"पुनः प्रयास करें",delete:"हटाएं",showInFinder:"Finder में दिखाएं",showInFolder:"फोल्डर में दिखाएं",more:"अधिक",logIn:"लॉग इन करें",timeLeft:"शेष समय",speed:"गति",fileSize:"फ़ाइल का आकार"},jg={logIn:"लॉग इन करें",logOut:"लॉग आउट करें",logInToX:"https://x.com पर लॉग इन करें",done:"पूर्ण",cancel:"रद्द करें",cancelLogin:"लॉगिन रद्द करें",loginTo:"यहाँ लॉग इन करें"},Ug={copyCaption:"कैप्शन कॉपी करें",copyLinkAddress:"लिंक पता कॉपी करें",openInBrowser:"ब्राउज़र में खोलें",remove:"हटाएं",removeAll:"सभी हटाएं"},$g={connectionTimeout:"कनेक्शन का समय समाप्त हो गया, कृपया नेटवर्क कनेक्शन जांचें",unsupportedUrl:"URL अभी समर्थित नहीं है। जल्द ही उपलब्ध होगा",needLoginToDownload:"डाउनलोड करने के लिए लॉग इन करना आवश्यक है",fileNotFound:"फ़ाइल नहीं मिली",folderNotFound:"फोल्डर नहीं मिला",openFileLocationFailed:"फ़ाइल स्थान खोलने में विफल",clipboardNotContainsValidUrl:"क्लिपबोर्ड में कोई वैध URL नहीं है",retryFailed:"पुनः प्रयास विफल हुआ",checkVersionFailed:"संस्करण जांच विफल हुई",notImplemented:"अभी लागू नहीं किया गया",parseError:"Erreur d'analyse",downloadError:"Erreur de téléchargement, veuillez vérifier la connexion réseau",mergeError:"Erreur de conversion"},Vg={saveSuccess:"सहेजना सफल रहा",saveFailed:"सहेजना विफल हुआ",authSuccess:"प्रमाणीकरण सफल रहा",authFailed:"प्रमाणीकरण विफल हुआ",removeAuthSuccess:"प्रमाणीकरण हटाना सफल रहा",removeAuthFailed:"प्रमाणीकरण हटाना विफल हुआ",validUrlPrompt:"कृपया एक वैध URL दर्ज करें",websiteAlreadyInList:"यह वेबसाइट पहले से ही सूची में है",defaultError:"कार्य विफल हुआ"},Hg={removeAll:{removeAllItemsFromTheList:"सूची से सभी आइटम हटाएं?",deleteDownloadedFiles:"डाउनलोड की गई फ़ाइलें हटाएं",remove:"हटाएं",cancel:"रद्द करें"},fileDeleted:{fileHasBeenDeletedOrMoved:"फ़ाइल हटा दी गई है या स्थानांतरित कर दी गई है। इस आइटम को हटाएं?",remove:"हटाएं",cancel:"रद्द करें"},deleteDownloading:{fileIsDownloading:"फ़ाइल डाउनलोड हो रही है। इसे हटाएं?",delete:"हटाएं",cancel:"रद्द करें"}},zg={website:"वेबसाइट",settings:"सेटिंग्स"},Bg={general:"सामान्य",saveTo:"यहाँ सहेजें",changeFolderBrowser:"फोल्डर बदलें",language:"भाषा",system:"सिस्टम",createSubdirectoriesForDownloadedPlaylistsAndChannels:"डाउनलोड की गई प्लेलिस्ट और चैनलों के लिए सब-फोल्डर बनाएं",numerateFilesInPlaylistsAndChannels:"प्लेलिस्ट और चैनलों में फ़ाइलों को क्रमांकित करें",embedSubtitlesInVideoFile:"वीडियो फ़ाइल में उपशीर्षक जोड़ें",authorization:"प्रमाणीकरण",logOut:"लॉग आउट करें",logIn:"लॉग इन करें",delete:"हटाएं",addUrl:"जोड़ें",enterTheWebsiteUrl:"वेबसाइट URL दर्ज करें",authorizationPanelTips:"वेबसाइट पर लॉग इन करने से आयु-प्रतिबंधित सामग्री, आपके द्वारा खरीदी गई सदस्यता सामग्री और अन्य निजी सामग्री डाउनलोड करने की अनुमति मिलती है।",proxy:"प्रॉक्सी",proxyType:"प्रॉक्सी प्रकार",httpProxy:"HTTP प्रॉक्सी",socks5Proxy:"SOCKS5 प्रॉक्सी",usingSystemProxy:"सिस्टम प्रॉक्सी का उपयोग कर रहा है",notUsingProxy:"प्रॉक्सी का उपयोग नहीं कर रहा है",host:"होस्ट",port:"पोर्ट",proxyInfoMessage:{pleaseEnterProxyHost:"कृपया प्रॉक्सी होस्ट पता दर्ज करें",pleaseEnterValidProxyHost:"कृपया एक वैध होस्ट पता दर्ज करें",pleaseEnterProxyPort:"कृपया प्रॉक्सी पोर्ट दर्ज करें",pleaseEnterValidProxyPort:"कृपया एक वैध पोर्ट नंबर दर्ज करें (1-65535)",optional:"वैकल्पिक"},login:"लॉगिन",password:"पासवर्ड",save:"सहेजें",about:"बारे में",version:"संस्करण",latestVersion:"नवीनतम संस्करण",upgrade:"अपग्रेड करें",message:{loadSettingsFailed:"सेटिंग्स लोड करने में विफल"},checkVersion:"संस्करण जांचें",latestVersionAvailable:"नवीनतम संस्करण मिला",latestVersionNotAvailable:"पहले से ही नवीनतम संस्करण है"},Kg={newVersionAvailable:"नया संस्करण उपलब्ध है",whatsNew:"नया क्या है",upgradeNow:"अभी अपग्रेड करें",downloading:"डाउनलोड हो रहा है...",remindAfterDownload:"डाउनलोड के बाद याद दिलाएं",newVersionReady:"नया संस्करण तैयार है",installNow:"अभी स्थापित करें",remindLater:"बाद में याद दिलाएं"},Gg={download:"डाउनलोड",online:"ऑनलाइन",convert:"कन्वर्ट",audioVideoMerger:"ऑडियो वीडियो मर्जर",joinTelegramGroup:"टेलीग्राम ग्रुप में शामिल हों",joinDiscordCommunity:"डिस्कॉर्ड समुदाय में शामिल हों"},qg={menu:{download:"लिंक डाउनलोड",network:"नेटवर्क स्निफिंग",format:"प्रारूप परिवर्तन",merge:"ऑडियो और वीडियो मर्ज"},loading:"लोड हो रहा है..."},Wg={cancel:"रद्द करें",ok:"ठीक है"},Yg={download:Ng,taskStatus:Og,taskActions:Mg,auth:jg,contextMenu:Ug,errors:$g,messages:Vg,dialogs:Hg,menu:zg,settings:Bg,update:Kg,mainMenu:Gg,application:qg,common:Wg},Qg={pasteLink:"Tempel Tautan",pasteLinkFromClipboard:"Tempel Tautan dari Papan Klip",playlistChannel:"Daftar Putar/Saluran",pastePlaylistChannelLink:"Tempel Tautan Daftar Putar/Saluran",download:"Unduh",resourceType:{video:"Video",audio:"Audio"},quality:"Kualitas",videoQuality:{best:"Terbaik"},audioQuality:{highest:"Tertinggi"},format:"Format",for:"Untuk",thumbnail:"Gambar Mini",subtitles:"Subtitle",audioTracks:"Trek Audio",allTracks:"Semua Trek",default:"Standar",none:"Tidak Ada",modal:{selectAll:"Pilih Semua",cancelAll:"Batalkan Semua",cancel:"Batal",download:"Unduh",needDownloadToSelect:"Silakan pilih setidaknya satu item untuk diunduh"},emptyState:{step1:"Langkah 1: Salin URL video",step2:"Langkah 2: Klik untuk menempelkan tautan dan mengunduh"},popconfirm:{downloadingDeleteTitle:"Apakah Anda yakin ingin menghapus tugas ini?",deleteText:"Hapus",openFileFailed:"Gagal membuka file, apakah Anda ingin menghapusnya?"}},Xg={retrievingInformation:"Mengambil informasi",downloading:"Sedang mengunduh",audioDownloading:"Sedang mengunduh audio",videoDownloading:"Sedang mengunduh video",subtitlesDownloading:"Sedang mengunduh subtitle",converting:"Sedang mengonversi",merging:"Sedang menggabungkan",downloadFailed:"Pengunduhan gagal",parseFailed:"Pemrosesan gagal",cancelled:"Dibatalkan",preparingToDownload:"Mempersiapkan pengunduhan"},Jg={retry:"Coba Lagi",delete:"Hapus",showInFinder:"Tampilkan di Finder",showInFolder:"Tampilkan di Folder",more:"Lainnya",logIn:"Masuk",timeLeft:"Waktu tersisa",speed:"Kecepatan",fileSize:"Ukuran berkas"},Zg={logIn:"Masuk",logOut:"Keluar",logInToX:"Masuk ke https://x.com",done:"Selesai",cancel:"Batal",cancelLogin:"Batalkan Masuk",loginTo:"Masuk ke"},ey={copyCaption:"Salin Keterangan",copyLinkAddress:"Salin Alamat Tautan",openInBrowser:"Buka di Peramban",remove:"Hapus",removeAll:"Hapus Semua"},ty={connectionTimeout:"Waktu koneksi habis, silakan periksa koneksi jaringan",unsupportedUrl:"URL belum didukung. Segera tersedia",needLoginToDownload:"Perlu masuk untuk mengunduh",fileNotFound:"Berkas tidak ditemukan",folderNotFound:"Folder tidak ditemukan",openFileLocationFailed:"Gagal membuka lokasi berkas",clipboardNotContainsValidUrl:"Papan klip tidak berisi URL yang valid",retryFailed:"Percobaan ulang gagal",checkVersionFailed:"Pemeriksaan versi gagal",notImplemented:"Belum diimplementasikan",parseError:"Kesalahan Penguraian",downloadError:"Kesalahan Unduhan, silakan periksa koneksi jaringan",mergeError:"Kesalahan Konversi"},ny={saveSuccess:"Penyimpanan berhasil",saveFailed:"Penyimpanan gagal",authSuccess:"Otorisasi berhasil",authFailed:"Otorisasi gagal",removeAuthSuccess:"Penghapusan otorisasi berhasil",removeAuthFailed:"Penghapusan otorisasi gagal",validUrlPrompt:"Silakan masukkan URL yang valid",websiteAlreadyInList:"Situs web ini sudah ada dalam daftar",defaultError:"Operasi gagal"},oy={removeAll:{removeAllItemsFromTheList:"Hapus semua item dari daftar?",deleteDownloadedFiles:"Hapus berkas yang telah diunduh",remove:"Hapus",cancel:"Batal"},fileDeleted:{fileHasBeenDeletedOrMoved:"Berkas telah dihapus atau dipindahkan. Hapus item ini?",remove:"Hapus",cancel:"Batal"},deleteDownloading:{fileIsDownloading:"Berkas sedang diunduh. Hapus?",delete:"Hapus",cancel:"Batal"}},ry={website:"Situs Web",settings:"Pengaturan"},ay={general:"Umum",saveTo:"Simpan ke",changeFolderBrowser:"Ubah Folder",language:"Bahasa",system:"Sistem",createSubdirectoriesForDownloadedPlaylistsAndChannels:"Buat subfolder untuk daftar putar dan saluran yang diunduh",numerateFilesInPlaylistsAndChannels:"Beri nomor pada berkas dalam daftar putar dan saluran",embedSubtitlesInVideoFile:"Sematkan subtitle dalam berkas video",authorization:"Otorisasi",logOut:"Keluar",logIn:"Masuk",delete:"Hapus",addUrl:"Tambah",enterTheWebsiteUrl:"Masukkan URL situs web",authorizationPanelTips:"Masuk ke situs web memungkinkan pengunduhan konten dengan batasan usia, konten keanggotaan yang telah Anda beli, dan konten pribadi lainnya.",proxy:"Proksi",proxyType:"Jenis Proksi",httpProxy:"Proksi HTTP",socks5Proxy:"Proksi SOCKS5",usingSystemProxy:"Menggunakan Proksi Sistem",notUsingProxy:"Tidak Menggunakan Proksi",host:"Host",port:"Port",proxyInfoMessage:{pleaseEnterProxyHost:"Silakan masukkan alamat host proksi",pleaseEnterValidProxyHost:"Silakan masukkan alamat host yang valid",pleaseEnterProxyPort:"Silakan masukkan port proksi",pleaseEnterValidProxyPort:"Silakan masukkan nomor port yang valid (1-65535)",optional:"Opsional"},login:"Nama Pengguna",password:"Kata Sandi",save:"Simpan",about:"Tentang",version:"Versi",latestVersion:"Versi Terbaru",upgrade:"Perbarui",message:{loadSettingsFailed:"Gagal memuat pengaturan"},checkVersion:"Periksa Versi",latestVersionAvailable:"Versi terbaru ditemukan",latestVersionNotAvailable:"Sudah menggunakan versi terbaru"},iy={newVersionAvailable:"Versi baru tersedia",whatsNew:"Apa yang Baru",upgradeNow:"Perbarui Sekarang",downloading:"Sedang mengunduh...",remindAfterDownload:"Ingatkan setelah pengunduhan",newVersionReady:"Versi baru telah siap",installNow:"Pasang Sekarang",remindLater:"Ingatkan Nanti"},sy={download:"Unduh",online:"Daring",convert:"Konversi",audioVideoMerger:"Penggabungan Audio Video",joinTelegramGroup:"Gabung ke Grup Telegram",joinDiscordCommunity:"Gabung ke Komunitas Discord"},ly={menu:{download:"Unduh Tautan",network:"Pemantauan Jaringan",format:"Konversi Format",merge:"Penggabungan Audio dan Video"},loading:"Memuat..."},cy={cancel:"Batal",ok:"OK"},dy={download:Qg,taskStatus:Xg,taskActions:Jg,auth:Zg,contextMenu:ey,errors:ty,messages:ny,dialogs:oy,menu:ry,settings:ay,update:iy,mainMenu:sy,application:ly,common:cy},uy={pasteLink:"Incolla Link",pasteLinkFromClipboard:"Incolla Link dagli Appunti",playlistChannel:"Playlist/Canale",pastePlaylistChannelLink:"Incolla Link Playlist/Canale",download:"Scarica",resourceType:{video:"Video",audio:"Audio"},quality:"Qualità",videoQuality:{best:"Migliore"},audioQuality:{highest:"Massima"},format:"Formato",for:"Per",thumbnail:"Miniatura",subtitles:"Sottotitoli",audioTracks:"Tracce Audio",allTracks:"Tutte le Tracce",default:"Predefinito",none:"Nessuno",modal:{selectAll:"Seleziona Tutto",cancelAll:"Annulla Tutto",cancel:"Annulla",download:"Scarica",needDownloadToSelect:"Seleziona almeno un elemento da scaricare"},popconfirm:{downloadingDeleteTitle:"Sei sicuro di voler eliminare questa attività?",deleteText:"Elimina",openFileFailed:"Apertura file non riuscita, vuoi eliminarlo?"}},py={retrievingInformation:"Recupero informazioni in corso",downloading:"Download in corso",audioDownloading:"Download audio in corso",videoDownloading:"Download video in corso",subtitlesDownloading:"Download sottotitoli in corso",converting:"Conversione in corso",merging:"Unione in corso",downloadFailed:"Download fallito",parseFailed:"Analisi fallita",cancelled:"Annullato",preparingToDownload:"Preparazione al download"},fy={retry:"Riprova",delete:"Elimina",showInFinder:"Mostra nel Finder",showInFolder:"Mostra nella Cartella",more:"Altro",logIn:"Accedi",timeLeft:"Tempo rimanente",speed:"Velocità",fileSize:"Dimensione del file"},my={logIn:"Accedi",logOut:"Esci",logInToX:"Accedi a https://x.com",done:"Completato",cancel:"Annulla",cancelLogin:"Annulla Accesso",loginTo:"Accedi a"},hy={copyCaption:"Copia Didascalia",copyLinkAddress:"Copia Indirizzo del Link",openInBrowser:"Apri nel Browser",remove:"Rimuovi",removeAll:"Rimuovi Tutto"},gy={connectionTimeout:"Connessione scaduta, verifica la connessione di rete",unsupportedUrl:"URL non ancora supportato. Disponibile a breve",needLoginToDownload:"È necessario accedere per scaricare",fileNotFound:"File non trovato",folderNotFound:"Cartella non trovata",openFileLocationFailed:"Apertura della posizione del file fallita",clipboardNotContainsValidUrl:"Gli appunti non contengono un URL valido",retryFailed:"Tentativo fallito",checkVersionFailed:"Verifica della versione fallita",notImplemented:"Non ancora implementato",parseError:"Errore di analisi",downloadError:"Errore di download, controlla la connessione di rete",mergeError:"Errore di conversione"},yy={saveSuccess:"Salvataggio riuscito",saveFailed:"Salvataggio fallito",authSuccess:"Autorizzazione completata",authFailed:"Autorizzazione fallita",removeAuthSuccess:"Rimozione autorizzazione completata",removeAuthFailed:"Rimozione autorizzazione fallita",validUrlPrompt:"Inserisci un URL valido",websiteAlreadyInList:"Questo sito web è già presente nella lista",defaultError:"Operazione fallita"},vy={removeAll:{removeAllItemsFromTheList:"Rimuovere tutti gli elementi dalla lista?",deleteDownloadedFiles:"Eliminare i file scaricati",remove:"Rimuovi",cancel:"Annulla"},fileDeleted:{fileHasBeenDeletedOrMoved:"Il file è stato eliminato o spostato. Rimuovere questo elemento?",remove:"Rimuovi",cancel:"Annulla"},deleteDownloading:{fileIsDownloading:"Il file è in fase di download. Eliminarlo?",delete:"Elimina",cancel:"Annulla"}},wy={website:"Sito Web",settings:"Impostazioni"},by={general:"Generale",saveTo:"Salva in",changeFolderBrowser:"Cambia Cartella",language:"Lingua",system:"Sistema",createSubdirectoriesForDownloadedPlaylistsAndChannels:"Crea sottocartelle per playlist e canali scaricati",numerateFilesInPlaylistsAndChannels:"Numera i file nelle playlist e nei canali",embedSubtitlesInVideoFile:"Incorpora i sottotitoli nel file video",authorization:"Autorizzazione",logOut:"Esci",logIn:"Accedi",delete:"Elimina",addUrl:"Aggiungi",enterTheWebsiteUrl:"Inserisci l'URL del sito web",authorizationPanelTips:"Accedere al sito web consente di scaricare contenuti con restrizioni di età, contenuti premium acquistati e altri contenuti privati.",proxy:"Proxy",proxyType:"Tipo di Proxy",httpProxy:"Proxy HTTP",socks5Proxy:"Proxy SOCKS5",usingSystemProxy:"Utilizzo del Proxy di Sistema",notUsingProxy:"Non Utilizzare Proxy",host:"Host",port:"Porta",proxyInfoMessage:{pleaseEnterProxyHost:"Inserisci l'indirizzo dell'host del proxy",pleaseEnterValidProxyHost:"Inserisci un indirizzo host valido",pleaseEnterProxyPort:"Inserisci la porta del proxy",pleaseEnterValidProxyPort:"Inserisci un numero di porta valido (1-65535)",optional:"Opzionale"},login:"Nome Utente",password:"Password",save:"Salva",about:"Informazioni",version:"Versione",latestVersion:"Ultima Versione",upgrade:"Aggiorna",message:{loadSettingsFailed:"Caricamento delle impostazioni fallito"},checkVersion:"Controlla Versione",latestVersionAvailable:"Trovata l'ultima versione",latestVersionNotAvailable:"Già all'ultima versione"},Sy={newVersionAvailable:"Nuova versione disponibile",whatsNew:"Novità",upgradeNow:"Aggiorna Ora",downloading:"Download in corso...",remindAfterDownload:"Ricordamelo dopo il download",newVersionReady:"Nuova versione pronta",installNow:"Installa Ora",remindLater:"Ricordamelo Più Tardi"},Ey={download:"Scarica",online:"Online",convert:"Converti",audioVideoMerger:"Unione Audio Video",joinTelegramGroup:"Unisciti al Gruppo Telegram",joinDiscordCommunity:"Unisciti alla Comunità Discord"},xy={menu:{download:"Download Link",network:"Monitoraggio Rete",format:"Conversione Formato",merge:"Unione Audio e Video"},loading:"Caricamento in corso..."},Ty={step1:"Passo 1: Copia l'URL del video",step2:"Passo 2: Fai clic per incollare il link e scaricare"},ky={cancel:"Annulla",ok:"OK"},_y={download:uy,taskStatus:py,taskActions:fy,auth:my,contextMenu:hy,errors:gy,messages:yy,dialogs:vy,menu:wy,settings:by,update:Sy,mainMenu:Ey,application:xy,emptyState:Ty,common:ky},Ay={pasteLink:"リンクを貼り付け",pasteLinkFromClipboard:"クリップボードからリンクを貼り付け",playlistChannel:"プレイリスト/チャンネル",pastePlaylistChannelLink:"プレイリスト/チャンネルのリンクを貼り付け",download:"ダウンロード",resourceType:{video:"動画",audio:"音声"},quality:"品質",videoQuality:{best:"最高"},audioQuality:{highest:"最高"},format:"フォーマット",for:"対象",thumbnail:"サムネイル",subtitles:"字幕",audioTracks:"音声トラック",allTracks:"すべてのトラック",default:"デフォルト",none:"なし",modal:{selectAll:"すべて選択",cancelAll:"すべてキャンセル",cancel:"キャンセル",download:"ダウンロード",needDownloadToSelect:"ダウンロードするには少なくとも1つの項目を選択してください"},emptyState:{step1:"ステップ1：動画のURLをコピーする",step2:"ステップ2：リンクを貼り付けてダウンロードするにはクリック"},popconfirm:{downloadingDeleteTitle:"このタスクを削除してもよろしいですか？",deleteText:"削除",openFileFailed:"ファイルを開けませんでした。削除しますか？"}},Cy={retrievingInformation:"情報取得中",downloading:"ダウンロード中",audioDownloading:"音声ダウンロード中",videoDownloading:"動画ダウンロード中",subtitlesDownloading:"字幕ダウンロード中",converting:"変換中",merging:"結合中",downloadFailed:"ダウンロード失敗",parseFailed:"解析失敗",cancelled:"キャンセル済み",preparingToDownload:"ダウンロード準備中"},Iy={retry:"再試行",delete:"削除",showInFinder:"Finderで表示",showInFolder:"フォルダで表示",more:"詳細",logIn:"ログイン",timeLeft:"残り時間",speed:"速度",fileSize:"ファイルサイズ"},Py={logIn:"ログイン",logOut:"ログアウト",logInToX:"https://x.com にログイン",done:"完了",cancel:"キャンセル",cancelLogin:"ログインをキャンセル",loginTo:"ログイン先"},Dy={copyCaption:"キャプションをコピー",copyLinkAddress:"リンクアドレスをコピー",openInBrowser:"ブラウザでリンクを開く",remove:"削除",removeAll:"すべて削除"},Ly={connectionTimeout:"接続がタイムアウトしました。ネットワーク接続を確認してください",unsupportedUrl:"このURLはまだサポートされていません。近日対応予定です",needLoginToDownload:"ダウンロードにはログインが必要です",fileNotFound:"ファイルが見つかりません",folderNotFound:"フォルダが見つかりません",openFileLocationFailed:"ファイルの場所を開けませんでした",clipboardNotContainsValidUrl:"クリップボードに有効なURLが含まれていません",retryFailed:"再試行に失敗しました",checkVersionFailed:"バージョンの確認に失敗しました",notImplemented:"まだ実装されていません",parseError:"解析エラー",downloadError:"ダウンロードエラー、ネットワーク接続をご確認ください",mergeError:"変換エラー"},Fy={saveSuccess:"保存に成功しました",saveFailed:"保存に失敗しました",authSuccess:"認証に成功しました",authFailed:"認証に失敗しました",removeAuthSuccess:"認証解除に成功しました",removeAuthFailed:"認証解除に失敗しました",validUrlPrompt:"有効なURLを入力してください",websiteAlreadyInList:"このウェブサイトはすでにリストに登録されています",defaultError:"操作に失敗しました"},Ry={removeAll:{removeAllItemsFromTheList:"リストからすべての項目を削除しますか？",deleteDownloadedFiles:"ダウンロード済みのファイルを削除",remove:"削除",cancel:"キャンセル"},fileDeleted:{fileHasBeenDeletedOrMoved:"ファイルが削除または移動されました。この項目を削除しますか？",remove:"削除",cancel:"キャンセル"},deleteDownloading:{fileIsDownloading:"ファイルをダウンロード中です。削除しますか？",delete:"削除",cancel:"キャンセル"}},Ny={website:"ウェブサイト",settings:"設定"},Oy={general:"一般",saveTo:"保存先",changeFolderBrowser:"フォルダを変更",language:"言語",system:"システム",createSubdirectoriesForDownloadedPlaylistsAndChannels:"ダウンロードしたプレイリストおよびチャンネル用にサブディレクトリを作成",numerateFilesInPlaylistsAndChannels:"プレイリストおよびチャンネルのファイルに番号を付ける",embedSubtitlesInVideoFile:"動画ファイルに字幕を埋め込む",authorization:"認証",logOut:"ログアウト",logIn:"ログイン",delete:"削除",addUrl:"追加",enterTheWebsiteUrl:"ウェブサイトのURLを入力",authorizationPanelTips:"ウェブサイトにログインすると、年齢制限付きコンテンツ、購入済みのメンバーシップコンテンツ、その他のプライベートコンテンツをダウンロードできます。",proxy:"プロキシ",proxyType:"プロキシの種類",httpProxy:"HTTPプロキシ",socks5Proxy:"SOCKS5プロキシ",usingSystemProxy:"システムプロキシを使用",notUsingProxy:"プロキシを使用しない",host:"ホスト",port:"ポート",proxyInfoMessage:{pleaseEnterProxyHost:"プロキシホストアドレスを入力してください",pleaseEnterValidProxyHost:"有効なホストアドレスを入力してください",pleaseEnterProxyPort:"プロキシポートを入力してください",pleaseEnterValidProxyPort:"有効なポート番号（1〜65535）を入力してください",optional:"オプション"},login:"ログイン",password:"パスワード",save:"保存",about:"アプリについて",version:"バージョン",latestVersion:"最新バージョン",upgrade:"アップグレード",message:{loadSettingsFailed:"設定の読み込みに失敗しました"},checkVersion:"バージョンを確認",latestVersionAvailable:"最新バージョンが見つかりました",latestVersionNotAvailable:"すでに最新バージョンです"},My={newVersionAvailable:"新しいバージョンが利用可能です",whatsNew:"新機能",upgradeNow:"今すぐアップグレード",downloading:"ダウンロード中...",remindAfterDownload:"ダウンロード後に通知",newVersionReady:"新しいバージョンの準備が完了しました",installNow:"今すぐインストール",remindLater:"後で通知"},jy={download:"ダウンロード",online:"オンライン",convert:"変換",audioVideoMerger:"音声・動画の結合",joinTelegramGroup:"Telegramグループに参加",joinDiscordCommunity:"Discordコミュニティに参加"},Uy={menu:{download:"リンクダウンロード",network:"ネットワークスニッフィング",format:"フォーマット変換",merge:"音声・動画の結合"},loading:"読み込み中..."},$y={cancel:"キャンセル",ok:"OK"},Vy={download:Ay,taskStatus:Cy,taskActions:Iy,auth:Py,contextMenu:Dy,errors:Ly,messages:Fy,dialogs:Ry,menu:Ny,settings:Oy,update:My,mainMenu:jy,application:Uy,common:$y},Hy={pasteLink:"링크 붙여넣기",pasteLinkFromClipboard:"클립보드에서 링크 붙여넣기",playlistChannel:"재생목록/채널",pastePlaylistChannelLink:"재생목록/채널 링크 붙여넣기",download:"다운로드",resourceType:{video:"영상",audio:"음성"},quality:"화질",videoQuality:{best:"최고"},audioQuality:{highest:"최상"},format:"형식",for:"용",thumbnail:"썸네일",subtitles:"자막",audioTracks:"음성 트랙",allTracks:"모든 트랙",default:"기본",none:"없음",modal:{selectAll:"모두 선택",cancelAll:"모두 취소",cancel:"취소",download:"다운로드",needDownloadToSelect:"다운로드하려면 최소한 하나의 항목을 선택하세요"},emptyState:{step1:"1단계: 비디오 URL 복사",step2:"2단계: 링크를 붙여넣고 다운로드하려면 클릭"},popconfirm:{downloadingDeleteTitle:"이 작업을 삭제하시겠습니까?",deleteText:"삭제",openFileFailed:"파일 열기에 실패했습니다. 삭제하시겠습니까?"}},zy={retrievingInformation:"정보를 가져오는 중",downloading:"다운로드 중",audioDownloading:"음성 다운로드 중",videoDownloading:"영상 다운로드 중",subtitlesDownloading:"자막 다운로드 중",converting:"변환 중",merging:"병합 중",downloadFailed:"다운로드 실패",parseFailed:"분석 실패",cancelled:"취소됨",preparingToDownload:"다운로드 준비 중"},By={retry:"다시 시도",delete:"삭제",showInFinder:"Finder에서 보기",showInFolder:"폴더에서 보기",more:"더 보기",logIn:"로그인",timeLeft:"남은 시간",speed:"속도",fileSize:"파일 크기"},Ky={logIn:"로그인",logOut:"로그아웃",logInToX:"https://x.com에 로그인",done:"완료",cancel:"취소",cancelLogin:"로그인 취소",loginTo:"다음에 로그인"},Gy={copyCaption:"캡션 복사",copyLinkAddress:"링크 주소 복사",openInBrowser:"브라우저에서 열기",remove:"삭제",removeAll:"모두 삭제"},qy={connectionTimeout:"연결 시간이 초과되었습니다. 네트워크 연결을 확인하세요",unsupportedUrl:"아직 지원되지 않는 URL입니다. 곧 지원 예정입니다",needLoginToDownload:"다운로드하려면 로그인이 필요합니다",fileNotFound:"파일을 찾을 수 없습니다",folderNotFound:"폴더를 찾을 수 없습니다",openFileLocationFailed:"파일 위치를 열 수 없습니다",clipboardNotContainsValidUrl:"클립보드에 유효한 URL이 없습니다",retryFailed:"재시도 실패",checkVersionFailed:"버전 확인 실패",notImplemented:"아직 구현되지 않음",parseError:"구문 분석 오류",downloadError:"다운로드 오류, 네트워크 연결을 확인해 주세요",mergeError:"변환 오류"},Wy={saveSuccess:"저장 성공",saveFailed:"저장 실패",authSuccess:"인증 성공",authFailed:"인증 실패",removeAuthSuccess:"인증 삭제 성공",removeAuthFailed:"인증 삭제 실패",validUrlPrompt:"유효한 URL을 입력하세요",websiteAlreadyInList:"이 웹사이트는 이미 목록에 있습니다",defaultError:"예외"},Yy={removeAll:{removeAllItemsFromTheList:"목록에서 모든 항목을 삭제하시겠습니까?",deleteDownloadedFiles:"다운로드된 파일 삭제",remove:"삭제",cancel:"취소"},fileDeleted:{fileHasBeenDeletedOrMoved:"파일이 삭제되거나 이동되었습니다. 이 항목을 삭제하시겠습니까?",remove:"삭제",cancel:"취소"},deleteDownloading:{fileIsDownloading:"파일이 다운로드 중입니다. 삭제하시겠습니까?",delete:"삭제",cancel:"취소"}},Qy={website:"웹사이트",settings:"설정"},Xy={general:"일반",saveTo:"저장 위치",changeFolderBrowser:"폴더 변경",language:"언어",system:"시스템",createSubdirectoriesForDownloadedPlaylistsAndChannels:"다운로드된 재생목록 및 채널에 하위 폴더 생성",numerateFilesInPlaylistsAndChannels:"재생목록 및 채널 내 파일 번호 매기기",embedSubtitlesInVideoFile:"영상 파일에 자막 삽입",authorization:"인증",logOut:"로그아웃",logIn:"로그인",delete:"삭제",addUrl:"추가",enterTheWebsiteUrl:"웹사이트 URL 입력",authorizationPanelTips:"웹사이트에 로그인하면 연령 제한 콘텐츠, 구매한 멤버십 콘텐츠 및 기타 비공개 콘텐츠를 다운로드할 수 있습니다.",proxy:"프록시",proxyType:"프록시 유형",httpProxy:"HTTP 프록시",socks5Proxy:"SOCKS5 프록시",usingSystemProxy:"시스템 프록시 사용",notUsingProxy:"프록시 사용 안 함",host:"호스트",port:"포트",proxyInfoMessage:{pleaseEnterProxyHost:"프록시 호스트 주소를 입력하세요",pleaseEnterValidProxyHost:"유효한 호스트 주소를 입력하세요",pleaseEnterProxyPort:"프록시 포트를 입력하세요",pleaseEnterValidProxyPort:"유효한 포트 번호를 입력하세요 (1-65535)",optional:"선택 사항"},login:"아이디",password:"비밀번호",save:"저장",about:"정보",version:"버전",latestVersion:"최신 버전",upgrade:"업그레이드",message:{loadSettingsFailed:"설정 로드 실패"},checkVersion:"버전 확인",latestVersionAvailable:"최신 버전 발견",latestVersionNotAvailable:"이미 최신 버전입니다"},Jy={newVersionAvailable:"새 버전 사용 가능",whatsNew:"새로운 점",upgradeNow:"지금 업그레이드",downloading:"다운로드 중...",remindAfterDownload:"다운로드 후 알림",newVersionReady:"새 버전 준비 완료",installNow:"지금 설치",remindLater:"나중에 알림"},Zy={download:"다운로드",online:"온라인",convert:"변환",audioVideoMerger:"음성 및 영상 병합",joinTelegramGroup:"텔레그램 그룹 가입",joinDiscordCommunity:"디스코드 커뮤니티 가입"},e0={menu:{download:"링크 다운로드",network:"네트워크 스니핑",format:"형식 변환",merge:"음성 및 영상 병합"},loading:"로딩 중..."},t0={cancel:"취소",ok:"확인"},n0={download:Hy,taskStatus:zy,taskActions:By,auth:Ky,contextMenu:Gy,errors:qy,messages:Wy,dialogs:Yy,menu:Qy,settings:Xy,update:Jy,mainMenu:Zy,application:e0,common:t0},o0={pasteLink:"Colar Link",pasteLinkFromClipboard:"Colar Link da Área de Transferência",playlistChannel:"Playlist/Canal",pastePlaylistChannelLink:"Colar Link de Playlist/Canal",download:"Baixar",resourceType:{video:"Vídeo",audio:"Áudio"},quality:"Qualidade",videoQuality:{best:"Melhor"},audioQuality:{highest:"Mais Alta"},format:"Formato",for:"Para",thumbnail:"Miniatura",subtitles:"Legendas",audioTracks:"Faixas de Áudio",allTracks:"Todas as Faixas",default:"Padrão",none:"Nenhum",modal:{selectAll:"Selecionar Tudo",cancelAll:"Cancelar Tudo",cancel:"Cancelar",download:"Baixar",needDownloadToSelect:"Por favor, selecione pelo menos um item para baixar"},emptyState:{step1:"Passo 1: Copie a URL do vídeo",step2:"Passo 2: Clique para colar o link e fazer o download"},popconfirm:{downloadingDeleteTitle:"Tem certeza que deseja excluir esta tarefa?",deleteText:"Excluir",openFileFailed:"Falha ao abrir o arquivo, deseja excluí-lo?"}},r0={retrievingInformation:"Obtendo informações",downloading:"Baixando",audioDownloading:"Baixando áudio",videoDownloading:"Baixando vídeo",subtitlesDownloading:"Baixando legendas",converting:"Convertendo",merging:"Mesclando",downloadFailed:"Falha no download",parseFailed:"Falha na análise",cancelled:"Cancelado",preparingToDownload:"Preparando para baixar"},a0={retry:"Tentar Novamente",delete:"Excluir",showInFinder:"Mostrar no Finder",showInFolder:"Mostrar na Pasta",more:"Mais",logIn:"Entrar",timeLeft:"Tempo restante",speed:"Velocidade",fileSize:"Tamanho do arquivo"},i0={logIn:"Entrar",logOut:"Sair",logInToX:"Entrar em https://x.com",done:"Concluído",cancel:"Cancelar",cancelLogin:"Cancelar Login",loginTo:"Entrar em"},s0={copyCaption:"Copiar Legenda",copyLinkAddress:"Copiar Endereço do Link",openInBrowser:"Abrir no Navegador",remove:"Remover",removeAll:"Remover Tudo"},l0={connectionTimeout:"Tempo de conexão esgotado, por favor, verifique sua conexão de rede",unsupportedUrl:"URL ainda não suportado. Disponível em breve",needLoginToDownload:"É necessário fazer login para baixar",fileNotFound:"Arquivo não encontrado",folderNotFound:"Pasta não encontrada",openFileLocationFailed:"Falha ao abrir a localização do arquivo",clipboardNotContainsValidUrl:"A área de transferência não contém uma URL válida",retryFailed:"Tentativa de repetição falhou",checkVersionFailed:"Falha ao verificar a versão",notImplemented:"Ainda não implementado",parseError:"Erro de análise",downloadError:"Erro de download, verifique a conexão de rede",mergeError:"Erro de conversão"},c0={saveSuccess:"Salvo com sucesso",saveFailed:"Falha ao salvar",authSuccess:"Autorização bem-sucedida",authFailed:"Falha na autorização",removeAuthSuccess:"Remoção de autorização bem-sucedida",removeAuthFailed:"Falha na remoção da autorização",validUrlPrompt:"Por favor, insira uma URL válida",websiteAlreadyInList:"Este site já está na lista",defaultError:"Operação falhou"},d0={removeAll:{removeAllItemsFromTheList:"Remover todos os itens da lista?",deleteDownloadedFiles:"Excluir arquivos baixados",remove:"Remover",cancel:"Cancelar"},fileDeleted:{fileHasBeenDeletedOrMoved:"O arquivo foi excluído ou movido. Remover este item?",remove:"Remover",cancel:"Cancelar"},deleteDownloading:{fileIsDownloading:"O arquivo está sendo baixado. Excluir?",delete:"Excluir",cancel:"Cancelar"}},u0={website:"Site",settings:"Configurações"},p0={general:"Geral",saveTo:"Salvar em",changeFolderBrowser:"Alterar Pasta",language:"Idioma",system:"Sistema",createSubdirectoriesForDownloadedPlaylistsAndChannels:"Criar subpastas para playlists e canais baixados",numerateFilesInPlaylistsAndChannels:"Numerar arquivos em playlists e canais",embedSubtitlesInVideoFile:"Incorporar legendas no arquivo de vídeo",authorization:"Autorização",logOut:"Sair",logIn:"Entrar",delete:"Excluir",addUrl:"Adicionar",enterTheWebsiteUrl:"Digite a URL do site",authorizationPanelTips:"Fazer login no site permite baixar conteúdos com restrição de idade, conteúdos de assinatura que você adquiriu e outros conteúdos privados.",proxy:"Proxy",proxyType:"Tipo de Proxy",httpProxy:"Proxy HTTP",socks5Proxy:"Proxy SOCKS5",usingSystemProxy:"Usando Proxy do Sistema",notUsingProxy:"Não Usando Proxy",host:"Host",port:"Porta",proxyInfoMessage:{pleaseEnterProxyHost:"Por favor, insira o endereço do host do proxy",pleaseEnterValidProxyHost:"Por favor, insira um endereço de host válido",pleaseEnterProxyPort:"Por favor, insira a porta do proxy",pleaseEnterValidProxyPort:"Por favor, insira um número de porta válido (1-65535)",optional:"Opcional"},login:"Usuário",password:"Senha",save:"Salvar",about:"Sobre",version:"Versão",latestVersion:"Última Versão",upgrade:"Atualizar",message:{loadSettingsFailed:"Falha ao carregar configurações"},checkVersion:"Verificar Versão",latestVersionAvailable:"Nova versão encontrada",latestVersionNotAvailable:"Já está na versão mais recente"},f0={newVersionAvailable:"Nova versão disponível",whatsNew:"Novidades",upgradeNow:"Atualizar Agora",downloading:"Baixando...",remindAfterDownload:"Lembrar após o download",newVersionReady:"Nova versão pronta",installNow:"Instalar Agora",remindLater:"Lembrar Mais Tarde"},m0={download:"Baixar",online:"Online",convert:"Converter",audioVideoMerger:"Mesclador de Áudio e Vídeo",joinTelegramGroup:"Entrar no Grupo do Telegram",joinDiscordCommunity:"Entrar na Comunidade do Discord"},h0={menu:{download:"Download de Link",network:"Monitoramento de Rede",format:"Conversão de Formato",merge:"Mesclagem de Áudio e Vídeo"},loading:"Carregando..."},g0={cancel:"Cancelar",ok:"OK"},y0={download:o0,taskStatus:r0,taskActions:a0,auth:i0,contextMenu:s0,errors:l0,messages:c0,dialogs:d0,menu:u0,settings:p0,update:f0,mainMenu:m0,application:h0,common:g0},v0={pasteLink:"Вставить ссылку",pasteLinkFromClipboard:"Вставить ссылку из буфера обмена",playlistChannel:"Плейлист/Канал",pastePlaylistChannelLink:"Вставить ссылку на плейлист/канал",download:"Скачать",resourceType:{video:"Видео",audio:"Аудио"},quality:"Качество",videoQuality:{best:"Наилучшее"},audioQuality:{highest:"Наивысшее"},format:"Формат",for:"Для",thumbnail:"Миниатюра",subtitles:"Субтитры",audioTracks:"Аудиодорожки",allTracks:"Все дорожки",default:"По умолчанию",none:"Нет",modal:{selectAll:"Tümünü Seç",cancelAll:"Hepsini İptal Et",cancel:"İptal",download:"İndir",needDownloadToSelect:"Lütfen indirme için en az bir öğe seçin"},emptyState:{step1:"Шаг 1: Скопируйте URL видео",step2:"Шаг 2: Нажмите, чтобы вставить ссылку и скачать"},popconfirm:{downloadingDeleteTitle:"Вы уверены, что хотите удалить эту задачу?",deleteText:"Удалить",openFileFailed:"Не удалось открыть файл, хотите удалить его?"}},w0={retrievingInformation:"Получение информации",downloading:"Загрузка",audioDownloading:"Загрузка аудио",videoDownloading:"Загрузка видео",subtitlesDownloading:"Загрузка субтитров",converting:"Конвертация",merging:"Объединение",downloadFailed:"Ошибка загрузки",parseFailed:"Ошибка обработки",cancelled:"Отменено",preparingToDownload:"Подготовка к загрузке"},b0={retry:"Повторить",delete:"Удалить",showInFinder:"Показать в Finder",showInFolder:"Показать в папке",more:"Ещё",logIn:"Войти",timeLeft:"Осталось времени",speed:"Скорость",fileSize:"Размер файла"},S0={logIn:"Войти",logOut:"Выйти",logInToX:"Войти на https://x.com",done:"Готово",cancel:"Отмена",cancelLogin:"Отменить вход",loginTo:"Войти в"},E0={copyCaption:"Копировать подпись",copyLinkAddress:"Копировать адрес ссылки",openInBrowser:"Открыть в браузере",remove:"Удалить",removeAll:"Удалить все"},x0={connectionTimeout:"Превышено время ожидания подключения, проверьте подключение к сети",unsupportedUrl:"URL пока не поддерживается. Скоро будет доступно",needLoginToDownload:"Необходимо войти для загрузки",fileNotFound:"Файл не найден",folderNotFound:"Папка не найдена",openFileLocationFailed:"Не удалось открыть расположение файла",clipboardNotContainsValidUrl:"В буфере обмена нет действительного URL",retryFailed:"Повторная попытка не удалась",checkVersionFailed:"Sürüm Kontrol Edilemedi",notImplemented:"Henüz uygulanmadı",parseError:"Ошибка разбора",downloadError:"Ошибка загрузки, проверьте подключение к сети",mergeError:"Ошибка конвертации"},T0={saveSuccess:"Kaydetme Başarılı",saveFailed:"Kaydetme Başarısız",authSuccess:"Авторизация успешна",authFailed:"Ошибка авторизации",removeAuthSuccess:"Авторизация успешно удалена",removeAuthFailed:"Ошибка удаления авторизации",validUrlPrompt:"Пожалуйста, введите действительный URL",websiteAlreadyInList:"Этот сайт уже в списке",defaultError:"Islem Basarisiz"},k0={removeAll:{removeAllItemsFromTheList:"Удалить все элементы из списка?",deleteDownloadedFiles:"Удалить загруженные файлы",remove:"Удалить",cancel:"Отмена"},fileDeleted:{fileHasBeenDeletedOrMoved:"Файл был удален или перемещен. Удалить этот элемент?",remove:"Удалить",cancel:"Отмена"},deleteDownloading:{fileIsDownloading:"Файл загружается. Удалить его?",delete:"Удалить",cancel:"Отмена"}},_0={website:"Веб-сайт",settings:"Настройки"},A0={general:"Общие",saveTo:"Сохранить в",changeFolderBrowser:"Изменить папку",language:"Язык",system:"Система",createSubdirectoriesForDownloadedPlaylistsAndChannels:"Создавать подпапки для загруженных плейлистов и каналов",numerateFilesInPlaylistsAndChannels:"Нумеровать файлы в плейлистах и каналах",embedSubtitlesInVideoFile:"Встраивать субтитры в видеофайл",authorization:"Авторизация",logOut:"Выйти",logIn:"Войти",delete:"Удалить",addUrl:"Добавить",enterTheWebsiteUrl:"Введите URL сайта",authorizationPanelTips:"Вход на сайт позволяет загружать контент с возрастным ограничением, купленный премиум-контент и другой приватный контент.",proxy:"Прокси",proxyType:"Тип прокси",httpProxy:"HTTP прокси",socks5Proxy:"SOCKS5 прокси",usingSystemProxy:"Использовать системный прокси",notUsingProxy:"Не использовать прокси",host:"Хост",port:"Порт",proxyInfoMessage:{pleaseEnterProxyHost:"Пожалуйста, введите адрес прокси-хоста",pleaseEnterValidProxyHost:"Lütfen geçerli bir sunucu adresi girin",pleaseEnterProxyPort:"Пожалуйста, введите порт прокси",pleaseEnterValidProxyPort:"Пожалуйста, введите действительный номер порта (1-65535)",optional:"Необязательно"},login:"Логин",password:"Пароль",save:"Сохранить",about:"О программе",version:"Версия",latestVersion:"Последняя версия",upgrade:"Обновить",message:{loadSettingsFailed:"Ошибка загрузки настроек"},checkVersion:"Sürümü Kontrol Et",latestVersionAvailable:"En son sürüm bulundu",latestVersionNotAvailable:"Zaten en son sürüm"},C0={newVersionAvailable:"Доступна новая версия",whatsNew:"Что нового",upgradeNow:"Обновить сейчас",downloading:"Загрузка...",remindAfterDownload:"Напомнить после загрузки",newVersionReady:"Новая версия готова",installNow:"Установить сейчас",remindLater:"Напомнить позже"},I0={download:"Загрузить",online:"Онлайн",convert:"Конвертировать",audioVideoMerger:"Объединение аудио и видео",joinTelegramGroup:"Присоединиться к группе Telegram",joinDiscordCommunity:"Присоединиться к сообществу Discord"},P0={menu:{download:"İndirme Bağlantısı",network:"Ağ İzleme",format:"Biçim Dönüştürme",merge:"Ses ve Video Birleştirme"},loading:"yükleniyor..."},D0={cancel:"Отмена",ok:"ОК"},L0={download:v0,taskStatus:w0,taskActions:b0,auth:S0,contextMenu:E0,errors:x0,messages:T0,dialogs:k0,menu:_0,settings:A0,update:C0,mainMenu:I0,application:P0,common:D0},F0={pasteLink:"Bağlantı Yapıştır",pasteLinkFromClipboard:"Panodan Bağlantı Yapıştır",playlistChannel:"Çalma Listesi/Kanal",pastePlaylistChannelLink:"Çalma Listesi/Kanal Bağlantısı Yapıştır",download:"İndir",resourceType:{video:"Video",audio:"Ses"},quality:"Kalite",videoQuality:{best:"En İyi"},audioQuality:{highest:"En Yüksek"},format:"Format",for:"İçin",thumbnail:"Küçük Resim",subtitles:"Altyazılar",audioTracks:"Ses Parçaları",allTracks:"Tüm Parçalar",default:"Varsayılan",none:"Hiçbiri",modal:{selectAll:"Tümünü Seç",cancelAll:"Tümünü İptal Et",cancel:"İptal",download:"İndir",needDownloadToSelect:"Lütfen indirme için en az bir öğeyi seçin"},emptyState:{step1:"Adım 1: Video URL'sini kopyalayın",step2:"Adım 2: Bağlantıyı yapıştırmak ve indirmek için tıklayın"},popconfirm:{downloadingDeleteTitle:"Bu görevi silmek istediğinizden emin misiniz?",deleteText:"Sil",openFileFailed:"Dosya açılamadı, silmek ister misiniz?"}},R0={retrievingInformation:"Bilgi alınıyor",downloading:"İndiriliyor",audioDownloading:"Ses indiriliyor",videoDownloading:"Video indiriliyor",subtitlesDownloading:"Altyazılar indiriliyor",converting:"Dönüştürülüyor",merging:"Birleştiriliyor",downloadFailed:"İndirme başarısız",parseFailed:"Ayrıştırma başarısız",cancelled:"İptal edildi",preparingToDownload:"İndirmeye hazırlanıyor"},N0={retry:"Tekrar Dene",delete:"Sil",showInFinder:"Finder'da Göster",showInFolder:"Klasörde Göster",more:"Daha Fazla",logIn:"Giriş Yap",timeLeft:"Kalan süre",speed:"Hız",fileSize:"Dosya boyutu"},O0={logIn:"Giriş Yap",logOut:"Çıkış Yap",logInToX:"https://x.com'a Giriş Yap",done:"Tamam",cancel:"İptal",cancelLogin:"Girişi İptal Et",loginTo:"Giriş Yap"},M0={copyCaption:"Başlığı Kopyala",copyLinkAddress:"Bağlantı Adresini Kopyala",openInBrowser:"Tarayıcıda Aç",remove:"Kaldır",removeAll:"Tümünü Kaldır"},j0={connectionTimeout:"Bağlantı zaman aşımı, lütfen ağ bağlantınızı kontrol edin",unsupportedUrl:"URL henüz desteklenmiyor. Yakında",needLoginToDownload:"İndirmek için giriş yapmanız gerekiyor",fileNotFound:"Dosya bulunamadı",folderNotFound:"Klasör bulunamadı",openFileLocationFailed:"Dosya konumu açılamadı",clipboardNotContainsValidUrl:"Panoda geçerli URL yok",retryFailed:"Yeniden deneme başarısız",checkVersionFailed:"Sürüm Kontrol Edilemedi",notImplemented:"Henüz uygulanmadı",parseError:"Ayrıştırma Hatası",downloadError:"İndirme Hatası, lütfen ağ bağlantınızı kontrol edin",mergeError:"Dönüştürme Hatası"},U0={saveSuccess:"Kaydetme Başarılı",saveFailed:"Kaydetme Başarısız",authSuccess:"Yetkilendirme başarılı",authFailed:"Yetkilendirme başarısız",removeAuthSuccess:"Yetkilendirme kaldırma başarılı",removeAuthFailed:"Yetkilendirme kaldırma başarısız",validUrlPrompt:"Lütfen geçerli bir URL girin",websiteAlreadyInList:"Bu web sitesi zaten listede",defaultError:"Islem Basarisiz"},$0={removeAll:{removeAllItemsFromTheList:"Listeden tüm öğeleri kaldır?",deleteDownloadedFiles:"İndirilen dosyaları sil",remove:"Kaldır",cancel:"İptal"},fileDeleted:{fileHasBeenDeletedOrMoved:"Dosya silindi veya taşındı. Bu öğeyi kaldır?",remove:"Kaldır",cancel:"İptal"},deleteDownloading:{fileIsDownloading:"Dosya indiriliyor. Silinsin mi?",delete:"Sil",cancel:"İptal"}},V0={website:"Web Sitesi",settings:"Ayarlar"},H0={general:"Genel",saveTo:"Kaydet",changeFolderBrowser:"Klasör Değiştir",language:"Dil",system:"Sistem",createSubdirectoriesForDownloadedPlaylistsAndChannels:"İndirilen çalma listeleri ve kanallar için alt dizinler oluştur",numerateFilesInPlaylistsAndChannels:"Çalma listeleri ve kanallardaki dosyaları numaralandır",embedSubtitlesInVideoFile:"Altyazıları video dosyasına göm",authorization:"Yetkilendirme",logOut:"Çıkış Yap",logIn:"Giriş Yap",delete:"Sil",addUrl:"Ekle",enterTheWebsiteUrl:"Web sitesi URL'sini girin",authorizationPanelTips:"Web sitesine giriş yapmak, yaş kısıtlamalı içeriği, satın aldığınız üyelik içeriğini ve diğer özel içeriği indirmenize olanak tanır.",proxy:"Proxy",proxyType:"Proxy Türü",httpProxy:"HTTP Proxy",socks5Proxy:"SOCKS5 Proxy",usingSystemProxy:"Sistem Proxy'sini Kullan",notUsingProxy:"Proxy Kullanma",host:"Sunucu",port:"Port",proxyInfoMessage:{pleaseEnterProxyHost:"Lütfen proxy sunucu adresini girin",pleaseEnterValidProxyHost:"Lütfen geçerli bir host adresi girin",pleaseEnterProxyPort:"Lütfen proxy portunu girin",pleaseEnterValidProxyPort:"Lütfen geçerli bir port numarası girin (1-65535)",optional:"İsteğe bağlı"},login:"Kullanıcı Adı",password:"Şifre",save:"Kaydet",about:"Hakkında",version:"Sürüm",latestVersion:"En Son Sürüm",upgrade:"Güncelle",message:{loadSettingsFailed:"Ayarlar yüklenemedi"},checkVersion:"Sürümü Kontrol Et",latestVersionAvailable:"Yeni sürüm mevcuttur",latestVersionNotAvailable:"Zaten en son sürüm"},z0={newVersionAvailable:"Yeni sürüm mevcut",whatsNew:"Yenilikler",upgradeNow:"Şimdi Güncelle",downloading:"İndiriliyor...",remindAfterDownload:"İndirdikten Sonra Hatırlat",newVersionReady:"Yeni sürüm hazır",installNow:"Şimdi Yükle",remindLater:"Daha Sonra Hatırlat"},B0={download:"İndir",online:"Çevrimiçi",convert:"Dönüştür",audioVideoMerger:"Ses Video Birleştirici",joinTelegramGroup:"Telegram Grubuna Katıl",joinDiscordCommunity:"Discord Topluluğuna Katıl"},K0={menu:{download:"Bağlantı İndir",network:"Ağ İzleme",format:"Biçim Dönüştürme",merge:"Ses ve Video Birleştirme"},loading:"yükleniyor..."},G0={cancel:"İptal",ok:"Tamam"},q0={download:F0,taskStatus:R0,taskActions:N0,auth:O0,contextMenu:M0,errors:j0,messages:U0,dialogs:$0,menu:V0,settings:H0,update:z0,mainMenu:B0,application:K0,common:G0},W0={pasteLink:"Dán liên kết",pasteLinkFromClipboard:"Dán liên kết từ Clipboard",playlistChannel:"Danh sách phát/Kênh",pastePlaylistChannelLink:"Dán liên kết Danh sách phát/Kênh",download:"Tải xuống",resourceType:{video:"Video",audio:"Âm thanh"},quality:"Chất lượng",videoQuality:{best:"Tốt nhất"},audioQuality:{highest:"Cao nhất"},format:"Định dạng",for:"Cho",thumbnail:"Hình thu nhỏ",subtitles:"Phụ đề",audioTracks:"Bản âm thanh",allTracks:"Tất cả bản",default:"Mặc định",none:"Không có",modal:{selectAll:"Chọn tất cả",cancelAll:"Hủy tất cả",cancel:"Hủy",download:"Tải xuống",needDownloadToSelect:"Vui lòng chọn ít nhất một mục để tải xuống"},emptyState:{step1:"Bước 1: Sao chép URL video",step2:"Bước 2: Nhấn để dán liên kết và tải xuống"},popconfirm:{downloadingDeleteTitle:"Bạn có chắc chắn muốn xóa nhiệm vụ này không?",deleteText:"Xóa",openFileFailed:"Mở tệp thất bại, bạn có muốn xóa không?"}},Y0={retrievingInformation:"Đang lấy thông tin",downloading:"Đang tải xuống",audioDownloading:"Đang tải xuống âm thanh",videoDownloading:"Đang tải xuống video",subtitlesDownloading:"Đang tải xuống phụ đề",converting:"Đang chuyển đổi",merging:"Đang kết hợp",downloadFailed:"Tải xuống thất bại",parseFailed:"Phân tích thất bại",cancelled:"Đã hủy",preparingToDownload:"Đang chuẩn bị tải xuống"},Q0={retry:"Thử lại",delete:"Xóa",showInFinder:"Hiển thị trong Finder",showInFolder:"Hiển thị trong Thư mục",more:"Thêm",logIn:"Đăng nhập",timeLeft:"Thời gian còn lại",speed:"Tốc độ",fileSize:"Kích thước tệp"},X0={logIn:"Đăng nhập",logOut:"Đăng xuất",logInToX:"Đăng nhập vào https://x.com",done:"Xong",cancel:"Hủy",cancelLogin:"Hủy đăng nhập",loginTo:"Đăng nhập vào"},J0={copyCaption:"Sao chép chú thích",copyLinkAddress:"Sao chép địa chỉ liên kết",openInBrowser:"Mở trong trình duyệt",remove:"Xóa",removeAll:"Xóa tất cả"},Z0={connectionTimeout:"Hết thời gian kết nối, vui lòng kiểm tra kết nối mạng",unsupportedUrl:"URL chưa được hỗ trợ. Sắp ra mắt",needLoginToDownload:"Cần đăng nhập để tải xuống",fileNotFound:"Không tìm thấy tệp",folderNotFound:"Không tìm thấy thư mục",openFileLocationFailed:"Không thể mở vị trí tệp",clipboardNotContainsValidUrl:"Clipboard không chứa URL hợp lệ",retryFailed:"Thử lại thất bại",checkVersionFailed:"Kiểm tra phiên bản thất bại",notImplemented:"Chưa được thực hiện",parseError:"Lỗi phân tích",downloadError:"Lỗi tải xuống, vui lòng kiểm tra kết nối mạng",mergeError:"Lỗi chuyển đổi"},e1={saveSuccess:"Lưu thành công",saveFailed:"Lưu thất bại",authSuccess:"Xác thực thành công",authFailed:"Xác thực thất bại",removeAuthSuccess:"Xóa xác thực thành công",removeAuthFailed:"Xóa xác thực thất bại",validUrlPrompt:"Vui lòng nhập URL hợp lệ",websiteAlreadyInList:"Trang web này đã có trong danh sách",defaultError:"Hoạt động thất bại"},t1={removeAll:{removeAllItemsFromTheList:"Xóa tất cả mục khỏi danh sách?",deleteDownloadedFiles:"Xóa các tệp đã tải xuống",remove:"Xóa",cancel:"Hủy"},fileDeleted:{fileHasBeenDeletedOrMoved:"Tệp đã bị xóa hoặc di chuyển. Xóa mục này?",remove:"Xóa",cancel:"Hủy"},deleteDownloading:{fileIsDownloading:"Tệp đang được tải xuống. Xóa?",delete:"Xóa",cancel:"Hủy"}},n1={website:"Trang web",settings:"Cài đặt"},o1={general:"Chung",saveTo:"Lưu vào",changeFolderBrowser:"Thay đổi thư mục",language:"Ngôn ngữ",system:"Hệ thống",createSubdirectoriesForDownloadedPlaylistsAndChannels:"Tạo thư mục con cho danh sách phát và kênh đã tải xuống",numerateFilesInPlaylistsAndChannels:"Đánh số tệp trong danh sách phát và kênh",embedSubtitlesInVideoFile:"Nhúng phụ đề vào tệp video",authorization:"Ủy quyền",logOut:"Đăng xuất",logIn:"Đăng nhập",delete:"Xóa",addUrl:"Thêm",enterTheWebsiteUrl:"Nhập URL trang web",authorizationPanelTips:"Đăng nhập vào trang web cho phép tải xuống nội dung giới hạn độ tuổi, nội dung thành viên bạn đã mua và nội dung riêng tư khác.",proxy:"Proxy",proxyType:"Loại proxy",httpProxy:"Proxy HTTP",socks5Proxy:"Proxy SOCKS5",usingSystemProxy:"Sử dụng Proxy hệ thống",notUsingProxy:"Không sử dụng Proxy",host:"Máy chủ",port:"Cổng",proxyInfoMessage:{pleaseEnterProxyHost:"Vui lòng nhập địa chỉ máy chủ proxy",pleaseEnterValidProxyHost:"Vui lòng nhập địa chỉ máy chủ hợp lệ",pleaseEnterProxyPort:"Vui lòng nhập cổng proxy",pleaseEnterValidProxyPort:"Vui lòng nhập số cổng hợp lệ (1-65535)",optional:"Tùy chọn"},login:"Đăng nhập",password:"Mật khẩu",save:"Lưu",about:"Giới thiệu",version:"Phiên bản",latestVersion:"Phiên bản mới nhất",upgrade:"Nâng cấp",message:{loadSettingsFailed:"Tải cài đặt thất bại"},checkVersion:"Kiểm tra phiên bản",latestVersionAvailable:"Đã tìm thấy phiên bản mới nhất",latestVersionNotAvailable:"Đã là phiên bản mới nhất"},r1={newVersionAvailable:"Có phiên bản mới",whatsNew:"Có gì mới",upgradeNow:"Nâng cấp ngay",downloading:"Đang tải xuống...",remindAfterDownload:"Nhắc sau khi tải xuống",newVersionReady:"Phiên bản mới đã sẵn sàng",installNow:"Cài đặt ngay",remindLater:"Nhắc sau"},a1={download:"Tải xuống",online:"Trực tuyến",convert:"Chuyển đổi",audioVideoMerger:"Trộn Âm thanh Video",joinTelegramGroup:"Tham gia Nhóm Telegram",joinDiscordCommunity:"Tham gia Cộng đồng Discord"},i1={menu:{download:"Tải xuống liên kết",network:"Giám sát mạng",format:"Chuyển đổi định dạng",merge:"Trộn âm thanh và video"},loading:"Đang tải..."},s1={cancel:"Hủy",ok:"OK"},l1={download:W0,taskStatus:Y0,taskActions:Q0,auth:X0,contextMenu:J0,errors:Z0,messages:e1,dialogs:t1,menu:n1,settings:o1,update:r1,mainMenu:a1,application:i1,common:s1},c1={pasteLink:"粘贴链接",pasteLinkFromClipboard:"从剪切板粘贴链接",playlistChannel:"播放列表/频道",pastePlaylistChannelLink:"粘贴播放列表/频道链接",download:"下载",resourceType:{video:"视频",audio:"音频"},quality:"质量",videoQuality:{best:"最优"},audioQuality:{highest:"最高"},format:"格式",for:"为",thumbnail:"封面",subtitles:"字幕",audioTracks:"音轨",allTracks:"所有轨道",default:"默认",none:"无",modal:{selectAll:"全选",cancelAll:"取消全选",cancel:"取消",download:"下载",needDownloadToSelect:"请选择至少一项以进行下载"},emptyState:{step1:"第一步：复制视频URL",step2:"第二步：点击粘贴链接即可下载"},popconfirm:{downloadingDeleteTitle:"文件正在下载，确定要删除吗？",deleteText:"删除",openFileFailed:"文件已被删除或移动，是否移除此记录"}},d1={retrievingInformation:"提取中",downloading:"下载中",audioDownloading:"音频下载中",videoDownloading:"视频下载中",subtitlesDownloading:"字幕下载中",converting:"转换中",merging:"合并中",downloadFailed:"下载失败",parseFailed:"解析失败",cancelled:"已取消",preparingToDownload:"准备下载"},u1={retry:"重试",delete:"删除",showInFinder:"在访达中显示",showInFolder:"在文件夹中显示",more:"更多",logIn:"登录",timeLeft:"剩余时间",speed:"速度",fileSize:"文件大小"},p1={logIn:"登录",logOut:"退出登录",logInToX:"登录到 https://x.com",done:"完成",cancel:"取消",cancelLogin:"取消登录",loginTo:"登录到"},f1={copyCaption:"复制文案",copyLinkAddress:"复制链接地址",openInBrowser:"在浏览器中打开链接",remove:"移除",removeAll:"移除全部"},m1={connectionTimeout:"连接超时，请检查网络连接",unsupportedUrl:"不支持的链接",needLoginToDownload:"需登录后下载",fileNotFound:"文件不存在",folderNotFound:"文件夹不存在",openFileLocationFailed:"打开文件夹失败",clipboardNotContainsValidUrl:"剪贴板不包含有效URL",retryFailed:"重试失败",checkVersionFailed:"检查版本失败",notImplemented:"暂未实现该功能",parseError:"解析失败",downloadError:"下载失败，请检查网络连接",mergeError:"转换失败"},h1={saveSuccess:"保存成功",saveFailed:"保存失败",authSuccess:"授权成功",authFailed:"授权失败",removeAuthSuccess:"移除授权成功",removeAuthFailed:"移除授权失败",validUrlPrompt:"请输入有效的URL",websiteAlreadyInList:"该网站已在列表中",defaultError:"操作失败"},g1={removeAll:{removeAllItemsFromTheList:"移除列表中的所有记录？",deleteDownloadedFiles:"删除下载的文件",remove:"移除",cancel:"取消"},fileDeleted:{fileHasBeenDeletedOrMoved:"文件已被删除或移动。移除这条记录？",remove:"移除",cancel:"取消"},deleteDownloading:{fileIsDownloading:"文件正在下载，要删除吗？",delete:"删除",cancel:"取消"}},y1={website:"官网",settings:"设置"},v1={general:"常规",saveTo:"下载位置",changeFolderBrowser:"更改文件夹",language:"语言",system:"跟随系统",createSubdirectoriesForDownloadedPlaylistsAndChannels:"以播放列表/频道名称创建子文件夹",numerateFilesInPlaylistsAndChannels:"给播放列表/频道中的文件添加序号",embedSubtitlesInVideoFile:"将字幕嵌入视频文件中",authorization:"授权",logOut:"退出登录",logIn:"登录",delete:"删除",addUrl:"添加网址",enterTheWebsiteUrl:"输入网址URL",authorizationPanelTips:"登录网站后可以下载年龄限制的内容、您购买的会员内容以及其他私人内容。",proxy:"代理",proxyType:"代理类型",httpProxy:"HTTP 代理",socks5Proxy:"SOCKS5 代理",usingSystemProxy:"使用系统代理",notUsingProxy:"不使用代理",host:"主机",port:"端口",proxyInfoMessage:{pleaseEnterProxyHost:"请输入代理主机地址",pleaseEnterValidProxyHost:"请输入有效主机地址",pleaseEnterProxyPort:"请输入代理端口",pleaseEnterValidProxyPort:"请输入有效的端口号(1-65535)",optional:"可选"},login:"用户名",password:"密码",save:"保存",about:"关于",version:"版本号",latestVersion:"最新版本",upgrade:"立即升级",message:{loadSettingsFailed:"加载设置失败"},checkVersion:"检查更新",latestVersionAvailable:"发现最新版本",latestVersionNotAvailable:"已经是最新版本"},w1={newVersionAvailable:"发现新版本",whatsNew:"更新内容",upgradeNow:"安装更新",downloading:"正在下载更新...",remindAfterDownload:"稍后提醒我",newVersionReady:"新版本已准备好",installNow:"立即安装",remindLater:"以后提醒我"},b1={download:"链接下载",online:"浏览器嗅探",convert:"格式转换",audioVideoMerger:"音视频合并",joinTelegramGroup:"加入Telegram交流群",joinDiscordCommunity:"加入Discord交流群"},S1={menu:{download:"链接下载",network:"网络嗅探",format:"格式转换",merge:"音视频合并"},loading:"加载中..."},E1={cancel:"取消",ok:"确定"},x1={download:c1,taskStatus:d1,taskActions:u1,auth:p1,contextMenu:f1,errors:m1,messages:h1,dialogs:g1,menu:y1,settings:v1,update:w1,mainMenu:b1,application:S1,common:E1},T1={pasteLink:"貼上連結",pasteLinkFromClipboard:"從剪貼簿貼上連結",playlistChannel:"播放清單/頻道",pastePlaylistChannelLink:"貼上播放清單/頻道連結",download:"下載",resourceType:{video:"影片",audio:"音訊"},quality:"品質",videoQuality:{best:"最佳"},audioQuality:{highest:"最高"},format:"格式",for:"為",thumbnail:"縮圖",subtitles:"字幕",audioTracks:"音軌",allTracks:"所有軌道",default:"預設",none:"無",modal:{selectAll:"全選",cancelAll:"取消全選",cancel:"取消",download:"下載",needDownloadToSelect:"请選擇至少一項以進行下載"},emptyState:{step1:"步驟 1：複製影片 URL",step2:"步驟 2：點擊以貼上連結並下載"},popconfirm:{downloadingDeleteTitle:"您確定要刪除此任務嗎？",deleteText:"刪除",openFileFailed:"開啟檔案失敗，您要刪除嗎？"}},k1={retrievingInformation:"擷取中",downloading:"下載中",audioDownloading:"音訊下載中",videoDownloading:"影片下載中",subtitlesDownloading:"字幕下載中",converting:"轉換中",merging:"合併中",downloadFailed:"下載失敗",parseFailed:"解析失敗",cancelled:"已取消",preparingToDownload:"準備下載"},_1={retry:"重試",delete:"刪除",showInFinder:"在訪達中顯示",showInFolder:"在資料夾中顯示",more:"更多",logIn:"登入",timeLeft:"剩餘時間",speed:"速度",fileSize:"檔案大小"},A1={logIn:"登入",logOut:"登出",logInToX:"登入到 https://x.com",done:"完成",cancel:"取消",cancelLogin:"取消登入",loginTo:"登入到"},C1={copyCaption:"複製說明文字",copyLinkAddress:"複製連結位址",openInBrowser:"在瀏覽器中開啟",remove:"移除",removeAll:"移除全部"},I1={connectionTimeout:"連線逾時，請檢查網路連線",unsupportedUrl:"不支援的連結，即將推出",needLoginToDownload:"需要登入才能下載",fileNotFound:"找不到檔案",folderNotFound:"找不到資料夾",openFileLocationFailed:"開啟檔案位置失敗",clipboardNotContainsValidUrl:"剪貼簿中沒有有效的URL",retryFailed:"重試失敗",checkVersionFailed:"檢查版本失敗",notImplemented:"暫未實現該功能",parseError:"解析失敗",downloadError:"下載失敗，請檢查網路連線",mergeError:"轉換失敗"},P1={saveSuccess:"儲存成功",saveFailed:"儲存失敗",authSuccess:"授權成功",authFailed:"授權失敗",removeAuthSuccess:"移除授權成功",removeAuthFailed:"移除授權失敗",validUrlPrompt:"請輸入有效的URL",websiteAlreadyInList:"該網站已在清單中",defaultError:"操作失敗"},D1={removeAll:{removeAllItemsFromTheList:"移除清單中的所有項目？",deleteDownloadedFiles:"刪除已下載的檔案",remove:"移除",cancel:"取消"},fileDeleted:{fileHasBeenDeletedOrMoved:"檔案已被刪除或移動。移除此項目？",remove:"移除",cancel:"取消"},deleteDownloading:{fileIsDownloading:"檔案正在下載中。要刪除嗎？",delete:"刪除",cancel:"取消"}},L1={website:"網站",settings:"設定"},F1={general:"一般",saveTo:"儲存至",changeFolderBrowser:"變更資料夾",language:"語言",system:"系統",createSubdirectoriesForDownloadedPlaylistsAndChannels:"為已下載的播放清單和頻道建立子目錄",numerateFilesInPlaylistsAndChannels:"為播放清單和頻道中的檔案編號",embedSubtitlesInVideoFile:"將字幕嵌入影片檔案中",authorization:"授權",logOut:"登出",logIn:"登入",delete:"刪除",addUrl:"新增",enterTheWebsiteUrl:"輸入網站URL",authorizationPanelTips:"登入網站後可以下載年齡限制內容、您購買的會員內容以及其他私人內容。",proxy:"代理",proxyType:"代理類型",httpProxy:"HTTP 代理",socks5Proxy:"SOCKS5 代理",usingSystemProxy:"使用系統代理",notUsingProxy:"不使用代理",host:"主機",port:"連接埠",proxyInfoMessage:{pleaseEnterProxyHost:"請輸入代理主機位址",pleaseEnterValidProxyHost:"請輸入有效的代理主機位址",pleaseEnterProxyPort:"請輸入代理連接埠",pleaseEnterValidProxyPort:"請輸入有效的連接埠號碼 (1-65535)",optional:"選填"},login:"登入",password:"密碼",save:"儲存",about:"關於",version:"版本",latestVersion:"最新版本",upgrade:"升級",message:{loadSettingsFailed:"載入設定失敗"},checkVersion:"檢查更新",latestVersionAvailable:"發現最新版本",latestVersionNotAvailable:"已經是最新版本"},R1={newVersionAvailable:"有新版本可用",whatsNew:"新功能",upgradeNow:"立即升級",downloading:"下載中...",remindAfterDownload:"下載後提醒我",newVersionReady:"新版本已準備就緒",installNow:"立即安裝",remindLater:"稍後提醒"},N1={download:"下載",online:"線上",convert:"轉換",audioVideoMerger:"音訊影片合併",joinTelegramGroup:"加入Telegram群組",joinDiscordCommunity:"加入Discord社群"},O1={menu:{download:"連結下載",network:"網路嗅探",format:"格式轉換",merge:"音頻影片合併"},loading:"加載中..."},M1={cancel:"取消",ok:"確定"},j1={download:T1,taskStatus:k1,taskActions:_1,auth:A1,contextMenu:C1,errors:I1,messages:P1,dialogs:D1,menu:L1,settings:F1,update:R1,mainMenu:N1,application:O1,common:M1},ui={"zh-Hans":"zh-Hans","zh-Hant":"zh-Hant",hi:"hi",es:"es",fr:"fr",ru:"ru",id:"id",bn:"bn",pt:"pt",de:"de",ja:"ja",ko:"ko",vi:"vi",tr:"tr",it:"it"};async function U1(e){if(e==="system"){const t=await window.electronAPI.getSystemLanguage();return ui[t]||"en"}return ui[e]||e}cr.use(Us).use($s).init({resources:{en:{translation:rg},"zh-Hans":{translation:x1},"zh-Hant":{translation:j1},hi:{translation:Yg},es:{translation:wg},fr:{translation:Rg},ru:{translation:L0},id:{translation:dy},bn:{translation:Ch},pt:{translation:y0},de:{translation:zh},ja:{translation:Vy},ko:{translation:n0},vi:{translation:l1},tr:{translation:q0},it:{translation:_y}},fallbackLng:"en",interpolation:{escapeValue:!1},detection:{order:["localStorage","navigator"],caches:["localStorage"]}});ns().then(async e=>{if(e.language){const t=await U1(e.language);cr.changeLanguage(t)}});dp({dsn:"https://<EMAIL>/4508499640909824",enabled:!0});pp.createRoot(document.getElementById("root")).render(d.jsx(Vs,{i18n:cr,children:d.jsx(js,{children:d.jsx(fh,{})})}));
