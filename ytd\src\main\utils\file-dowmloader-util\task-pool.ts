class TaskPool<T> {
  private queue: Array<() => Promise<T>> = [];
  private running = 0;
  private results: T[] = [];
  private aborted = false;
  private resolvePool: ((value: T[]) => void) | null = null;
  private rejectPool: ((reason: Error | unknown) => void) | null = null;
  private abortController: AbortController = new AbortController();
  private totalTasks = 0;
  private completedTasks = 0; // 添加已完成任务计数

  constructor(private readonly concurrency: number) {}

  async runAll(tasks: Array<() => Promise<T>>): Promise<T[]> {
    // 重置状态
    this.queue = [...tasks];
    this.results = [];
    this.running = 0;
    this.aborted = false;
    this.abortController = new AbortController();
    this.totalTasks = tasks.length;
    this.completedTasks = 0;

    return new Promise((resolve, reject) => {
      this.resolvePool = resolve;
      this.rejectPool = reject;

      // 启动初始的并发任务
      const startTasks = Math.min(this.concurrency, tasks.length);
      for (let i = 0; i < startTasks; i++) {
        this.run().catch((error) => {
          this.abort();
          reject(error);
        });
      }
    });
  }

  private async run(): Promise<void> {
    if (this.queue.length === 0 || this.aborted) {
      return;
    }

    this.running++;
    const task = this.queue.shift()!;
    const taskIndex = this.completedTasks;

    try {
      const result = await task();

      if (this.abortController.signal.aborted) {
        throw new Error('Task aborted');
      }

      this.results[taskIndex] = result; // 保持结果顺序
      this.completedTasks++;
      this.running--;

      // 如果队列中还有任务，继续执行
      if (this.queue.length > 0) {
        this.run();
      }

      // 检查是否所有任务都完成
      if (this.completedTasks === this.totalTasks) {
        if (this.resolvePool) {
          this.resolvePool(this.results);
        }
      }
    } catch (error) {
      // console.error(`任务 ${taskIndex + 1}/${this.totalTasks} 失败:`, error);
      this.abort();

      if (this.rejectPool) {
        this.rejectPool(error);
      }

      throw error;
    }
  }

  abort(): void {
    if (this.aborted) return;

    this.aborted = true;
    this.queue = [];
    this.abortController.abort();
  }

  async close(): Promise<void> {
    // 中止所有正在进行的任务
    this.abort();

    // 清理状态
    this.queue = [];
    this.results = [];
    this.running = 0;
    this.aborted = false;
    this.resolvePool = null;
    this.rejectPool = null;
    this.completedTasks = 0;
    this.totalTasks = 0;
  }
}

export default TaskPool;
