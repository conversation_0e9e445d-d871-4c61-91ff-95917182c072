import tailwindcss from '@tailwindcss/vite';
import react from '@vitejs/plugin-react';
import path from 'path';
import { defineConfig } from 'vite';
import electron from 'vite-plugin-electron/simple';
import svgr from 'vite-plugin-svgr';

// 自定义插件，用于手动处理public目录，排除二进制文件
function customPublicDirPlugin() {
  return {
    name: 'custom-public-dir',
    buildStart() {
      // 这个钩子在构建开始时运行
      console.log('排除二进制文件，不复制到dist目录');
    },
  };
}

export default defineConfig({
  plugins: [
    svgr(),
    tailwindcss(),
    react(),
    customPublicDirPlugin(),
    electron({
      main: {
        entry: 'src/main/main.ts',
        vite: {
          resolve: {
            alias: {
              '@main': path.resolve(__dirname, 'src/main'),
              '@common': path.resolve(__dirname, 'src/common'),
            },
          },
        },
      },
      preload: {
        input: path.join(__dirname, 'src/main/preload.ts'),
      },
    }),
  ],
  base: './',
  // 禁用自动复制public目录
  publicDir: false,
  build: {
    // outDir: '../dist/renderer',
    // assetsDir: 'assets',
    // emptyOutDir: true,
    rollupOptions: {
      output: {
        entryFileNames: 'js/[name]-[hash].js',
        chunkFileNames: 'js/[name]-[hash].js',

        assetFileNames: (assetInfo: { name?: string }) => {
          const info = assetInfo.name?.split('.') || [];
          let extType = info[info.length - 1] || 'assets';
          if (assetInfo.name) {
            if (
              /\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/i.test(assetInfo.name)
            ) {
              extType = 'media';
            } else if (
              /\.(png|jpe?g|gif|svg|ico|webp)(\?.*)?$/i.test(assetInfo.name)
            ) {
              extType = 'img';
            } else if (/\.(woff2?|eot|ttf|otf)(\?.*)?$/i.test(assetInfo.name)) {
              extType = 'fonts';
            }
          }
          return `${extType}/[name]-[hash][extname]`;
        },
      },
    },
    commonjsOptions: {
      transformMixedEsModules: true,
    },
  },
  server: {
    port: 5173,
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src/renderer'),
      '@common': path.resolve(__dirname, 'src/common'),
    },
    extensions: ['.js', '.jsx', '.ts', '.tsx'],
  },
  optimizeDeps: {
    include: ['react', 'react-dom'],
  },
});
