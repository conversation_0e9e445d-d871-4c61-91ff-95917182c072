import './index.css';

import * as Sentry from '@sentry/electron/renderer';
import ReactDOM from 'react-dom/client';
import { I18nextProvider } from 'react-i18next';
import { HashRouter } from 'react-router-dom';

import ApplicationLayout from '@/layouts/Application';
import { isDev } from '@/utils/environment';

import i18n from './utils/i18n';

// 修改 Sentry 初始化配置
Sentry.init({
  dsn: 'https://<EMAIL>/4508499640909824',
  // 开发环境禁用
  enabled: !isDev,
});

ReactDOM.createRoot(document.getElementById('root')!).render(
  <I18nextProvider i18n={i18n}>
    <HashRouter>
      <ApplicationLayout />
    </HashRouter>
  </I18nextProvider>,
);
