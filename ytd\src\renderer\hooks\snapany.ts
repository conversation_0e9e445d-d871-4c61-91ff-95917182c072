import { useLockFn } from 'ahooks';
import useS<PERSON> from 'swr';

import {
  checkForUpdate,
  getAppVersion,
  getSettings,
  saveSettings,
} from '@/service/render';
import { DownloadConfigStore } from '@/store/download';
import { mapDownloadConfigToSettings } from '@/utils/setting';

export function useSnapany() {
  const {
    data: version,
    mutate: mutateVersion,
    isLoading: isLoadingVersion,
  } = useSWR('snapanyVersion', getAppVersion);

  const {
    data: settings,
    mutate: mutateSettings,
    isLoading: isLoadingSettings,
  } = useSWR('getSettings', getSettings);

  const {
    data: updateInfo,
    mutate: mutateUpdateInfo,
    isLoading: isLoadingUpdate,
  } = useSWR('checkForUpdate', checkForUpdate);

  const patchSetting = useLockFn(async (partial: Partial<typeof settings>) => {
    if (!settings) {
      return;
    }

    const newSettings = {
      ...settings,
      ...partial,
    };
    await saveSettings(newSettings);
    mutateSettings();
  });

  const updateDownloadConfig = useLockFn(
    async (downloadConfig: DownloadConfigStore) => {
      if (!settings) {
        return;
      }
      const updatedSettings = mapDownloadConfigToSettings(
        downloadConfig,
        settings,
      );

      await saveSettings(updatedSettings);
      mutateSettings();
    },
  );

  return {
    version,
    mutateVersion,
    settings,
    mutateSettings,
    patchSetting,
    updateInfo,
    mutateUpdateInfo,
    isLoading: isLoadingVersion || isLoadingSettings || isLoadingUpdate,
    updateDownloadConfig,
  };
}
